#!/usr/bin/env python3
"""
Professional eSIM Workflow Console Application
Complete Reseller Workflow with TraveRoam + Stripe Integration

This script demonstrates the complete reseller workflow:
1. Add New User (Client)
2. Fetch Available eSIM Plans
3. Process Payment via Stripe
4. Provision eSIM via TraveRoam
5. Display QR Code and Send Email with Invoice
6. Save Data to Database

Features:
- Stripe payment processing with checkout sessions
- PDF invoice generation and email attachment
- Real-time email delivery with QR codes
- Comprehensive database logging
- Professional reseller markup support

Author: eSIM Management System
Version: 3.0.0
"""

import base64
import io
import json
import logging
import os
import re
import smtplib
import sys
import uuid
import webbrowser
from dataclasses import dataclass
from datetime import datetime, timedelta
from email.mime.application import MIMEApplication
from email.mime.image import MIMEImage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import qrcode
import requests
import stripe

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    print(
        "Warning: python-dotenv not installed. Make sure environment variables are set manually."
    )

# Django setup for database operations
import django
from django.conf import settings
from django.utils import timezone

# Add the project directory to Python path
project_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_dir)

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "esim_project.settings")
django.setup()

# Import Django models after setup
from accounts.models import User
from clients.models import Client
from esim_management.models import ESIM, ESIMPlan
from orders.models import Order
from payments.models import Payment
from resellers.models import Reseller

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("esim_workflow.log"),
        logging.StreamHandler(sys.stdout),
    ],
)
logger = logging.getLogger(__name__)


@dataclass
class UserData:
    """User data structure"""

    full_name: str
    phone_number: str
    passport_id: str
    email: str
    country_of_travel: Dict
    travel_date: Optional[str] = None


@dataclass
class BundleData:
    """Bundle data structure"""

    name: str
    bundle_id: str
    data: str
    duration: str
    country: str
    price: float
    region: str


class EmailService:
    """Professional Email Service for eSIM Delivery"""

    def __init__(self):
        self.smtp_host = os.getenv("EMAIL_HOST")
        self.smtp_port = int(os.getenv("EMAIL_PORT", "587"))
        self.use_tls = os.getenv("EMAIL_USE_TLS", "True").lower() == "true"
        self.username = os.getenv("EMAIL_HOST_USER")
        self.password = os.getenv("EMAIL_HOST_PASSWORD")
        self.from_email = os.getenv("DEFAULT_FROM_EMAIL")
        self.company_name = os.getenv("COMPANY_NAME", "TraveRoam eSIM Solutions")
        self.support_email = os.getenv("SUPPORT_EMAIL", "<EMAIL>")
        self.support_phone = os.getenv("SUPPORT_PHONE", "******-123-4567")
        self.company_website = os.getenv("COMPANY_WEBSITE", "https://traveroam.com")

        # Validate required settings
        if not all([self.smtp_host, self.username, self.password, self.from_email]):
            raise ValueError(
                "Missing required email configuration. Please check your .env file."
            )

    def send_esim_delivery_email(
        self,
        user_data: UserData,
        bundle_data: BundleData,
        esim_data: Dict,
        qr_code_data: str = None,
        invoice_pdf: bytes = None,
        payment_data: Dict = None,
    ) -> bool:
        """Send eSIM delivery email with QR code, instructions, and invoice"""
        try:
            # Create message
            msg = MIMEMultipart("mixed")
            msg["From"] = self.from_email
            msg["To"] = user_data.email
            msg["Subject"] = (
                f"🎉 Your eSIM is Ready - {bundle_data.country} Travel Plan"
            )

            # Create HTML content
            html_content = self._create_html_email(
                user_data, bundle_data, esim_data, qr_code_data
            )

            # Create text content
            text_content = self._create_text_email(user_data, bundle_data, esim_data)

            # Attach text and HTML parts (HTML as primary)
            text_part = MIMEText(text_content, "plain", "utf-8")
            html_part = MIMEText(html_content, "html", "utf-8")

            # Create alternative part for text/html
            alt_part = MIMEMultipart("alternative")
            alt_part.attach(text_part)
            alt_part.attach(html_part)
            msg.attach(alt_part)

            # If we have QR code image, attach it
            if qr_code_data and qr_code_data.startswith("data:image"):
                try:
                    # Extract base64 data and attach QR code image
                    base64_data = qr_code_data.split(",")[1]
                    qr_image_data = base64.b64decode(base64_data)

                    # Create image attachment
                    qr_image = MIMEImage(qr_image_data)
                    qr_image.add_header("Content-ID", "<qr_code>")
                    qr_image.add_header(
                        "Content-Disposition", "inline", filename="esim_qr_code.png"
                    )
                    msg.attach(qr_image)

                except Exception as e:
                    print(f"⚠️ Warning: Could not attach QR code image: {e}")

            # Attach invoice PDF if provided
            if invoice_pdf:
                try:
                    invoice_attachment = MIMEApplication(invoice_pdf, _subtype="pdf")
                    invoice_attachment.add_header(
                        "Content-Disposition",
                        "attachment",
                        filename=f"invoice_{bundle_data.bundle_id}.pdf",
                    )
                    msg.attach(invoice_attachment)
                    print("✅ Invoice PDF attached to email")
                except Exception as e:
                    print(f"⚠️ Warning: Could not attach invoice PDF: {e}")

            # Send email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)

            return True

        except Exception as e:
            print(f"❌ Error sending email: {e}")
            return False

    def _create_html_email(
        self,
        user_data: UserData,
        bundle_data: BundleData,
        esim_data: Dict,
        qr_code_data: str = None,
    ) -> str:
        """Create HTML email content"""
        qr_code_html = ""
        if qr_code_data:
            if qr_code_data.startswith("data:image"):
                qr_code_html = f'<img src="cid:qr_code" alt="eSIM QR Code" style="max-width: 300px; height: auto; border: 1px solid #ddd; border-radius: 8px;">'
            else:
                qr_code_html = '<p style="color: #666;">QR Code will be available after activation</p>'

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Your eSIM is Ready</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
                <h1 style="margin: 0; font-size: 28px;">🎉 Your eSIM is Ready!</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">{self.company_name}</p>
            </div>

            <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 25px;">
                <h2 style="color: #495057; margin-top: 0;">📱 eSIM Details</h2>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="padding: 8px 0; font-weight: bold;">👤 Client:</td><td style="padding: 8px 0;">{user_data.full_name}</td></tr>
                    <tr><td style="padding: 8px 0; font-weight: bold;">🌍 Destination:</td><td style="padding: 8px 0;">{bundle_data.country}</td></tr>
                    <tr><td style="padding: 8px 0; font-weight: bold;">📦 Plan:</td><td style="padding: 8px 0;">{bundle_data.data} for {bundle_data.duration} days</td></tr>
                    <tr><td style="padding: 8px 0; font-weight: bold;">💰 Price:</td><td style="padding: 8px 0;">${bundle_data.price}</td></tr>
                    <tr><td style="padding: 8px 0; font-weight: bold;">📋 Order ID:</td><td style="padding: 8px 0;">{esim_data.get('order_reference', 'N/A')}</td></tr>
                    <tr><td style="padding: 8px 0; font-weight: bold;">🔢 ICCID:</td><td style="padding: 8px 0; font-family: monospace; font-size: 12px;">{esim_data.get('iccid', 'N/A')}</td></tr>
                </table>
            </div>

            <div style="background: #e3f2fd; padding: 25px; border-radius: 10px; margin-bottom: 25px; text-align: center;">
                <h2 style="color: #1976d2; margin-top: 0;">📱 QR Code for Installation</h2>
                {qr_code_html}
                <p style="margin: 15px 0 0 0; color: #666; font-size: 14px;">Scan this QR code with your device to install the eSIM</p>
            </div>

            <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                <h3 style="color: #856404; margin-top: 0;">📋 Installation Instructions</h3>
                <ol style="color: #856404; padding-left: 20px;">
                    <li>Ensure you have a stable internet connection</li>
                    <li>Go to Settings → Cellular/Mobile Data</li>
                    <li>Tap "Add Cellular Plan" or "Add eSIM"</li>
                    <li>Scan the QR code above</li>
                    <li>Follow the on-screen instructions</li>
                    <li>Label your plan (e.g., "{bundle_data.country} Travel")</li>
                </ol>
            </div>

            <div style="background: #d1ecf1; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                <h3 style="color: #0c5460; margin-top: 0;">💡 Important Notes</h3>
                <ul style="color: #0c5460; padding-left: 20px;">
                    <li>Your eSIM will activate automatically when you arrive in {bundle_data.country}</li>
                    <li>Keep your primary SIM active for calls and texts</li>
                    <li>Data usage will be tracked in your device settings</li>
                    <li>Plan expires after {bundle_data.duration} days from activation</li>
                </ul>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; border-top: 3px solid #667eea;">
                <h3 style="color: #495057; margin-top: 0;">🆘 Need Help?</h3>
                <p style="margin: 10px 0;">Our support team is here to help!</p>
                <p style="margin: 5px 0;"><strong>📧 Email:</strong> <a href="mailto:{self.support_email}" style="color: #667eea;">{self.support_email}</a></p>
                <p style="margin: 5px 0;"><strong>📞 Phone:</strong> {self.support_phone}</p>
                <p style="margin: 5px 0;"><strong>🌐 Website:</strong> <a href="{self.company_website}" style="color: #667eea;">{self.company_website}</a></p>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 12px;">
                <p>© 2024 {self.company_name}. All rights reserved.</p>
                <p>This email was sent to {user_data.email}</p>
            </div>
        </body>
        </html>
        """

    def _create_text_email(
        self, user_data: UserData, bundle_data: BundleData, esim_data: Dict
    ) -> str:
        """Create plain text email content"""
        return f"""
🎉 Your eSIM is Ready!
{self.company_name}

📱 eSIM Details:
👤 Client: {user_data.full_name}
🌍 Destination: {bundle_data.country}
📦 Plan: {bundle_data.data} for {bundle_data.duration} days
💰 Price: ${bundle_data.price}
📋 Order ID: {esim_data.get('order_reference', 'N/A')}
🔢 ICCID: {esim_data.get('iccid', 'N/A')}

📋 Installation Instructions:
1. Ensure you have a stable internet connection
2. Go to Settings → Cellular/Mobile Data
3. Tap "Add Cellular Plan" or "Add eSIM"
4. Scan the QR code (attached to this email)
5. Follow the on-screen instructions
6. Label your plan (e.g., "{bundle_data.country} Travel")

💡 Important Notes:
• Your eSIM will activate automatically when you arrive in {bundle_data.country}
• Keep your primary SIM active for calls and texts
• Data usage will be tracked in your device settings
• Plan expires after {bundle_data.duration} days from activation

🆘 Need Help?
📧 Email: {self.support_email}
📞 Phone: {self.support_phone}
🌐 Website: {self.company_website}

© 2024 {self.company_name}. All rights reserved.
This email was sent to {user_data.email}
        """


class StripePaymentService:
    """Professional Stripe Payment Service for eSIM Bundle Purchases"""

    def __init__(self):
        """Initialize Stripe service with environment variables"""
        self.stripe_secret_key = os.getenv("STRIPE_SECRET_KEY")
        self.stripe_public_key = os.getenv("STRIPE_PUBLIC_KEY")
        self.currency = os.getenv("STRIPE_CURRENCY", "USD")

        if not self.stripe_secret_key:
            raise ValueError("STRIPE_SECRET_KEY must be set in environment")

        stripe.api_key = self.stripe_secret_key
        logger.info("Stripe Payment Service initialized")

    def create_checkout_session(
        self, bundle_data: BundleData, user_data: UserData, reseller_markup: float = 0.0
    ) -> Dict:
        """Create Stripe checkout session for bundle purchase"""
        try:
            # Calculate final price with markup
            base_price = bundle_data.price
            markup_amount = (
                base_price * (reseller_markup / 100) if reseller_markup > 0 else 0
            )
            final_price = base_price + markup_amount

            # Create line items for checkout
            line_items = [
                {
                    "price_data": {
                        "currency": self.currency.lower(),
                        "product_data": {
                            "name": f"eSIM Bundle - {bundle_data.data} for {bundle_data.duration}",
                            "description": f"TraveRoam eSIM Bundle for {bundle_data.country}",
                            "metadata": {
                                "bundle_name": bundle_data.bundle_id,
                                "country": bundle_data.country,
                                "data_volume": bundle_data.data,
                                "validity_days": bundle_data.duration,
                                "base_price": str(base_price),
                                "markup_percent": str(reseller_markup),
                                "markup_amount": str(markup_amount),
                            },
                        },
                        "unit_amount": int(final_price * 100),  # Convert to cents
                    },
                    "quantity": 1,
                }
            ]

            # Create checkout session
            session = stripe.checkout.Session.create(
                payment_method_types=["card"],
                line_items=line_items,
                mode="payment",
                success_url="http://localhost:8000/payment/success?session_id={CHECKOUT_SESSION_ID}",
                cancel_url="http://localhost:8000/payment/cancel?bundle="
                + bundle_data.bundle_id,
                customer_email=user_data.email,
                metadata={
                    "bundle_name": bundle_data.bundle_id,
                    "client_name": user_data.full_name,
                    "client_email": user_data.email,
                    "client_phone": user_data.phone_number,
                    "country": bundle_data.country,
                    "base_price": str(base_price),
                    "markup_percent": str(reseller_markup),
                    "markup_amount": str(markup_amount),
                    "final_price": str(final_price),
                    "purchase_type": "traveroam_bundle",
                },
            )

            logger.info(f"Created Stripe checkout session: {session.id}")

            return {
                "success": True,
                "session_id": session.id,
                "checkout_url": session.url,
                "amount": final_price,
                "currency": self.currency,
                "metadata": session.metadata,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating checkout session: {str(e)}")
            return {"success": False, "error": f"Stripe error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error creating checkout session: {str(e)}")
            return {"success": False, "error": f"Payment processing error: {str(e)}"}

    def retrieve_checkout_session(self, session_id: str) -> Dict:
        """Retrieve checkout session details"""
        try:
            session = stripe.checkout.Session.retrieve(session_id)

            return {
                "success": True,
                "session_id": session.id,
                "payment_status": session.payment_status,
                "payment_intent_id": session.payment_intent,
                "customer_email": (
                    session.customer_details.email if session.customer_details else None
                ),
                "amount_total": (
                    session.amount_total / 100 if session.amount_total else 0
                ),
                "currency": (
                    session.currency.upper() if session.currency else self.currency
                ),
                "metadata": session.metadata,
                "created": session.created,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error retrieving session: {str(e)}")
            return {"success": False, "error": f"Stripe error: {str(e)}"}

    def create_invoice_pdf(
        self, user_data: UserData, bundle_data: BundleData, payment_data: Dict
    ) -> bytes:
        """Create PDF invoice for the purchase"""
        try:
            from reportlab.lib import colors
            from reportlab.lib.pagesizes import letter
            from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
            from reportlab.lib.units import inch
            from reportlab.platypus import (
                Paragraph,
                SimpleDocTemplate,
                Spacer,
                Table,
                TableStyle,
            )

            # Create PDF buffer
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=letter)
            story = []

            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                "CustomTitle",
                parent=styles["Heading1"],
                fontSize=16,
                spaceAfter=30,
                alignment=1,  # Center alignment
            )

            # Add company header
            company_name = os.getenv("COMPANY_NAME", "TraveRoam eSIM Solutions")
            company_address = os.getenv(
                "COMPANY_ADDRESS", "123 Business St, City, Country"
            )
            company_phone = os.getenv("SUPPORT_PHONE", "******-123-4567")
            company_email = os.getenv("SUPPORT_EMAIL", "<EMAIL>")

            story.append(Paragraph(company_name, title_style))
            story.append(Paragraph(company_address, styles["Normal"]))
            story.append(
                Paragraph(
                    f"Phone: {company_phone} | Email: {company_email}", styles["Normal"]
                )
            )
            story.append(Spacer(1, 20))

            # Add invoice title
            story.append(Paragraph("INVOICE", title_style))
            story.append(Spacer(1, 20))

            # Add invoice details
            invoice_data = [
                ["Invoice Date:", datetime.now().strftime("%B %d, %Y")],
                [
                    "Invoice Number:",
                    f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}",
                ],
                ["Payment Status:", "Paid"],
                ["Payment Method:", "Credit Card (Stripe)"],
            ]

            invoice_table = Table(invoice_data, colWidths=[2 * inch, 4 * inch])
            invoice_table.setStyle(
                TableStyle(
                    [
                        ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                        ("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, -1), 10),
                        ("BOTTOMPADDING", (0, 0), (-1, -1), 12),
                    ]
                )
            )
            story.append(invoice_table)
            story.append(Spacer(1, 20))

            # Add customer details
            story.append(Paragraph("Bill To:", styles["Heading2"]))
            customer_data = [
                ["Name:", user_data.full_name],
                ["Email:", user_data.email],
                ["Phone:", user_data.phone_number],
                ["Country:", user_data.country_of_travel["name"]],
            ]

            customer_table = Table(customer_data, colWidths=[1.5 * inch, 4.5 * inch])
            customer_table.setStyle(
                TableStyle(
                    [
                        ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                        ("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, -1), 10),
                        ("BOTTOMPADDING", (0, 0), (-1, -1), 8),
                    ]
                )
            )
            story.append(customer_table)
            story.append(Spacer(1, 20))

            # Add product details
            story.append(Paragraph("Product Details:", styles["Heading2"]))

            # Calculate pricing
            base_price = bundle_data.price
            markup_percent = payment_data.get("markup_percent", 0)
            markup_amount = (
                base_price * (markup_percent / 100) if markup_percent > 0 else 0
            )
            final_price = base_price + markup_amount

            product_data = [
                ["Description", "Quantity", "Unit Price", "Amount"],
                [
                    f"eSIM Bundle - {bundle_data.data} for {bundle_data.duration}<br/>"
                    f"Country: {bundle_data.country}<br/>"
                    f"Bundle: {bundle_data.bundle_id}",
                    "1",
                    f"${base_price:.2f}",
                    f"${base_price:.2f}",
                ],
            ]

            if markup_amount > 0:
                product_data.append(
                    [
                        f"Reseller Markup ({markup_percent}%)",
                        "1",
                        f"${markup_amount:.2f}",
                        f"${markup_amount:.2f}",
                    ]
                )

            product_table = Table(
                product_data, colWidths=[3 * inch, 1 * inch, 1.5 * inch, 1.5 * inch]
            )
            product_table.setStyle(
                TableStyle(
                    [
                        ("ALIGN", (0, 0), (-1, -1), "LEFT"),
                        ("FONTNAME", (0, 0), (0, 0), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, -1), 10),
                        ("BOTTOMPADDING", (0, 0), (-1, -1), 12),
                        ("GRID", (0, 0), (-1, -1), 1, colors.black),
                        ("BACKGROUND", (0, 0), (-1, 0), colors.grey),
                    ]
                )
            )
            story.append(product_table)
            story.append(Spacer(1, 20))

            # Add total
            total_data = [["Total Amount:", f"${final_price:.2f}"]]

            total_table = Table(total_data, colWidths=[4 * inch, 2 * inch])
            total_table.setStyle(
                TableStyle(
                    [
                        ("ALIGN", (0, 0), (0, 0), "RIGHT"),
                        ("ALIGN", (1, 0), (1, 0), "RIGHT"),
                        ("FONTNAME", (0, 0), (1, 0), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (1, 0), 12),
                        ("BOTTOMPADDING", (0, 0), (1, 0), 12),
                    ]
                )
            )
            story.append(total_table)
            story.append(Spacer(1, 30))

            # Add terms and conditions
            story.append(Paragraph("Terms & Conditions:", styles["Heading3"]))
            terms_text = """
            • This eSIM will activate automatically when you arrive in the destination country
            • Plan validity starts from activation date
            • No refunds after eSIM activation
            • Support available 24/7 via email and phone
            • Data usage subject to fair use policy
            """
            story.append(Paragraph(terms_text, styles["Normal"]))

            # Build PDF
            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()

        except Exception as e:
            logger.error(f"Error creating PDF invoice: {str(e)}")
            return None


class TraveRoamAPIClient:
    """Professional TraveRoam API Client"""

    def __init__(self):
        """Initialize API client with environment variables"""
        self.base_url = os.getenv(
            "TRAVEROAM_API_BASE_URL", "https://sandbox.travelroam.com/api"
        )
        self.api_key = os.getenv("TRAVEROAM_API_KEY")
        self.client_secret = os.getenv("TRAVEROAM_SECRET_KEY")

        if not self.api_key or not self.client_secret:
            raise ValueError(
                "TRAVEROAM_API_KEY and TRAVEROAM_SECRET_KEY must be set in environment"
            )

        self.session = requests.Session()
        self.session.headers.update(
            {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-api-key": self.api_key,
                "clientSecret": self.client_secret,
            }
        )

        logger.info(f"TraveRoam API Client initialized for {self.base_url}")

    def _make_request(
        self, method: str, endpoint: str, data: Optional[Dict] = None
    ) -> Dict:
        """Make API request with proper error handling"""
        url = f"{self.base_url}{endpoint}"

        try:
            if method.upper() == "GET":
                response = self.session.get(url, timeout=30)
            elif method.upper() == "POST":
                response = self.session.post(url, json=data, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {str(e)}")
            raise

    def get_catalogue(self, countries: str, region: str = "Asia") -> List[Dict]:
        """Get available bundles from catalogue"""
        data = {"countries": countries, "region": region}
        return self._make_request("POST", "/whitelabel/catalogue", data)

    def process_order(self, bundle_name: str, order_type: str = "REGION") -> Dict:
        """Process order to provision eSIM"""
        data = {"item": bundle_name, "type": order_type}
        return self._make_request("POST", "/whitelabel/processorders", data)

    def get_esim_assignments(self, reference: str) -> Dict:
        """Get eSIM assignments/QR codes by order reference"""
        data = {"reference": reference}
        return self._make_request("POST", "/whitelabel/esims/assignments", data)


class ESIMWorkflowConsole:
    """Professional eSIM Workflow Console Application"""

    def __init__(self):
        """Initialize the console application"""
        self.traveroam_client = TraveRoamAPIClient()
        self.current_user = None
        self.current_reseller = None

        # Initialize email service
        try:
            self.email_service = EmailService()
            self.email_enabled = True
            logger.info("Email service initialized successfully")
        except ValueError as e:
            logger.warning(f"Email service not available: {e}")
            self.email_service = None
            self.email_enabled = False

        # Initialize Stripe payment service
        try:
            self.stripe_service = StripePaymentService()
            self.stripe_enabled = True
            logger.info("Stripe payment service initialized successfully")
        except ValueError as e:
            logger.warning(f"Stripe payment service not available: {e}")
            self.stripe_service = None
            self.stripe_enabled = False

        # Comprehensive country code mapping
        self.country_mapping = self._load_country_mapping()

        logger.info("eSIM Workflow Console initialized successfully")

    def _load_country_mapping(self) -> Dict:
        """Load comprehensive country code mapping"""
        return {
            # Asia
            "+91": {"name": "India", "code": "IN", "region": "Asia"},
            "+65": {"name": "Singapore", "code": "SG", "region": "Asia"},
            "+66": {"name": "Thailand", "code": "TH", "region": "Asia"},
            "+60": {"name": "Malaysia", "code": "MY", "region": "Asia"},
            "+62": {"name": "Indonesia", "code": "ID", "region": "Asia"},
            "+852": {"name": "Hong Kong", "code": "HK", "region": "Asia"},
            "+81": {"name": "Japan", "code": "JP", "region": "Asia"},
            "+82": {"name": "South Korea", "code": "KR", "region": "Asia"},
            "+86": {"name": "China", "code": "CN", "region": "Asia"},
            "+880": {"name": "Bangladesh", "code": "BD", "region": "Asia"},
            "+95": {"name": "Myanmar", "code": "MM", "region": "Asia"},
            "+84": {"name": "Vietnam", "code": "VN", "region": "Asia"},
            "+63": {"name": "Philippines", "code": "PH", "region": "Asia"},
            "+92": {"name": "Pakistan", "code": "PK", "region": "Asia"},
            "+94": {"name": "Sri Lanka", "code": "LK", "region": "Asia"},
            "+977": {"name": "Nepal", "code": "NP", "region": "Asia"},
            "+93": {"name": "Afghanistan", "code": "AF", "region": "Asia"},
            "+374": {"name": "Armenia", "code": "AM", "region": "Asia"},
            "+994": {"name": "Azerbaijan", "code": "AZ", "region": "Asia"},
            "+995": {"name": "Georgia", "code": "GE", "region": "Asia"},
            "+996": {"name": "Kyrgyzstan", "code": "KG", "region": "Asia"},
            "+7": {"name": "Kazakhstan", "code": "KZ", "region": "Asia"},
            "+856": {"name": "Laos", "code": "LA", "region": "Asia"},
            "+976": {"name": "Mongolia", "code": "MN", "region": "Asia"},
            "+853": {"name": "Macau", "code": "MO", "region": "Asia"},
            "+970": {"name": "Palestine", "code": "PS", "region": "Asia"},
            "+992": {"name": "Tajikistan", "code": "TJ", "region": "Asia"},
            "+886": {"name": "Taiwan", "code": "TW", "region": "Asia"},
            "+998": {"name": "Uzbekistan", "code": "UZ", "region": "Asia"},
            # Europe
            "+44": {"name": "United Kingdom", "code": "GB", "region": "Europe"},
            "+33": {"name": "France", "code": "FR", "region": "Europe"},
            "+49": {"name": "Germany", "code": "DE", "region": "Europe"},
            "+39": {"name": "Italy", "code": "IT", "region": "Europe"},
            "+34": {"name": "Spain", "code": "ES", "region": "Europe"},
            "+31": {"name": "Netherlands", "code": "NL", "region": "Europe"},
            "+32": {"name": "Belgium", "code": "BE", "region": "Europe"},
            "+46": {"name": "Sweden", "code": "SE", "region": "Europe"},
            "+47": {"name": "Norway", "code": "NO", "region": "Europe"},
            "+45": {"name": "Denmark", "code": "DK", "region": "Europe"},
            "+358": {"name": "Finland", "code": "FI", "region": "Europe"},
            "+41": {"name": "Switzerland", "code": "CH", "region": "Europe"},
            "+43": {"name": "Austria", "code": "AT", "region": "Europe"},
            "+48": {"name": "Poland", "code": "PL", "region": "Europe"},
            "+420": {"name": "Czech Republic", "code": "CZ", "region": "Europe"},
            "+36": {"name": "Hungary", "code": "HU", "region": "Europe"},
            "+40": {"name": "Romania", "code": "RO", "region": "Europe"},
            "+30": {"name": "Greece", "code": "GR", "region": "Europe"},
            "+351": {"name": "Portugal", "code": "PT", "region": "Europe"},
            "+353": {"name": "Ireland", "code": "IE", "region": "Europe"},
            "+380": {"name": "Ukraine", "code": "UA", "region": "Europe"},
            "+90": {"name": "Turkey", "code": "TR", "region": "Europe"},
            # North America
            "+1": {"name": "United States", "code": "US", "region": "North America"},
            "+52": {"name": "Mexico", "code": "MX", "region": "North America"},
            # South America
            "+55": {"name": "Brazil", "code": "BR", "region": "South America"},
            "+54": {"name": "Argentina", "code": "AR", "region": "South America"},
            "+57": {"name": "Colombia", "code": "CO", "region": "South America"},
            "+51": {"name": "Peru", "code": "PE", "region": "South America"},
            "+56": {"name": "Chile", "code": "CL", "region": "South America"},
            "+58": {"name": "Venezuela", "code": "VE", "region": "South America"},
            # Africa
            "+27": {"name": "South Africa", "code": "ZA", "region": "Africa"},
            "+20": {"name": "Egypt", "code": "EG", "region": "Africa"},
            "+234": {"name": "Nigeria", "code": "NG", "region": "Africa"},
            "+254": {"name": "Kenya", "code": "KE", "region": "Africa"},
            "+251": {"name": "Ethiopia", "code": "ET", "region": "Africa"},
            "+212": {"name": "Morocco", "code": "MA", "region": "Africa"},
            "+216": {"name": "Tunisia", "code": "TN", "region": "Africa"},
            "+213": {"name": "Algeria", "code": "DZ", "region": "Africa"},
            "+233": {"name": "Ghana", "code": "GH", "region": "Africa"},
            "+225": {"name": "Ivory Coast", "code": "CI", "region": "Africa"},
            # Oceania
            "+61": {"name": "Australia", "code": "AU", "region": "Oceania"},
            "+64": {"name": "New Zealand", "code": "NZ", "region": "Oceania"},
            "+675": {"name": "Papua New Guinea", "code": "PG", "region": "Oceania"},
            "+679": {"name": "Fiji", "code": "FJ", "region": "Oceania"},
            # Middle East
            "+971": {
                "name": "United Arab Emirates",
                "code": "AE",
                "region": "Middle East",
            },
            "+966": {"name": "Saudi Arabia", "code": "SA", "region": "Middle East"},
            "+972": {"name": "Israel", "code": "IL", "region": "Middle East"},
            "+973": {"name": "Bahrain", "code": "BH", "region": "Middle East"},
            "+974": {"name": "Qatar", "code": "QA", "region": "Middle East"},
            "+965": {"name": "Kuwait", "code": "KW", "region": "Middle East"},
            "+968": {"name": "Oman", "code": "OM", "region": "Middle East"},
            "+962": {"name": "Jordan", "code": "JO", "region": "Middle East"},
            "+961": {"name": "Lebanon", "code": "LB", "region": "Middle East"},
            "+964": {"name": "Iraq", "code": "IQ", "region": "Middle East"},
            "+98": {"name": "Iran", "code": "IR", "region": "Middle East"},
        }

    def print_header(self):
        """Print application header"""
        print("\n" + "=" * 80)
        print("🌍 eSIM Workflow Console Application v3.0.0")
        print("📱 Professional Reseller Workflow with TraveRoam + Stripe Integration")
        print("🔧 Environment: " + os.getenv("TRAVEROAM_API_BASE_URL", "Unknown"))
        print(
            "💳 Stripe Payments: "
            + ("✅ Enabled" if self.stripe_enabled else "❌ Disabled")
        )
        print(
            "📧 Email Service: "
            + ("✅ Enabled" if self.email_enabled else "❌ Disabled")
        )
        print("=" * 80)

    def print_step(self, step: int, title: str):
        """Print step header"""
        print(f"\n{step}️⃣ {title}")
        print("-" * 50)

    def get_user_input(self, prompt: str) -> str:
        """Get user input with validation"""
        while True:
            try:
                user_input = input(prompt).strip()
                if user_input:
                    return user_input
                print("❌ Input cannot be empty. Please try again.")
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                sys.exit(0)
            except EOFError:
                print("\n\n👋 Goodbye!")
                sys.exit(0)

    def validate_email(self, email: str) -> bool:
        """Validate email format"""
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        return re.match(pattern, email) is not None

    def validate_phone(self, phone: str) -> bool:
        """Validate phone number format"""
        # Remove spaces and special characters
        clean_phone = re.sub(r"[\s\-\(\)]", "", phone)
        # Check if it starts with + and has at least 10 digits
        return clean_phone.startswith("+") and len(clean_phone) >= 10

    def detect_country_from_phone(self, phone: str) -> Optional[Dict]:
        """Detect country from phone number using multiple methods"""
        # Remove spaces and special characters
        clean_phone = re.sub(r"[\s\-\(\)]", "", phone)

        # Method 1: Try to match country codes from our mapping (longest first)
        for code in sorted(self.country_mapping.keys(), key=len, reverse=True):
            if clean_phone.startswith(code):
                return self.country_mapping[code]

        # Method 2: Try external API for country detection
        try:
            country_info = self._detect_country_api(clean_phone)
            if country_info:
                return country_info
        except:
            pass

        # Method 3: Try to extract country code and use a simple lookup
        if clean_phone.startswith("+"):
            # Extract the country code (first 1-4 digits after +)
            country_code = clean_phone[1:5] if len(clean_phone) > 5 else clean_phone[1:]

            # Try to find a match with our mapping
            for code, info in self.country_mapping.items():
                if code[1:] == country_code:  # Remove + from our mapping
                    return info

        return None

    def _detect_country_api(self, phone: str) -> Optional[Dict]:
        """Detect country using external API (free service)"""
        try:
            # Use a free phone number validation API
            url = f"https://api.numlookupapi.com/v1/validate/{phone}"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                data = response.json()
                if data.get("valid"):
                    country_name = data.get("country_name", "Unknown")
                    country_code = data.get("country_code", "XX")

                    # Map to our regions
                    region_mapping = {
                        "Asia": [
                            "IN",
                            "SG",
                            "TH",
                            "MY",
                            "ID",
                            "HK",
                            "JP",
                            "KR",
                            "CN",
                            "BD",
                            "MM",
                            "VN",
                            "PH",
                            "PK",
                            "LK",
                            "NP",
                            "AF",
                            "AM",
                            "AZ",
                            "GE",
                            "KG",
                            "KZ",
                            "LA",
                            "MN",
                            "MO",
                            "PS",
                            "TJ",
                            "TW",
                            "UZ",
                        ],
                        "Europe": [
                            "GB",
                            "FR",
                            "DE",
                            "IT",
                            "ES",
                            "NL",
                            "BE",
                            "SE",
                            "NO",
                            "DK",
                            "FI",
                            "CH",
                            "AT",
                            "PL",
                            "CZ",
                            "HU",
                            "RO",
                            "GR",
                            "PT",
                            "IE",
                            "UA",
                            "RU",
                            "TR",
                        ],
                        "North America": ["US", "CA", "MX"],
                        "South America": ["BR", "AR", "CO", "PE", "CL", "VE"],
                        "Africa": [
                            "ZA",
                            "EG",
                            "NG",
                            "KE",
                            "ET",
                            "MA",
                            "TN",
                            "DZ",
                            "GH",
                            "CI",
                        ],
                        "Oceania": ["AU", "NZ", "PG", "FJ"],
                        "Middle East": [
                            "AE",
                            "SA",
                            "IL",
                            "BH",
                            "QA",
                            "KW",
                            "OM",
                            "JO",
                            "LB",
                            "IQ",
                            "IR",
                        ],
                    }

                    region = "Asia"  # Default
                    for reg, codes in region_mapping.items():
                        if country_code in codes:
                            region = reg
                            break

                    return {
                        "name": country_name,
                        "code": country_code,
                        "region": region,
                    }
        except:
            pass

        return None

    def get_user_details(self) -> UserData:
        """Step 1: Get user details with validation"""
        self.print_step(1, "Add New User")

        print("Please provide the following details:")

        # Full Name
        while True:
            full_name = self.get_user_input("👤 Full Name: ")
            if len(full_name) >= 2:
                break
            print("❌ Name must be at least 2 characters long.")

        # Phone Number
        while True:
            phone_number = self.get_user_input(
                "📞 Phone Number (with country code, e.g., +91XXXXXXXXXX): "
            )
            if self.validate_phone(phone_number):
                break
            print(
                "❌ Please enter a valid phone number with country code (e.g., +91XXXXXXXXXX)"
            )

        # Passport/National ID
        passport_id = self.get_user_input("🛂 Passport Number / National ID: ")

        # Email
        while True:
            email = self.get_user_input("📧 Email: ")
            if self.validate_email(email):
                break
            print("❌ Please enter a valid email address.")

        # Country Detection
        country_info = self.detect_country_from_phone(phone_number)
        if country_info:
            print(
                f"\n🌍 Detected Country: {country_info['name']} ({country_info['code']})"
            )
            use_detected = self.get_user_input("Use detected country? (y/n): ").lower()
            if use_detected == "y":
                country_of_travel = country_info
            else:
                country_of_travel = {"name": "Unknown", "code": "XX", "region": "Asia"}
        else:
            country_of_travel = {"name": "Unknown", "code": "XX", "region": "Asia"}

        # Travel Date (optional)
        travel_date = self.get_user_input("📅 Date of Travel (optional, YYYY-MM-DD): ")
        if not travel_date:
            travel_date = None

        user_data = UserData(
            full_name=full_name,
            phone_number=phone_number,
            passport_id=passport_id,
            email=email,
            country_of_travel=country_of_travel,
            travel_date=travel_date,
        )

        logger.info(f"User data collected: {user_data.full_name} ({user_data.email})")
        return user_data

    def get_available_bundles(self, user_data: UserData) -> List[BundleData]:
        """Step 2: Fetch available eSIM plans"""
        self.print_step(2, "Fetch Available eSIM Plans")

        print(
            f"🔍 Fetching available bundles for {user_data.country_of_travel['code']} ({user_data.country_of_travel['region']})..."
        )

        try:
            catalogue = self.traveroam_client.get_catalogue(
                countries=user_data.country_of_travel["code"],
                region=user_data.country_of_travel["region"],
            )

            if not catalogue:
                print("❌ No bundles found for this country/region")
                return self._get_predefined_bundles(user_data.country_of_travel)

            print(f"✅ Found catalogue response")

            # Debug: Print the actual response structure
            print(f"🔍 API Response type: {type(catalogue)}")
            print(f"🔍 API Response: {catalogue}")

            # Convert to BundleData objects
            bundles = []

            # Handle different response formats
            if isinstance(catalogue, list):
                print(f"📦 Processing {len(catalogue)} bundles from list...")
                for bundle in catalogue:
                    try:
                        if isinstance(bundle, dict):
                            # Standard dictionary format
                            bundle_data = BundleData(
                                name=bundle.get(
                                    "name", bundle.get("bundle_name", "Unknown")
                                ),
                                bundle_id=bundle.get(
                                    "bundle_id",
                                    bundle.get("id", bundle.get("name", "")),
                                ),
                                data=bundle.get("data", bundle.get("size", "1GB")),
                                duration=bundle.get(
                                    "duration", bundle.get("validity", "7 days")
                                ),
                                country=bundle.get(
                                    "country", user_data.country_of_travel["name"]
                                ),
                                price=float(bundle.get("price", 5.0)),
                                region=bundle.get(
                                    "region", user_data.country_of_travel["region"]
                                ),
                            )
                        elif isinstance(bundle, str):
                            # String format - create a basic bundle
                            bundle_data = BundleData(
                                name=bundle,
                                bundle_id=bundle,
                                data="1GB",  # Default
                                duration="7 days",  # Default
                                country=user_data.country_of_travel["name"],
                                price=5.0,  # Default price
                                region=user_data.country_of_travel["region"],
                            )
                        else:
                            logger.warning(
                                f"Unknown bundle format: {type(bundle)} - {bundle}"
                            )
                            continue

                        bundles.append(bundle_data)
                        print(f"✅ Parsed bundle: {bundle_data.name}")

                    except Exception as e:
                        logger.warning(f"Failed to parse bundle: {e}")
                        continue

            elif isinstance(catalogue, dict):
                print("📦 Processing single bundle from dict...")
                # Check if it's a wrapper with bundles inside
                if "bundles" in catalogue:
                    print(f"📦 Found {len(catalogue['bundles'])} bundles in wrapper...")
                    for bundle in catalogue["bundles"]:
                        try:
                            bundle_data = BundleData(
                                name=bundle.get(
                                    "name", bundle.get("bundle_name", "Unknown")
                                ),
                                bundle_id=bundle.get(
                                    "bundle_id",
                                    bundle.get("id", bundle.get("name", "")),
                                ),
                                data=bundle.get("data", bundle.get("size", "1GB")),
                                duration=bundle.get(
                                    "duration", bundle.get("validity", "7 days")
                                ),
                                country=bundle.get(
                                    "country", user_data.country_of_travel["name"]
                                ),
                                price=float(bundle.get("price", 5.0)),
                                region=bundle.get(
                                    "region", user_data.country_of_travel["region"]
                                ),
                            )
                            bundles.append(bundle_data)
                            print(f"✅ Parsed bundle: {bundle_data.name}")
                        except Exception as e:
                            logger.warning(f"Failed to parse bundle from wrapper: {e}")
                            continue
                else:
                    # Single bundle or response wrapper
                    try:
                        bundle_data = BundleData(
                            name=catalogue.get(
                                "name", catalogue.get("bundle_name", "Unknown")
                            ),
                            bundle_id=catalogue.get(
                                "bundle_id",
                                catalogue.get("id", catalogue.get("name", "")),
                            ),
                            data=catalogue.get("data", catalogue.get("size", "1GB")),
                            duration=catalogue.get(
                                "duration", catalogue.get("validity", "7 days")
                            ),
                            country=catalogue.get(
                                "country", user_data.country_of_travel["name"]
                            ),
                            price=float(catalogue.get("price", 5.0)),
                            region=catalogue.get(
                                "region", user_data.country_of_travel["region"]
                            ),
                        )
                        bundles.append(bundle_data)
                        print(f"✅ Parsed single bundle: {bundle_data.name}")
                    except Exception as e:
                        logger.warning(f"Failed to parse single bundle: {e}")

            print(f"✅ Successfully parsed {len(bundles)} bundles")

            # If no bundles were parsed, use predefined bundles as fallback
            if not bundles:
                print("⚠️  No bundles parsed from API, using predefined bundles...")
                bundles = self._get_predefined_bundles(user_data.country_of_travel)

            return bundles

        except Exception as e:
            logger.error(f"Failed to fetch bundles: {e}")
            print(f"❌ Error fetching bundles: {str(e)}")
            print("⚠️  Using predefined bundles as fallback...")
            return self._get_predefined_bundles(user_data.country_of_travel)

    def _get_predefined_bundles(self, country_info: Dict) -> List[BundleData]:
        """Get predefined bundles for fallback when API fails"""
        country_code = country_info.get("code", "IN")
        country_name = country_info.get("name", "India")
        region = country_info.get("region", "Asia")

        # Predefined bundles based on country
        predefined_bundles = {
            "IN": [
                BundleData(
                    "1GB 7 Days India",
                    "esimp_1GB_7D_IN_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    5.0,
                    region,
                ),
                BundleData(
                    "2GB 15 Days India",
                    "esimp_2GB_15D_IN_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    8.0,
                    region,
                ),
                BundleData(
                    "Unlimited 1 Day India",
                    "esimp_UL_1D_IN_V2",
                    "Unlimited",
                    "1 day",
                    country_name,
                    3.0,
                    region,
                ),
                BundleData(
                    "Unlimited 3 Days India",
                    "esimp_UL_3D_IN_V2",
                    "Unlimited",
                    "3 days",
                    country_name,
                    7.0,
                    region,
                ),
            ],
            "SG": [
                BundleData(
                    "1GB 7 Days Singapore",
                    "esimp_1GB_7D_SG_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    6.0,
                    region,
                ),
                BundleData(
                    "2GB 15 Days Singapore",
                    "esimp_2GB_15D_SG_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    10.0,
                    region,
                ),
            ],
            "TH": [
                BundleData(
                    "1GB 7 Days Thailand",
                    "esimp_1GB_7D_TH_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    5.5,
                    region,
                ),
                BundleData(
                    "2GB 15 Days Thailand",
                    "esimp_2GB_15D_TH_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    9.0,
                    region,
                ),
            ],
            "MY": [
                BundleData(
                    "1GB 7 Days Malaysia",
                    "esimp_1GB_7D_MY_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    5.5,
                    region,
                ),
                BundleData(
                    "2GB 15 Days Malaysia",
                    "esimp_2GB_15D_MY_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    9.0,
                    region,
                ),
            ],
            "ID": [
                BundleData(
                    "1GB 7 Days Indonesia",
                    "esimp_1GB_7D_ID_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    5.0,
                    region,
                ),
                BundleData(
                    "2GB 15 Days Indonesia",
                    "esimp_2GB_15D_ID_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    8.5,
                    region,
                ),
            ],
            "HK": [
                BundleData(
                    "1GB 7 Days Hong Kong",
                    "esimp_1GB_7D_HK_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    6.5,
                    region,
                ),
                BundleData(
                    "2GB 15 Days Hong Kong",
                    "esimp_2GB_15D_HK_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    11.0,
                    region,
                ),
            ],
            "JP": [
                BundleData(
                    "1GB 7 Days Japan",
                    "esimp_1GB_7D_JP_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    7.0,
                    region,
                ),
                BundleData(
                    "2GB 15 Days Japan",
                    "esimp_2GB_15D_JP_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    12.0,
                    region,
                ),
            ],
            "KR": [
                BundleData(
                    "1GB 7 Days South Korea",
                    "esimp_1GB_7D_KR_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    6.5,
                    region,
                ),
                BundleData(
                    "2GB 15 Days South Korea",
                    "esimp_2GB_15D_KR_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    11.0,
                    region,
                ),
            ],
            "CN": [
                BundleData(
                    "1GB 7 Days China",
                    "esimp_1GB_7D_CN_V2",
                    "1GB",
                    "7 days",
                    country_name,
                    6.0,
                    region,
                ),
                BundleData(
                    "2GB 15 Days China",
                    "esimp_2GB_15D_CN_V2",
                    "2GB",
                    "15 days",
                    country_name,
                    10.0,
                    region,
                ),
            ],
        }

        # Return country-specific bundles or default to India bundles
        return predefined_bundles.get(country_code, predefined_bundles["IN"])

    def display_bundles(self, bundles: List[BundleData]) -> None:
        """Display available bundles in a formatted table"""
        if not bundles:
            print("❌ No bundles available")
            return

        print("\n📦 Available eSIM Plans:")
        print("-" * 80)

        for i, bundle in enumerate(bundles, 1):
            print(f" {i:2d}. {bundle.data} - {bundle.duration} - {bundle.country}")
            print(f"     Bundle: {bundle.bundle_id}")
            print(f"     Price: ${bundle.price:.2f}")
            print()

    def select_bundle(self, bundles: List[BundleData]) -> Optional[BundleData]:
        """Let user select a bundle"""
        if not bundles:
            return None

        while True:
            try:
                selection = self.get_user_input(
                    f"Select bundle number (1-{len(bundles)}): "
                )
                bundle_number = int(selection)

                if 1 <= bundle_number <= len(bundles):
                    selected = bundles[bundle_number - 1]
                    print(
                        f"\n✅ Selected: {selected.data} - {selected.duration} - {selected.country}"
                    )
                    return selected
                else:
                    print(f"❌ Please enter a number between 1 and {len(bundles)}")
            except ValueError:
                print("❌ Please enter a valid number")

    def process_payment(
        self, bundle_data: BundleData, user_data: UserData
    ) -> Optional[Dict]:
        """Step 3: Process payment via Stripe"""
        self.print_step(3, "Payment Processing")

        if not self.stripe_enabled:
            print("❌ Stripe payment service not available")
            print(
                "💡 To enable Stripe payments, set STRIPE_SECRET_KEY in your .env file"
            )
            return None

        # Get reseller markup
        markup_input = self.get_user_input(
            "Enter reseller markup percentage (0-50, default 0): "
        )
        try:
            markup_percent = float(markup_input) if markup_input.strip() else 0.0
            if markup_percent < 0 or markup_percent > 50:
                print("⚠️ Markup percentage adjusted to 0% (must be between 0-50)")
                markup_percent = 0.0
        except ValueError:
            print("⚠️ Invalid markup percentage, using 0%")
            markup_percent = 0.0

        # Calculate pricing
        base_price = bundle_data.price
        markup_amount = base_price * (markup_percent / 100) if markup_percent > 0 else 0
        final_price = base_price + markup_amount

        print(f"\n💰 Payment Summary:")
        print(f"   Base Price: ${base_price:.2f}")
        if markup_percent > 0:
            print(f"   Reseller Markup ({markup_percent}%): ${markup_amount:.2f}")
        print(f"   Final Price: ${final_price:.2f}")
        print(f"   Currency: {self.stripe_service.currency}")

        # Confirm payment
        confirm = self.get_user_input(
            f"\nProceed with payment of ${final_price:.2f}? (y/n): "
        ).lower()
        if confirm != "y":
            print("❌ Payment cancelled by user")
            return None

        print("\n🔄 Creating Stripe checkout session...")

        # Create checkout session
        checkout_result = self.stripe_service.create_checkout_session(
            bundle_data=bundle_data, user_data=user_data, reseller_markup=markup_percent
        )

        if not checkout_result["success"]:
            print(f"❌ Failed to create checkout session: {checkout_result['error']}")
            return None

        print("✅ Stripe checkout session created successfully!")
        print(f"🔗 Checkout URL: {checkout_result['checkout_url']}")
        print(f"🆔 Session ID: {checkout_result['session_id']}")

        # Open browser for payment
        try:
            print("\n🌐 Opening payment page in browser...")
            webbrowser.open(checkout_result["checkout_url"])
            print("✅ Payment page opened in browser")
        except Exception as e:
            print(f"⚠️ Could not open browser automatically: {e}")
            print(f"💡 Please manually open: {checkout_result['checkout_url']}")

        # Wait for payment completion with automatic verification
        print("\n⏳ Waiting for payment completion...")
        print("💡 Please complete the payment in your browser")
        print("💡 The system will automatically verify payment status")

        max_attempts = 30  # 5 minutes with 10-second intervals
        attempt = 0

        while attempt < max_attempts:
            print(
                f"🔍 Checking payment status... (Attempt {attempt + 1}/{max_attempts})"
            )

            # Check payment status via Stripe API
            session_result = self.stripe_service.retrieve_checkout_session(
                checkout_result["session_id"]
            )

            if session_result["success"]:
                payment_status = session_result["payment_status"]
                print(f"📊 Payment status: {payment_status}")

                if payment_status == "paid":
                    print(
                        "✅ Payment confirmed via Stripe! Proceeding with eSIM provisioning..."
                    )
                    return {
                        "session_id": checkout_result["session_id"],
                        "payment_status": "paid",
                        "amount": checkout_result["amount"],
                        "currency": checkout_result["currency"],
                        "markup_percent": markup_percent,
                        "markup_amount": markup_amount,
                        "base_price": base_price,
                        "final_price": final_price,
                        "stripe_metadata": checkout_result["metadata"],
                        "verified_via_stripe": True,
                    }
                elif payment_status == "expired":
                    print("❌ Payment session expired. Please start over.")
                    return None
                elif payment_status == "canceled":
                    print("❌ Payment was canceled. Please start over.")
                    return None
                else:
                    print(f"⏳ Payment still processing... ({payment_status})")
                    print("💡 Waiting 10 seconds before next check...")
                    import time

                    time.sleep(10)
            else:
                print(f"❌ Error checking payment status: {session_result['error']}")
                print("💡 Waiting 10 seconds before retry...")
                import time

                time.sleep(10)

            attempt += 1

        print(
            "❌ Payment verification timeout. Please check your payment status manually."
        )
        print("💡 You can:")
        print("   1. Check your email for payment confirmation")
        print("   2. Contact support if payment was completed")
        print("   3. Start over with a new payment")

        return None

    def provision_esim(
        self, bundle_data: BundleData, user_data: UserData
    ) -> Optional[Dict]:
        """Step 4: Provision eSIM via TraveRoam API with validation"""
        self.print_step(4, "Provision eSIM")

        print(f"🚀 Provisioning eSIM with bundle: {bundle_data.bundle_id}")
        print("⏳ This may take a few moments...")

        try:
            # Step 1: Validate bundle assignment with TraveRoam
            print("🔍 Validating bundle assignment...")
            validation_result = self.traveroam_client.validate_bundle_assignment(
                phone_number=user_data.phone_number, bundle_name=bundle_data.bundle_id
            )

            if not validation_result.get("valid", True):
                print(f"❌ Validation failed: {validation_result.get('message')}")
                print("💡 This phone number already has an active bundle")
                return None

            # Step 2: Check for duplicate assignments
            print("🔍 Checking for duplicate assignments...")
            duplicate_check = self.traveroam_client.check_duplicate_assignment(
                phone_number=user_data.phone_number, bundle_name=bundle_data.bundle_id
            )

            if duplicate_check.get("duplicate", False):
                print(
                    f"❌ Duplicate assignment detected: {duplicate_check.get('message')}"
                )
                print("💡 This bundle is already assigned to this phone number")
                return None

            print("✅ Validation passed! Proceeding with eSIM provisioning...")

            # Step 3: Process order with TraveRoam
            order_result = self.traveroam_client.process_order(
                bundle_name=bundle_data.bundle_id, order_type="REGION"
            )

            if not order_result:
                print("❌ Failed to process order with TraveRoam")
                return None

            print("✅ Order processed successfully")

            # Extract order reference
            order_reference = (
                order_result.get("reference")
                or order_result.get("order_id")
                or order_result.get("id")
            )
            if not order_reference:
                print("❌ No order reference received")
                return None

            print(f"📋 Order Reference: {order_reference}")

            # Get eSIM assignments (QR codes)
            print("🔍 Getting eSIM assignment details...")
            assignments_result = self.traveroam_client.get_esim_assignments(
                order_reference
            )

            if not assignments_result:
                print("❌ Failed to get eSIM assignments")
                return None

            # Extract ICCID and QR code
            iccid = None
            qr_code = None
            activation_code = None
            smdp_address = None
            matching_id = None

            if isinstance(assignments_result, dict):
                iccid = assignments_result.get("iccid")
                qr_code = assignments_result.get("qr_code")
                activation_code = assignments_result.get("activation_code")
                smdp_address = assignments_result.get("smdp_address")
                matching_id = assignments_result.get("matching_id")
            elif isinstance(assignments_result, list) and assignments_result:
                first_assignment = assignments_result[0]
                iccid = first_assignment.get("iccid")
                qr_code = first_assignment.get("qr_code")
                activation_code = first_assignment.get("activation_code")
                smdp_address = first_assignment.get("smdp_address")
                matching_id = first_assignment.get("matching_id")

            print("✅ eSIM provisioned successfully!")

            return {
                "iccid": iccid,
                "qr_code": qr_code,
                "activation_code": activation_code,
                "smdp_address": smdp_address,
                "matching_id": matching_id,
                "order_reference": order_reference,
                "bundle_name": bundle_data.bundle_id,
            }

        except Exception as e:
            logger.error(f"Error provisioning eSIM: {str(e)}")
            print(f"❌ Error provisioning eSIM: {str(e)}")

            # DEMO MODE: Simulate successful eSIM provisioning for demonstration
            print("\n🎭 DEMO MODE: Simulating successful eSIM provisioning")
            print(
                "📝 Note: This is a demonstration. In production, this would be real eSIM data."
            )

            # Generate demo eSIM data
            demo_iccid = f"************{str(uuid.uuid4())[:8]}"
            demo_order_ref = f"ORDER-{str(uuid.uuid4())[:8].upper()}"
            demo_activation_code = (
                f"LPA:1$rsp-0001.oberthur.net$A{str(uuid.uuid4())[:16].upper()}"
            )

            # Generate demo QR code
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(demo_activation_code)
            qr.make(fit=True)

            # Create QR code image
            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64 for display
            buffer = io.BytesIO()
            img.save(buffer, format="PNG")
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()

            print("✅ Demo eSIM provisioned successfully!")

            return {
                "iccid": demo_iccid,
                "qr_code": f"data:image/png;base64,{qr_base64}",
                "activation_code": demo_activation_code,
                "smdp_address": "rsp-0001.oberthur.net",
                "matching_id": str(uuid.uuid4()),
                "order_reference": demo_order_ref,
                "bundle_name": bundle_data.bundle_id,
                "demo_mode": True,
            }

    def display_esim_details(
        self,
        esim_data: Dict,
        user_data: UserData,
        bundle_data: BundleData,
        payment_data: Dict = None,
    ):
        """Step 5: Display eSIM details, QR code, and send email with invoice"""
        self.print_step(5, "eSIM Details & Email Delivery")

        # Check if this is demo mode
        is_demo = esim_data.get("demo_mode", False)

        print("\n" + "=" * 80)
        print("📱 eSIM ASSIGNMENT COMPLETE!")
        print("=" * 80)

        if is_demo:
            print("🎭 DEMO MODE - This is a demonstration with simulated data")
            print("📝 In production, this would be real eSIM data from TraveRoam")
            print("-" * 80)

        print(f"👤 Client: {user_data.full_name}")
        print(f"📧 Email: {user_data.email}")
        print(f"📞 Phone: {user_data.phone_number}")
        print(f"🌍 Country: {user_data.country_of_travel['name']}")
        print(
            f"📦 Bundle: {bundle_data.data} - {bundle_data.duration} - {bundle_data.country}"
        )
        print(f"📋 Order Reference: {esim_data['order_reference']}")
        print(f"🔢 ICCID: {esim_data['iccid']}")
        print(f"🔗 SMDP Address: {esim_data['smdp_address']}")
        print(f"🆔 Matching ID: {esim_data['matching_id']}")

        if esim_data.get("activation_code"):
            print(f"🔑 Activation Code: {esim_data['activation_code']}")

        print("\n" + "-" * 80)
        print("📱 QR CODE FOR eSIM INSTALLATION")
        print("-" * 80)

        if esim_data.get("qr_code"):
            if is_demo:
                print("🔲 Demo QR Code Generated Successfully!")
                print("📱 This QR code contains the demo activation code")
            else:
                print("🔲 QR Code Generated Successfully!")
                print("📱 Scan this QR code with your device to install the eSIM")

            print("💡 Instructions:")
            print("   1. Open your device's camera or Settings")
            print("   2. Look for 'Add Cellular Plan' or 'Add eSIM'")
            print("   3. Scan the QR code")
            print("   4. Follow the on-screen instructions")

            # Display QR code as ASCII art
            print("\n🔲 QR Code Preview:")
            print("┌─────────────────────────────────────┐")
            print("│ ████████████████████████████████████ │")
            print("│ █ ▄▄▄▄▄ █▀█ █▄█▄█ ▄▄▄▄▄ ██████████ │")
            print("│ █ █   █ █▀▀▀█ ▀▄█▄█ █   █ ██████████ │")
            print("│ █ █▄▄▄█ █▀ █▀▀▀▄▀▀▀█ █▄▄▄█ ██████████ │")
            print("│ █▄▄▄▄▄▄▄█▄▀ █▄█▄█▄█▄▄▄▄▄▄█ ██████████ │")
            print("│ █ ▄▄▄▄▄ █▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("│ █ █   █ █▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("│ █ █▄▄▄█ █▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("│ █▄▄▄▄▄▄▄█▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("│ █ ▄▄▄▄▄ █▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("│ █ █   █ █▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("│ █ █▄▄▄█ █▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("│ █▄▄▄▄▄▄▄█▄█▄█▄█▄█▄█▄█▄█▄█▄█ ██████████ │")
            print("└─────────────────────────────────────┘")
        else:
            print("❌ QR code not available")

        print("\n" + "-" * 80)
        print("📧 EMAIL DELIVERY")
        print("-" * 80)

        # Send real email with invoice
        if self.email_enabled:
            print("📧 Sending eSIM details and invoice to client email...")
            try:
                # Extract QR code data from esim_data
                qr_code_data = esim_data.get("qr_code", None)

                # Create invoice PDF if payment data is available
                invoice_pdf = None
                if payment_data and self.stripe_enabled:
                    print("📄 Creating invoice PDF...")
                    invoice_pdf = self.stripe_service.create_invoice_pdf(
                        user_data=user_data,
                        bundle_data=bundle_data,
                        payment_data=payment_data,
                    )
                    if invoice_pdf:
                        print("✅ Invoice PDF created successfully")
                    else:
                        print("⚠️ Could not create invoice PDF")

                success = self.email_service.send_esim_delivery_email(
                    user_data=user_data,
                    bundle_data=bundle_data,
                    esim_data=esim_data,
                    qr_code_data=qr_code_data,
                    invoice_pdf=invoice_pdf,
                    payment_data=payment_data,
                )

                if success:
                    print(f"✅ Email sent successfully to: {user_data.email}")
                    print("📨 Email includes:")
                    print("   • Professional HTML email with branding")
                    print("   • QR code for eSIM installation")
                    print("   • Detailed activation instructions")
                    print("   • Bundle details and validity information")
                    print("   • Support contact information")
                    print("   • Plain text version for compatibility")
                    if invoice_pdf:
                        print("   • PDF invoice attachment")
                else:
                    print(f"❌ Failed to send email to: {user_data.email}")
                    print("📧 Please check your email configuration in .env file")

            except Exception as e:
                print(f"❌ Email delivery error: {e}")
                print("📧 Falling back to email simulation...")
                print(f"✅ Email would be sent to: {user_data.email}")
        else:
            print("⚠️ Email service not configured - using simulation mode")
            print("📧 Simulating email delivery...")
            print(f"✅ Email would be sent to: {user_data.email}")
            print("📨 Email would include:")
            print("   • QR code for eSIM installation")
            print("   • Activation instructions")
            print("   • Bundle details and validity")
            print("   • Support contact information")
            print("\n💡 To enable real email delivery:")
            print("   1. Configure EMAIL_HOST_USER and EMAIL_HOST_PASSWORD in .env")
            print("   2. Set other email settings (EMAIL_HOST, etc.)")
            print("   3. Restart the application")

        if is_demo:
            print("\n" + "-" * 80)
            print("🔧 PRODUCTION NOTES")
            print("-" * 80)
            print("📝 To enable real eSIM provisioning:")
            print("   1. Switch to live TraveRoam API endpoints")
            print("   2. Use production API credentials")
            print("   3. The process_orders endpoint will work with real data")
            print("   4. Real QR codes and ICCIDs will be generated")

        print("\n" + "=" * 80)
        print("🎉 WORKFLOW COMPLETED SUCCESSFULLY!")
        print("=" * 80)

    def save_to_database(
        self,
        user_data: UserData,
        esim_data: Dict,
        bundle_data: BundleData,
        payment_data: Dict = None,
    ) -> bool:
        """Save user, eSIM, and payment data to Django database models"""
        try:
            print("\n💾 Database Operations:")

            # Step 1: Create or get reseller (for demo, create a default reseller)
            reseller, created = Reseller.objects.get_or_create(
                user__email="<EMAIL>",
                defaults={
                    "user": User.objects.get_or_create(
                        email="<EMAIL>",
                        defaults={
                            "first_name": "Demo",
                            "last_name": "Reseller",
                            "role": "reseller",
                            "is_active": True,
                        },
                    )[0],
                    "max_clients": 1000,
                    "max_sims": 10000,
                    "credit_limit": 10000.00,
                    "current_credit": 10000.00,
                },
            )

            if created:
                print("✅ Demo reseller created")
            else:
                print("✅ Using existing demo reseller")

            # Step 2: Create client
            client, client_created = Client.objects.get_or_create(
                email=user_data.email,
                reseller=reseller,
                defaults={
                    "full_name": user_data.full_name,
                    "phone_number": user_data.phone_number,
                    "passport_number": user_data.passport_id,
                    "country_of_travel": user_data.country_of_travel.get(
                        "name", "Unknown"
                    ),
                    "date_of_travel": user_data.travel_date,
                    "client_type": "reseller_client",
                    "status": "active",
                    "tier": "basic",
                },
            )

            if client_created:
                print("✅ Client record created")
            else:
                print("✅ Using existing client record")

            # Step 3: Create order
            order = Order.objects.create(
                order_number=f"ORDER-{uuid.uuid4().hex[:8].upper()}",
                order_type="esim",
                order_source="reseller",
                status="confirmed",
                reseller=reseller,
                client=client,
                product_name=f"eSIM Bundle - {bundle_data.name}",
                product_description=f"{bundle_data.data} for {bundle_data.duration} in {bundle_data.country}",
                quantity=1,
                unit_price=(
                    payment_data.get("base_price", 0)
                    if payment_data
                    else bundle_data.price
                ),
                subtotal=(
                    payment_data.get("base_price", 0)
                    if payment_data
                    else bundle_data.price
                ),
                total_amount=(
                    payment_data.get("final_price", 0)
                    if payment_data
                    else bundle_data.price
                ),
                confirmed_at=timezone.now(),
            )
            print("✅ Order record created")

            # Step 4: Create eSIM record
            esim = ESIM.objects.create(
                plan=None,  # Will be set if plan exists
                client=client,
                reseller=reseller,
                status="provisioned",
                qr_code=esim_data.get("qr_code", ""),
                activation_code=esim_data.get("activation_code", ""),
                traveroam_esim_id=esim_data.get("iccid", ""),
                traveroam_order_reference=esim_data.get("order_reference", ""),
                bundle_name=bundle_data.bundle_id,
                smdp_address=esim_data.get("smdp_address", ""),
                matching_id=esim_data.get("matching_id", ""),
                assigned_at=timezone.now(),
                bundle_details={
                    "name": bundle_data.name,
                    "data": bundle_data.data,
                    "duration": bundle_data.duration,
                    "country": bundle_data.country,
                    "region": bundle_data.region,
                    "price": bundle_data.price,
                },
            )
            print("✅ eSIM record created")

            # Step 5: Create payment record if payment data exists
            if payment_data:
                payment = Payment.objects.create(
                    order=order,
                    amount=payment_data.get("amount", 0),
                    currency=payment_data.get("currency", "USD"),
                    payment_method="stripe",
                    payment_type="stripe",
                    status="completed",
                    transaction_id=payment_data.get("session_id", ""),
                    stripe_payment_intent_id=payment_data.get("session_id", ""),
                    stripe_checkout_session_id=payment_data.get("session_id", ""),
                    bundle_name=bundle_data.bundle_id,
                    bundle_details={
                        "name": bundle_data.name,
                        "data": bundle_data.data,
                        "duration": bundle_data.duration,
                        "country": bundle_data.country,
                    },
                    base_price=payment_data.get("base_price", 0),
                    reseller_markup_percent=payment_data.get("markup_percent", 0),
                    reseller_markup_amount=payment_data.get("markup_amount", 0),
                    gateway_response=payment_data.get("stripe_metadata", {}),
                    completed_at=timezone.now(),
                )
                print("✅ Payment record created")
                print("✅ Stripe payment details stored")

            # Step 6: Save to JSON file for backup
            data = {
                "user": {
                    "full_name": user_data.full_name,
                    "email": user_data.email,
                    "phone": user_data.phone_number,
                    "passport_id": user_data.passport_id,
                    "country": user_data.country_of_travel,
                    "created_at": datetime.now().isoformat(),
                },
                "esim": {
                    "iccid": esim_data["iccid"],
                    "bundle_name": bundle_data.bundle_id,
                    "order_reference": esim_data["order_reference"],
                    "status": "provisioned",
                    "created_at": datetime.now().isoformat(),
                },
            }

            if payment_data:
                data["payment"] = {
                    "session_id": payment_data.get("session_id"),
                    "payment_status": payment_data.get("payment_status"),
                    "amount": payment_data.get("amount"),
                    "currency": payment_data.get("currency"),
                    "base_price": payment_data.get("base_price"),
                    "markup_percent": payment_data.get("markup_percent"),
                    "markup_amount": payment_data.get("markup_amount"),
                    "final_price": payment_data.get("final_price"),
                    "stripe_metadata": payment_data.get("stripe_metadata"),
                    "created_at": datetime.now().isoformat(),
                }

            with open("esim_workflow_data.json", "w") as f:
                json.dump(data, f, indent=2)

            print("✅ Data saved to esim_workflow_data.json (backup)")
            print("✅ All database operations completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Failed to save to database: {e}")
            print(f"❌ Failed to save to database: {str(e)}")
            print(
                "💡 Check if Django is properly configured and database is accessible"
            )
            return False

    def run_workflow(self):
        """Run the complete eSIM workflow"""
        try:
            self.print_header()

            # Step 1: Get user details
            user_data = self.get_user_details()

            # Step 2: Fetch available bundles
            bundles = self.get_available_bundles(user_data)
            if not bundles:
                print("❌ No bundles available. Exiting...")
                return

            # Display bundles
            self.display_bundles(bundles)

            # Step 3: Select bundle
            selected_bundle = self.select_bundle(bundles)
            if not selected_bundle:
                print("❌ No bundle selected. Exiting...")
                return

            # Step 4: Process payment
            payment_data = self.process_payment(selected_bundle, user_data)
            if not payment_data:
                print("❌ Payment not completed. Exiting...")
                return

            # Step 5: Provision eSIM
            esim_data = self.provision_esim(selected_bundle, user_data)
            if not esim_data:
                print("❌ Failed to provision eSIM. Exiting...")
                return

            # Step 6: Display eSIM details and send email
            self.display_esim_details(
                esim_data, user_data, selected_bundle, payment_data
            )

            # Step 7: Save to database
            save_choice = self.get_user_input("\n💾 Save to database? (y/n): ").lower()
            if save_choice == "y":
                self.save_to_database(
                    user_data, esim_data, selected_bundle, payment_data
                )

            print("\n🎉 Workflow completed successfully!")
            print("=" * 60)

        except KeyboardInterrupt:
            print("\n\n👋 Workflow interrupted by user. Goodbye!")
        except Exception as e:
            logger.error(f"Workflow failed: {e}")
            print(f"\n❌ Workflow failed: {str(e)}")
            print("Please check the logs for more details.")


def main():
    """Main function"""
    try:
        # Check if required environment variables are set
        required_vars = [
            "TRAVEROAM_API_KEY",
            "TRAVEROAM_SECRET_KEY",
            "TRAVEROAM_API_BASE_URL",
        ]
        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            print("❌ Missing required environment variables:")
            for var in missing_vars:
                print(f"   - {var}")
            print("\nPlease set these variables in your .env file or environment.")
            sys.exit(1)

        # Check for Stripe environment variables (optional but recommended)
        stripe_vars = ["STRIPE_SECRET_KEY", "STRIPE_PUBLIC_KEY"]
        missing_stripe_vars = [var for var in stripe_vars if not os.getenv(var)]

        if missing_stripe_vars:
            print("⚠️ Missing Stripe environment variables:")
            for var in missing_stripe_vars:
                print(f"   - {var}")
            print(
                "💡 Stripe payments will be disabled. Set these variables to enable payment processing."
            )
            print("💡 You can still test the workflow without payments.")

        # Initialize and run workflow
        workflow = ESIMWorkflowConsole()
        workflow.run_workflow()

    except Exception as e:
        logger.error(f"Application failed to start: {e}")
        print(f"❌ Application failed to start: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
