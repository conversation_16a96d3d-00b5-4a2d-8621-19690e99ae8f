import logging

from celery import shared_task
from django.conf import settings
from django.contrib.sessions.models import Session
from django.db import transaction
from django.utils import timezone

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def cleanup_expired_sessions(self):
    """
    Clean up expired sessions with proper error handling and monitoring.

    This task:
    - Removes expired sessions from the database
    - Handles database errors gracefully
    - Provides detailed logging
    - Implements retry logic for failures
    - Reports cleanup statistics
    """
    try:
        # Get current time for comparison
        now = timezone.now()

        # Count sessions before cleanup for reporting
        total_sessions_before = Session.objects.count()

        # Find expired sessions
        expired_sessions = Session.objects.filter(expire_date__lt=now)
        expired_count = expired_sessions.count()

        if expired_count == 0:
            logger.info("No expired sessions found to cleanup")
            return {
                "status": "success",
                "message": "No expired sessions found",
                "sessions_removed": 0,
                "total_sessions": total_sessions_before,
                "timestamp": now.isoformat(),
            }

        # Use transaction to ensure atomic operation
        with transaction.atomic():
            # Delete expired sessions
            deleted_count = expired_sessions.delete()[0]

            # Count remaining sessions
            total_sessions_after = Session.objects.count()

        # Log the cleanup operation
        logger.info(
            f"Session cleanup completed: {deleted_count} expired sessions removed. "
            f"Total sessions: {total_sessions_before} -> {total_sessions_after}"
        )

        # Return detailed results
        return {
            "status": "success",
            "message": f"Successfully removed {deleted_count} expired sessions",
            "sessions_removed": deleted_count,
            "total_sessions_before": total_sessions_before,
            "total_sessions_after": total_sessions_after,
            "timestamp": now.isoformat(),
        }

    except Exception as exc:
        logger.error(f"Session cleanup failed: {str(exc)}", exc_info=True)

        # Retry logic for transient failures
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying session cleanup in {self.default_retry_delay} seconds..."
            )
            raise self.retry(exc=exc)
        else:
            logger.error("Session cleanup failed after maximum retries")
            return {
                "status": "error",
                "message": f"Session cleanup failed after {self.max_retries} retries: {str(exc)}",
                "error": str(exc),
                "timestamp": timezone.now().isoformat(),
            }


@shared_task
def cleanup_old_password_reset_tokens():
    """
    Clean up expired password reset tokens.

    This task removes password reset tokens that have expired
    to keep the database clean and maintain security.
    """
    try:
        from .models import PasswordResetToken

        now = timezone.now()

        # Count tokens before cleanup
        total_tokens_before = PasswordResetToken.objects.count()

        # Find expired tokens
        expired_tokens = PasswordResetToken.objects.filter(expires_at__lt=now)
        expired_count = expired_tokens.count()

        if expired_count == 0:
            logger.info("No expired password reset tokens found")
            return {
                "status": "success",
                "message": "No expired password reset tokens found",
                "tokens_removed": 0,
                "total_tokens": total_tokens_before,
                "timestamp": now.isoformat(),
            }

        # Use transaction for atomic operation
        with transaction.atomic():
            deleted_count = expired_tokens.delete()[0]
            total_tokens_after = PasswordResetToken.objects.count()

        logger.info(
            f"Password reset token cleanup completed: {deleted_count} expired tokens removed. "
            f"Total tokens: {total_tokens_before} -> {total_tokens_after}"
        )

        return {
            "status": "success",
            "message": f"Successfully removed {deleted_count} expired password reset tokens",
            "tokens_removed": deleted_count,
            "total_tokens_before": total_tokens_before,
            "total_tokens_after": total_tokens_after,
            "timestamp": now.isoformat(),
        }

    except Exception as exc:
        logger.error(f"Password reset token cleanup failed: {str(exc)}", exc_info=True)
        return {
            "status": "error",
            "message": f"Password reset token cleanup failed: {str(exc)}",
            "error": str(exc),
            "timestamp": timezone.now().isoformat(),
        }


@shared_task
def cleanup_inactive_users():
    """
    Clean up inactive users who haven't logged in for a long time.

    This task identifies and optionally deactivates users who haven't
    logged in for an extended period (configurable via settings).
    """
    try:
        from .models import User

        # Get the inactivity threshold from settings (default: 90 days)
        inactivity_days = getattr(settings, "USER_INACTIVITY_DAYS", 90)
        cutoff_date = timezone.now() - timezone.timedelta(days=inactivity_days)

        # Find inactive users (those who haven't logged in since cutoff_date)
        inactive_users = User.objects.filter(
            last_login__lt=cutoff_date,
            is_active=True,
            role__in=["public_user", "client"],  # Don't deactivate admins/resellers
        )

        inactive_count = inactive_users.count()

        if inactive_count == 0:
            logger.info("No inactive users found to deactivate")
            return {
                "status": "success",
                "message": "No inactive users found",
                "users_deactivated": 0,
                "timestamp": timezone.now().isoformat(),
            }

        # Deactivate users in a transaction
        with transaction.atomic():
            deactivated_count = inactive_users.update(is_active=False)

        logger.info(
            f"User deactivation completed: {deactivated_count} inactive users deactivated. "
            f"Inactivity threshold: {inactivity_days} days"
        )

        return {
            "status": "success",
            "message": f"Successfully deactivated {deactivated_count} inactive users",
            "users_deactivated": deactivated_count,
            "inactivity_threshold_days": inactivity_days,
            "timestamp": timezone.now().isoformat(),
        }

    except Exception as exc:
        logger.error(f"User deactivation failed: {str(exc)}", exc_info=True)
        return {
            "status": "error",
            "message": f"User deactivation failed: {str(exc)}",
            "error": str(exc),
            "timestamp": timezone.now().isoformat(),
        }
