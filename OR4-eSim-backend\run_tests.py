#!/usr/bin/env python3
"""
Test runner script for the eSIM Management System
Run this script to execute all tests with proper configuration
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def setup_environment():
    """Setup the Django environment for testing"""
    # Add the project directory to Python path
    project_dir = Path(__file__).parent
    sys.path.insert(0, str(project_dir))

    # Set Django settings module
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "esim_project.settings")

    # Import Django and setup
    import django

    django.setup()


def run_tests(args):
    """Run the tests with the specified arguments"""
    setup_environment()

    # Build pytest command
    cmd = [
        "python",
        "-m",
        "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--strict-markers",
        "--disable-warnings",
    ]

    # Add coverage if requested
    if args.coverage:
        cmd.extend(
            [
                "--cov=.",
                "--cov-report=html",
                "--cov-report=term-missing",
                "--cov-fail-under=80",
            ]
        )

    # Add specific test file if provided
    if args.test_file:
        cmd.append(args.test_file)

    # Add specific test class if provided
    if args.test_class:
        cmd.append(f"-k {args.test_class}")

    # Add specific test function if provided
    if args.test_function:
        cmd.append(f"-k {args.test_function}")

    # Add markers if provided
    if args.markers:
        cmd.append(f"-m {args.markers}")

    # Add parallel execution if requested
    if args.parallel:
        cmd.extend(["-n", "auto"])

    # Add verbose output if requested
    if args.verbose:
        cmd.append("-vv")

    # Add stop on first failure if requested
    if args.stop_on_failure:
        cmd.append("-x")

    # Add database reuse if requested
    if args.reuse_db:
        cmd.append("--reuse-db")

    # Add no migrations if requested
    if args.no_migrations:
        cmd.append("--nomigrations")

    print(f"Running tests with command: {' '.join(cmd)}")
    print("-" * 80)

    try:
        result = subprocess.run(cmd, check=True)
        print("-" * 80)
        print("✅ All tests passed successfully!")
        return 0
    except subprocess.CalledProcessError as e:
        print("-" * 80)
        print(f"❌ Tests failed with exit code: {e.returncode}")
        return e.returncode


def main():
    """Main function to parse arguments and run tests"""
    parser = argparse.ArgumentParser(
        description="Run tests for the eSIM Management System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run all tests
  python run_tests.py --coverage         # Run with coverage report
  python run_tests.py tests/test_api_views.py  # Run specific test file
  python run_tests.py -k TestUserViews   # Run specific test class
  python run_tests.py -k test_user_create  # Run specific test function
  python run_tests.py -m "not slow"      # Run tests excluding slow markers
  python run_tests.py --parallel         # Run tests in parallel
  python run_tests.py --verbose          # Verbose output
        """,
    )

    parser.add_argument(
        "test_file",
        nargs="?",
        help="Specific test file to run (e.g., tests/test_api_views.py)",
    )

    parser.add_argument(
        "-k",
        "--test-class",
        help="Run tests matching the given substring expression for class names",
    )

    parser.add_argument(
        "-f",
        "--test-function",
        help="Run tests matching the given substring expression for function names",
    )

    parser.add_argument(
        "-m", "--markers", help="Only run tests matching given marker expression"
    )

    parser.add_argument(
        "--coverage", action="store_true", help="Generate coverage report"
    )

    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")

    parser.add_argument("--verbose", action="store_true", help="Verbose output")

    parser.add_argument(
        "--stop-on-failure", action="store_true", help="Stop on first failure"
    )

    parser.add_argument("--reuse-db", action="store_true", help="Reuse test database")

    parser.add_argument(
        "--no-migrations", action="store_true", help="Skip database migrations"
    )

    args = parser.parse_args()

    # Run the tests
    exit_code = run_tests(args)
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
