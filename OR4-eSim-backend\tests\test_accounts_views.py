import pytest
from django.urls import reverse
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.models import User, UserProfile


class TestSignupView:
    """Test cases for user signup view"""

    @pytest.mark.django_db
    def test_signup_success_json(self, api_client, mock_firebase_storage):
        """Test successful signup with JSON data"""
        url = reverse("signup")
        data = {
            "email": "<EMAIL>",
            "password": "Test@123",
            "confirm_password": "Test@123",
            "first_name": "Test",
            "last_name": "User",
            "role": "public_user",
        }

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert response.data["message"] == "User registered successfully"
        # JSON-only signup doesn't return user_id in data

        # Verify user was created
        user = User.objects.get(email="<EMAIL>")
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.role == "public_user"

    @pytest.mark.django_db
    def test_signup_success_with_image(
        self, api_client, test_image, mock_firebase_storage
    ):
        """Test successful signup with profile image"""
        url = reverse("signup")
        data = {
            "email": "<EMAIL>",
            "password": "Test@123",
            "confirm_password": "Test@123",
            "first_name": "Test",
            "last_name": "User",
            "role": "public_user",
            "profile_image": test_image,
        }

        response = api_client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "profile_image_url" in response.data["data"]

        # Verify user profile was created with image
        user = User.objects.get(email="<EMAIL>")
        profile = UserProfile.objects.get(user=user)
        assert profile.profile_image_url is not None

    @pytest.mark.django_db
    def test_signup_duplicate_email(self, api_client, user_factory):
        """Test signup with existing email"""
        # Create existing user
        user_factory(email="<EMAIL>")

        url = reverse("signup")
        data = {
            "email": "<EMAIL>",
            "password": "Test@123",
            "confirm_password": "Test@123",
            "first_name": "Test",
            "last_name": "User",
            "role": "public_user",
        }

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False
        assert "email" in response.data["errors"]

    @pytest.mark.django_db
    def test_signup_invalid_data(self, api_client):
        """Test signup with invalid data"""
        url = reverse("signup")
        data = {
            "email": "invalid-email",
            "password": "short",
            "confirm_password": "different",
            "first_name": "",
            "last_name": "",
            "role": "invalid_role",
        }

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False
        assert "errors" in response.data

    @pytest.mark.django_db
    def test_signup_invalid_image_type(self, api_client, mock_firebase_storage):
        """Test signup with invalid image type"""
        from django.core.files.uploadedfile import SimpleUploadedFile

        invalid_image = SimpleUploadedFile(
            name="test.txt", content=b"invalid content", content_type="text/plain"
        )

        url = reverse("signup")
        data = {
            "email": "<EMAIL>",
            "password": "Test@123",
            "confirm_password": "Test@123",
            "first_name": "Test",
            "last_name": "User",
            "role": "public_user",
            "profile_image": invalid_image,
        }

        response = api_client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid file type" in response.data["message"]


class TestLoginView:
    """Test cases for user login view"""

    @pytest.mark.django_db
    def test_login_success(self, api_client, user_factory):
        """Test successful login"""
        # Create user
        user = user_factory(email="<EMAIL>")
        user.set_password("Test@123")
        user.save()

        url = reverse("login")
        data = {"email": "<EMAIL>", "password": "Test@123"}

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "tokens" in response.data["data"]
        assert "user" in response.data["data"]
        assert response.data["data"]["user"]["email"] == "<EMAIL>"

    @pytest.mark.django_db
    def test_login_invalid_credentials(self, api_client):
        """Test login with invalid credentials"""
        url = reverse("login")
        data = {"email": "<EMAIL>", "password": "wrongpassword"}

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.data["success"] is False

    @pytest.mark.django_db
    def test_login_inactive_user(self, api_client, user_factory):
        """Test login with inactive user"""
        user = user_factory(email="<EMAIL>", is_active=False)
        user.set_password("Test@123")
        user.save()

        url = reverse("login")
        data = {"email": "<EMAIL>", "password": "Test@123"}

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.data["success"] is False


class TestLogoutView:
    """Test cases for user logout view"""

    @pytest.mark.django_db
    def test_logout_success(self, authenticated_client):
        """Test successful logout"""
        client, user = authenticated_client

        url = reverse("logout")
        data = {"refresh_token": "dummy_refresh_token"}

        response = client.post(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True


class TestEditProfileView:
    """Test cases for edit profile view"""

    @pytest.mark.django_db
    def test_edit_profile_success(self, authenticated_client, mock_firebase_storage):
        """Test successful profile edit"""
        client, user = authenticated_client

        url = reverse("edit-profile", kwargs={"email": user.email})
        data = {"first_name": "Updated", "last_name": "Name", "city": "New York"}

        response = client.patch(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert response.data["data"]["user"]["first_name"] == "Updated"
        assert response.data["data"]["user"]["last_name"] == "Name"

    @pytest.mark.django_db
    def test_edit_profile_with_image(
        self, authenticated_client, test_image, mock_firebase_storage
    ):
        """Test profile edit with image upload"""
        client, user = authenticated_client

        url = reverse("edit-profile", kwargs={"email": user.email})
        data = {"first_name": "Updated", "profile_image": test_image}

        response = client.patch(url, data, format="multipart")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

    @pytest.mark.django_db
    def test_edit_profile_unauthorized(self, api_client, user_factory):
        """Test edit profile without authentication"""
        user = user_factory()

        url = reverse("edit-profile", kwargs={"email": user.email})
        data = {"first_name": "Updated"}

        response = api_client.patch(url, data, format="json")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.django_db
    def test_edit_profile_permission_denied(self, authenticated_client, user_factory):
        """Test edit profile for different user (should be denied)"""
        client, user = authenticated_client
        other_user = user_factory(email="<EMAIL>")

        url = reverse("edit-profile", kwargs={"email": other_user.email})
        data = {"first_name": "Updated"}

        response = client.patch(url, data, format="json")

        assert response.status_code == status.HTTP_403_FORBIDDEN

    @pytest.mark.django_db
    def test_edit_profile_admin_can_edit_any(self, admin_client, user_factory):
        """Test admin can edit any user's profile"""
        client, admin_user = admin_client
        regular_user = user_factory(email="<EMAIL>")

        url = reverse("edit-profile", kwargs={"email": regular_user.email})
        data = {"first_name": "Admin Updated"}

        response = client.patch(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

    @pytest.mark.django_db
    def test_edit_profile_user_not_found(self, authenticated_client):
        """Test edit profile for non-existent user"""
        client, user = authenticated_client

        url = reverse("edit-profile", kwargs={"email": "<EMAIL>"})
        data = {"first_name": "Updated"}

        response = client.patch(url, data, format="json")

        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestUserProfileView:
    """Test cases for user profile view"""

    @pytest.mark.django_db
    def test_user_profile_success(self, authenticated_client):
        """Test successful profile retrieval"""
        client, user = authenticated_client

        url = reverse("user-profile")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert response.data["data"]["user"]["email"] == user.email

    @pytest.mark.django_db
    def test_user_profile_unauthorized(self, api_client):
        """Test profile retrieval without authentication"""
        url = reverse("user-profile")

        response = api_client.get(url)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestPasswordResetViews:
    """Test cases for password reset views"""

    @pytest.mark.django_db
    def test_password_reset_request_success(self, api_client, user_factory):
        """Test successful password reset request"""
        user = user_factory(email="<EMAIL>")

        url = reverse("password-reset-request")
        data = {"email": "<EMAIL>"}

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

    @pytest.mark.django_db
    def test_password_reset_confirm_success(self, api_client, user_factory):
        """Test successful password reset confirmation"""
        user = user_factory(email="<EMAIL>")

        url = reverse("password-reset-confirm")
        data = {
            "email": "<EMAIL>",
            "new_password": "NewPassword@123",
            "confirm_password": "NewPassword@123",
        }

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

        # Verify password was changed
        user.refresh_from_db()
        assert user.check_password("NewPassword@123")

    @pytest.mark.django_db
    def test_password_reset_confirm_passwords_dont_match(
        self, api_client, user_factory
    ):
        """Test password reset with non-matching passwords"""
        user_factory(email="<EMAIL>")

        url = reverse("password-reset-confirm")
        data = {
            "email": "<EMAIL>",
            "new_password": "NewPassword@123",
            "confirm_password": "DifferentPassword@123",
        }

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False


class TestTokenViews:
    """Test cases for token-related views"""

    @pytest.mark.django_db
    def test_refresh_token_success(self, api_client, user_factory):
        """Test successful token refresh"""
        user = user_factory()
        refresh = RefreshToken.for_user(user)

        url = reverse("refresh-token")
        data = {"refresh_token": str(refresh)}

        response = api_client.post(url, data, format="json")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "access" in response.data["data"]

    @pytest.mark.django_db
    def test_verify_token_success(self, authenticated_client):
        """Test successful token verification"""
        client, user = authenticated_client

        url = reverse("verify_token")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert response.data["data"]["user"]["email"] == user.email

    @pytest.mark.django_db
    def test_verify_token_invalid(self, api_client):
        """Test token verification with invalid token"""
        url = reverse("verify_token")

        response = api_client.get(url)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED
