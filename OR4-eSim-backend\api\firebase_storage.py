import mimetypes
import os
import uuid
from datetime import datetime
from typing import Optional, <PERSON>ple

import firebase_admin
from decouple import config
from django.conf import settings
from firebase_admin import credentials, storage


class FirebaseStorage:
    """
    Utility class for handling Firebase Storage operations
    """

    def __init__(self):
        self.bucket_name = config("FIREBASE_STORAGE_BUCKET", default="")
        self._initialized = False
        self._bucket = None
        self._initialize_firebase()

    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            # Check if Firebase is already initialized
            firebase_admin.get_app()
            self._initialized = True
            print("✅ Firebase already initialized")
        except ValueError:
            # Check if Firebase credentials are configured
            project_id = config("FIREBASE_PROJECT_ID", default="")
            if not project_id:
                print("⚠️  Firebase not configured - image uploads will be disabled")
                return

            try:
                # Initialize Firebase with service account credentials
                cred_dict = {
                    "type": config("FIREBASE_TYPE", default="service_account"),
                    "project_id": config("FIREBASE_PROJECT_ID"),
                    "private_key_id": config("FIREBASE_PRIVATE_KEY_ID"),
                    "private_key": config("FIREBASE_PRIVATE_KEY", default="").replace(
                        "\\n", "\n"
                    ),
                    "client_email": config("FIREBASE_CLIENT_EMAIL"),
                    "client_id": config("FIREBASE_CLIENT_ID"),
                    "auth_uri": config(
                        "FIREBASE_AUTH_URI",
                        default="https://accounts.google.com/o/oauth2/auth",
                    ),
                    "token_uri": config(
                        "FIREBASE_TOKEN_URI",
                        default="https://oauth2.googleapis.com/token",
                    ),
                    "auth_provider_x509_cert_url": config(
                        "FIREBASE_AUTH_PROVIDER_X509_CERT_URL",
                        default="https://www.googleapis.com/oauth2/v1/certs",
                    ),
                    "client_x509_cert_url": config(
                        "FIREBASE_CLIENT_X509_CERT_URL", default=""
                    ),
                }

                # Check if all required fields are present
                required_fields = [
                    "project_id",
                    "private_key_id",
                    "private_key",
                    "client_email",
                    "client_id",
                ]
                missing_fields = [
                    field for field in required_fields if not cred_dict[field]
                ]

                if missing_fields:
                    print(
                        f"⚠️  Missing Firebase credentials: {', '.join(missing_fields)}"
                    )
                    print("⚠️  Image uploads will be disabled")
                    return

                cred = credentials.Certificate(cred_dict)
                firebase_admin.initialize_app(cred, {"storageBucket": self.bucket_name})
                self._initialized = True
                print("✅ Firebase initialized successfully")

                # Test bucket access
                self._test_bucket_access()

            except Exception as e:
                print(f"❌ Firebase initialization failed: {str(e)}")
                print("⚠️  Image uploads will be disabled")
                # Don't raise the exception, just disable Firebase functionality

    def _test_bucket_access(self):
        """Test if the storage bucket exists and is accessible"""
        try:
            bucket = storage.bucket()
            # Try to list blobs (this will fail if bucket doesn't exist)
            list(bucket.list_blobs(max_results=1))
            self._bucket = bucket
            print(f"✅ Firebase Storage bucket '{self.bucket_name}' is accessible")
        except Exception as e:
            print(
                f"❌ Firebase Storage bucket '{self.bucket_name}' is not accessible: {str(e)}"
            )
            print("\n🔧 To fix this issue:")
            print("1. Go to Firebase Console: https://console.firebase.google.com")
            print("2. Select your project: or4esim-a31aa")
            print("3. Go to Storage in the left sidebar")
            print("4. Click 'Get Started' to enable Firebase Storage")
            print("5. Choose a location for your storage bucket")
            print("6. Set up security rules (you can use test mode for now)")
            print("\n📝 Alternative bucket names to try:")
            print(f"   - or4esim-a31aa.appspot.com")
            print(f"   - or4esim-a31aa.firebaseapp.com")
            print(f"   - or4esim-a31aa-default-rtdb.firebaseio.com")
            self._initialized = False

    def upload_image(
        self, image_file, folder_path: str = "images", filename: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        Upload an image to Firebase Storage

        Args:
            image_file: The image file to upload
            folder_path: The folder path in Firebase Storage
            filename: Optional custom filename

        Returns:
            Tuple of (public_url, file_path)
        """
        if not self._initialized or not self._bucket:
            raise Exception(
                "Firebase Storage not available. Please enable Firebase Storage in your project."
            )

        try:
            # Generate unique filename if not provided
            if not filename:
                file_extension = self._get_file_extension(image_file.name)
                filename = f"{uuid.uuid4().hex}{file_extension}"

            # Create full path
            file_path = f"{folder_path}/{filename}"

            # Get blob
            blob = self._bucket.blob(file_path)

            # Set content type
            content_type = self._get_content_type(image_file.name)
            blob.content_type = content_type

            # Upload file
            blob.upload_from_file(image_file, content_type=content_type)

            # Make blob publicly accessible
            blob.make_public()

            # Get public URL
            public_url = blob.public_url

            return public_url, file_path

        except Exception as e:
            error_msg = str(e)
            if "bucket does not exist" in error_msg.lower():
                raise Exception(
                    f"Firebase Storage bucket '{self.bucket_name}' does not exist. Please enable Firebase Storage in your Firebase Console."
                )
            elif "permission" in error_msg.lower():
                raise Exception(
                    f"Permission denied. Please check Firebase Storage security rules."
                )
            else:
                raise Exception(f"Failed to upload image to Firebase: {error_msg}")

    def delete_image(self, file_path: str) -> bool:
        """
        Delete an image from Firebase Storage

        Args:
            file_path: The file path in Firebase Storage

        Returns:
            True if successful, False otherwise
        """
        if not self._initialized or not self._bucket:
            print("Firebase not initialized. Cannot delete image.")
            return False

        try:
            blob = self._bucket.blob(file_path)
            blob.delete()
            return True
        except Exception as e:
            print(f"Failed to delete image from Firebase: {str(e)}")
            return False

    def get_image_url(self, file_path: str) -> str:
        """
        Get the public URL of an image

        Args:
            file_path: The file path in Firebase Storage

        Returns:
            Public URL of the image
        """
        if not self._initialized or not self._bucket:
            raise Exception(
                "Firebase not initialized. Please configure Firebase credentials."
            )

        try:
            blob = self._bucket.blob(file_path)
            return blob.public_url
        except Exception as e:
            raise Exception(f"Failed to get image URL: {str(e)}")

    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename"""
        return os.path.splitext(filename)[1]

    def _get_content_type(self, filename: str) -> str:
        """Get content type from filename"""
        content_type, _ = mimetypes.guess_type(filename)
        return content_type or "application/octet-stream"

    def upload_profile_image(
        self, image_file, user_id: int, user_full_name: str = None
    ) -> Tuple[str, str]:
        """Upload profile image for a user"""
        # Clean the full name for filename (remove special characters, spaces)
        if user_full_name:
            clean_name = "".join(
                c for c in user_full_name if c.isalnum() or c in (" ", "-", "_")
            ).rstrip()
            clean_name = clean_name.replace(" ", "_")
        else:
            clean_name = "user"

        folder_path = f"users/{user_id}"
        filename = f"{user_id}_{clean_name}{self._get_file_extension(image_file.name)}"
        return self.upload_image(image_file, folder_path, filename)

    def upload_document_image(
        self, image_file, document_type: str, user_id: int, user_full_name: str = None
    ) -> Tuple[str, str]:
        """Upload document image (ID, passport, etc.)"""
        # Clean the full name for filename
        if user_full_name:
            clean_name = "".join(
                c for c in user_full_name if c.isalnum() or c in (" ", "-", "_")
            ).rstrip()
            clean_name = clean_name.replace(" ", "_")
        else:
            clean_name = "user"

        folder_path = f"documents/{document_type}/{user_id}"
        filename = f"{user_id}_{clean_name}_{document_type}{self._get_file_extension(image_file.name)}"
        return self.upload_image(image_file, folder_path, filename)

    def upload_esim_qr(
        self, qr_file, esim_id: int, user_id: int = None, user_full_name: str = None
    ) -> Tuple[str, str]:
        """Upload eSIM QR code"""
        # Clean the full name for filename
        if user_full_name:
            clean_name = "".join(
                c for c in user_full_name if c.isalnum() or c in (" ", "-", "_")
            ).rstrip()
            clean_name = clean_name.replace(" ", "_")
        else:
            clean_name = "user"

        folder_path = f"esim_qr/{esim_id}"
        if user_id:
            filename = f"{user_id}_{clean_name}_esim_qr{self._get_file_extension(qr_file.name)}"
        else:
            filename = f"esim_{esim_id}_qr{self._get_file_extension(qr_file.name)}"
        return self.upload_image(qr_file, folder_path, filename)


# Global instance
firebase_storage = FirebaseStorage()
