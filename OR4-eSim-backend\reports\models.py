from django.conf import settings
from django.db import models


class Report(models.Model):
    """
    Report model for analytics and reporting
    """

    class ReportType(models.TextChoices):
        SALES = "sales", "Sales Report"
        REVENUE = "revenue", "Revenue Report"
        USER_GROWTH = "user_growth", "User Growth Report"
        ESIM_USAGE = "esim_usage", "eSIM Usage Report"
        RESELLER_PERFORMANCE = "reseller_performance", "Reseller Performance Report"
        CUSTOM = "custom", "Custom Report"

    class ReportFormat(models.TextChoices):
        PDF = "pdf", "PDF"
        EXCEL = "excel", "Excel"
        CSV = "csv", "CSV"
        JSON = "json", "JSON"

    name = models.CharField(max_length=200)
    report_type = models.CharField(max_length=30, choices=ReportType.choices)
    format = models.CharField(
        max_length=10, choices=ReportFormat.choices, default=ReportFormat.PDF
    )

    # Parameters
    date_from = models.DateField(null=True, blank=True)
    date_to = models.DateField(null=True, blank=True)
    parameters = models.JSONField(default=dict)  # Additional report parameters

    # Generated report
    file_path = models.CharField(max_length=500, blank=True, null=True)
    file_size = models.PositiveIntegerField(blank=True, null=True)  # in bytes
    generated_at = models.DateTimeField(null=True, blank=True)
    file_url = models.URLField(blank=True, null=True)  # URL to download the report

    # Status
    is_generated = models.BooleanField(default=False)
    is_downloaded = models.BooleanField(default=False)
    status = models.CharField(
        max_length=20, default="pending"
    )  # pending, completed, failed

    # User who requested the report
    requested_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    generated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="generated_reports",
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "reports"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} - {self.report_type}"


class DashboardMetric(models.Model):
    """
    Dashboard metrics for admin panel
    """

    class MetricType(models.TextChoices):
        COUNTER = "counter", "Counter"
        PERCENTAGE = "percentage", "Percentage"
        CURRENCY = "currency", "Currency"
        CHART = "chart", "Chart"

    name = models.CharField(max_length=100)
    metric_type = models.CharField(max_length=20, choices=MetricType.choices)
    value = models.DecimalField(max_digits=15, decimal_places=2)
    unit = models.CharField(
        max_length=20, blank=True, null=True
    )  # e.g., 'USD', '%', 'users'

    # Calculation
    calculation_query = models.TextField(
        blank=True, null=True
    )  # SQL or calculation logic
    refresh_interval = models.PositiveIntegerField(default=3600)  # in seconds

    # Display
    is_active = models.BooleanField(default=True)
    display_order = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_calculated = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "dashboard_metrics"
        ordering = ["display_order", "name"]

    def __str__(self):
        return f"{self.name} - {self.value} {self.unit}"


class AnalyticsEvent(models.Model):
    """
    Track analytics events for reporting
    """

    class EventType(models.TextChoices):
        USER_REGISTRATION = "user_registration", "User Registration"
        ORDER_CREATED = "order_created", "Order Created"
        PAYMENT_COMPLETED = "payment_completed", "Payment Completed"
        ESIM_ASSIGNED = "esim_assigned", "eSIM Assigned"
        ESIM_ACTIVATED = "esim_activated", "eSIM Activated"
        RESELLER_ACTIVITY = "reseller_activity", "Reseller Activity"

    event_type = models.CharField(max_length=30, choices=EventType.choices)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True
    )

    # Event data
    event_data = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "analytics_events"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.event_type} - {self.created_at}"


class PerformanceMetric(models.Model):
    """
    Performance metrics for system monitoring
    """

    class MetricCategory(models.TextChoices):
        SYSTEM = "system", "System"
        API = "api", "API"
        DATABASE = "database", "Database"
        EXTERNAL = "external", "External Service"

    name = models.CharField(max_length=100)
    category = models.CharField(max_length=20, choices=MetricCategory.choices)
    value = models.FloatField()
    unit = models.CharField(max_length=20)  # e.g., 'ms', 'MB', 'requests/sec'

    # Context
    context = models.JSONField(default=dict)  # Additional context data

    # Timestamps
    recorded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "performance_metrics"
        ordering = ["-recorded_at"]

    def __str__(self):
        return f"{self.name} - {self.value} {self.unit}"


class ReportSchedule(models.Model):
    """
    Scheduled reports for automatic generation
    """

    class ScheduleFrequency(models.TextChoices):
        DAILY = "daily", "Daily"
        WEEKLY = "weekly", "Weekly"
        MONTHLY = "monthly", "Monthly"
        QUARTERLY = "quarterly", "Quarterly"

    name = models.CharField(max_length=200)
    report_type = models.CharField(max_length=30, choices=Report.ReportType.choices)
    frequency = models.CharField(max_length=20, choices=ScheduleFrequency.choices)

    # Schedule settings
    is_active = models.BooleanField(default=True)
    last_run = models.DateTimeField(null=True, blank=True)
    next_run = models.DateTimeField(null=True, blank=True)

    # Recipients
    recipients = models.JSONField(default=list)  # List of email addresses
    format = models.CharField(
        max_length=10,
        choices=Report.ReportFormat.choices,
        default=Report.ReportFormat.PDF,
    )

    # Parameters
    parameters = models.JSONField(default=dict)

    # Created by
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "report_schedules"
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} - {self.frequency}"
