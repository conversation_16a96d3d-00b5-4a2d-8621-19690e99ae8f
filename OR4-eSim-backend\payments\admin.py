from django.contrib import admin
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html

from .models import Payment


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "transaction_id",
        "order_link",
        "customer_name",
        "reseller_name",
        "amount",
        "currency",
        "payment_type",
        "status",
        "created_at",
        "requires_approval",
    )
    list_filter = (
        "status",
        "payment_type",
        "payment_method",
        "currency",
        "created_at",
        "completed_at",
        "invoice_generated",
    )
    search_fields = (
        "transaction_id",
        "gateway_transaction_id",
        "order__order_number",
        "order__customer__full_name",
        "order__reseller__user__email",
        "invoice_number",
    )
    readonly_fields = (
        "id",
        "created_at",
        "updated_at",
        "is_refunded",
        "net_amount",
        "customer_name",
        "reseller_name",
        "requires_manual_approval",
    )
    raw_id_fields = (
        "order",
        "manual_approved_by",
        "refund_approved_by",
    )
    date_hierarchy = "created_at"
    ordering = ("-created_at",)

    fieldsets = (
        (
            "Payment Information",
            {
                "fields": (
                    "id",
                    "order",
                    "amount",
                    "currency",
                    "payment_method",
                    "payment_type",
                    "status",
                    "transaction_id",
                    "gateway_transaction_id",
                )
            },
        ),
        (
            "Customer Information",
            {
                "fields": (
                    "customer_name",
                    "reseller_name",
                )
            },
        ),
        (
            "Manual Payment Details",
            {
                "fields": (
                    "manual_payment_proof",
                    "manual_payment_notes",
                    "manual_approved_by",
                    "manual_approved_at",
                    "manual_rejection_reason",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Refund Information",
            {
                "fields": (
                    "refund_amount",
                    "refund_reason",
                    "refund_approved_by",
                    "refund_approved_at",
                    "is_refunded",
                    "net_amount",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Invoice Information",
            {
                "fields": (
                    "invoice_number",
                    "invoice_generated",
                    "invoice_generated_at",
                    "invoice_file",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {
                "fields": (
                    "created_at",
                    "updated_at",
                    "processed_at",
                    "completed_at",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    def order_link(self, obj):
        if obj.order:
            url = reverse("admin:orders_order_change", args=[obj.order.id])
            return format_html('<a href="{}">{}</a>', url, obj.order.order_number)
        return "No Order"

    order_link.short_description = "Order"

    def customer_name(self, obj):
        return obj.customer_name

    customer_name.short_description = "Customer"

    def reseller_name(self, obj):
        return obj.reseller_name

    reseller_name.short_description = "Reseller"

    def requires_approval(self, obj):
        return obj.requires_manual_approval

    requires_approval.boolean = True
    requires_approval.short_description = "Needs Approval"

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related(
                "order",
                "order__customer",
                "order__reseller__user",
                "manual_approved_by",
                "refund_approved_by",
            )
        )

    actions = [
        "approve_manual_payments",
        "reject_manual_payments",
        "generate_invoices",
        "mark_as_completed",
        "mark_as_failed",
    ]

    def approve_manual_payments(self, request, queryset):
        """Approve selected manual payments"""
        payments = queryset.filter(status=Payment.PaymentStatus.MANUAL_APPROVAL)
        count = 0

        for payment in payments:
            payment.approve_manual_payment(request.user, "Approved via admin")
            count += 1

        self.message_user(request, f"Successfully approved {count} manual payment(s).")

    approve_manual_payments.short_description = "Approve selected manual payments"

    def reject_manual_payments(self, request, queryset):
        """Reject selected manual payments"""
        payments = queryset.filter(status=Payment.PaymentStatus.MANUAL_APPROVAL)
        count = 0

        for payment in payments:
            payment.reject_manual_payment(request.user, "Rejected via admin")
            count += 1

        self.message_user(request, f"Successfully rejected {count} manual payment(s).")

    reject_manual_payments.short_description = "Reject selected manual payments"

    def generate_invoices(self, request, queryset):
        """Generate invoices for selected payments"""
        count = 0

        for payment in queryset:
            if not payment.invoice_generated:
                payment.generate_invoice_number()
                payment.invoice_generated = True
                payment.invoice_generated_at = timezone.now()
                payment.save()
                count += 1

        self.message_user(request, f"Successfully generated {count} invoice(s).")

    generate_invoices.short_description = "Generate invoices for selected payments"

    def mark_as_completed(self, request, queryset):
        """Mark selected payments as completed"""
        count = queryset.update(
            status=Payment.PaymentStatus.COMPLETED,
            processed_at=timezone.now(),
            completed_at=timezone.now(),
        )

        self.message_user(
            request, f"Successfully marked {count} payment(s) as completed."
        )

    mark_as_completed.short_description = "Mark selected payments as completed"

    def mark_as_failed(self, request, queryset):
        """Mark selected payments as failed"""
        count = queryset.update(status=Payment.PaymentStatus.FAILED)

        self.message_user(request, f"Successfully marked {count} payment(s) as failed.")

    mark_as_failed.short_description = "Mark selected payments as failed"
