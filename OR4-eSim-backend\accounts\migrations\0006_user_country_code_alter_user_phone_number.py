# Generated by Django 4.2.7 on 2025-08-10 08:10

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0005_remove_user_username"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="country_code",
            field=models.Char<PERSON>ield(
                default="+1",
                help_text="Country code (e.g., +1, +44, +91)",
                max_length=5,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=models.CharField(
                blank=True,
                help_text="Phone number without country code",
                max_length=15,
                null=True,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Phone number must be 9-15 digits without country code.",
                        regex="^\\d{9,15}$",
                    )
                ],
            ),
        ),
    ]
