# -*- coding: utf-8 -*-
"""
Professional Stripe Payment Views for TraveRoam Bundle Purchases
Integrated with existing Payment model for unified payment management
"""

import logging
from decimal import Decimal

from django.db import models, transaction
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from api.utils import create_error_response, create_success_response
from clients.models import Client
from esim_management.services import ESIMWorkflowService
from esim_management.traveroam_api import TraveRoamAPIClient
from resellers.models import Reseller

from .models import Payment, StripeWebhook
from .stripe_service import StripePaymentError, stripe_service

logger = logging.getLogger(__name__)


class StripeBundleCheckoutAPIView(APIView):
    """
    Create Stripe checkout session for TraveRoam bundle purchase
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Create checkout session for bundle purchase"""
        try:
            # Extract request data
            client_id = request.data.get("client_id")
            bundle_name = request.data.get("bundle_name")
            reseller_markup_percent = request.data.get("reseller_markup_percent", 0)

            if not all([client_id, bundle_name]):
                return Response(
                    create_error_response("client_id and bundle_name are required"),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get client and reseller
            try:
                client = Client.objects.get(id=client_id)
                reseller = None

                if hasattr(request.user, "reseller"):
                    reseller = request.user.reseller
                elif hasattr(request.user, "is_reseller") and request.user.is_reseller:
                    reseller = Reseller.objects.get(user=request.user)

            except Client.DoesNotExist:
                return Response(
                    create_error_response("Client not found"),
                    status=status.HTTP_404_NOT_FOUND,
                )
            except Reseller.DoesNotExist:
                return Response(
                    create_error_response("Reseller not found"),
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get bundle details from TraveRoam
            traveroam_client = TraveRoamAPIClient()
            bundle_details = traveroam_client.get_bundle_details(bundle_name)

            if not bundle_details:
                return Response(
                    create_error_response("Bundle not found"),
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Calculate pricing
            base_price = Decimal(str(bundle_details.get("price", 0)))
            markup_percent = Decimal(str(reseller_markup_percent))
            markup_amount = (
                base_price * (markup_percent / 100)
                if markup_percent > 0
                else Decimal("0.00")
            )
            final_price = base_price + markup_amount

            # Prepare bundle data for checkout
            bundle_data = {
                "bundle_name": bundle_name,
                "name": bundle_details.get("description", bundle_name),
                "description": f"TraveRoam eSIM Bundle - {bundle_details.get('description', '')}",
                "price": final_price,
                "country": (
                    bundle_details.get("countries", [{}])[0].get("name", "")
                    if bundle_details.get("countries")
                    else ""
                ),
                "data_volume": f"{bundle_details.get('dataAmount', 0)}MB",
                "validity_days": bundle_details.get("duration", 0),
            }

            client_data = {
                "id": client.id,
                "email": client.email,
                "phone_number": client.phone_number,
            }

            reseller_data = None
            if reseller:
                reseller_data = {
                    "id": reseller.id,
                    "company_name": getattr(
                        reseller,
                        "company_name",
                        reseller.user.get_full_name() or reseller.user.email,
                    ),
                }

            # Create checkout session
            with transaction.atomic():
                checkout_result = stripe_service.create_bundle_checkout_session(
                    bundle_data=bundle_data,
                    client_data=client_data,
                    reseller_data=reseller_data,
                )

                if not checkout_result["success"]:
                    return Response(
                        create_error_response("Failed to create checkout session"),
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )

                # Create payment record
                payment = Payment.objects.create(
                    order=None,  # Will be linked when order is created
                    amount=final_price,
                    currency=stripe_service.currency[:3],  # Ensure only 3 characters
                    payment_method="stripe",
                    payment_type=Payment.PaymentType.STRIPE,
                    status=Payment.PaymentStatus.PENDING,
                    bundle_name=bundle_name,
                    bundle_details=bundle_details,
                    base_price=base_price,
                    reseller_markup_percent=markup_percent,
                    reseller_markup_amount=markup_amount,
                    gateway_response={
                        "checkout_session_id": checkout_result["session_id"],
                        "stripe_metadata": checkout_result.get("metadata", {}),
                    },
                )

                # Set Stripe metadata
                payment.set_stripe_metadata(
                    checkout_session_id=checkout_result["session_id"]
                )

                logger.info(f"Created payment {payment.id} for bundle {bundle_name}")

                return Response(
                    create_success_response(
                        data={
                            "checkout_url": checkout_result["checkout_url"],
                            "session_id": checkout_result["session_id"],
                            "payment_id": payment.id,
                            "amount": float(final_price),
                            "currency": stripe_service.currency,
                            "bundle_details": bundle_data,
                        },
                        message="Checkout session created successfully",
                    ),
                    status=status.HTTP_200_OK,
                )

        except StripePaymentError as e:
            logger.error(f"Stripe payment error: {str(e)}")
            return Response(
                create_error_response(f"Payment processing error: {str(e)}"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(f"Error creating checkout session: {str(e)}")
            return Response(
                create_error_response(f"Failed to create checkout session: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class StripePaymentIntentAPIView(APIView):
    """
    Create Stripe Payment Intent for bundle purchase
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Create payment intent for bundle purchase"""
        try:
            # Extract request data
            client_id = request.data.get("client_id")
            bundle_name = request.data.get("bundle_name")
            reseller_markup_percent = request.data.get("reseller_markup_percent", 0)

            if not all([client_id, bundle_name]):
                return Response(
                    create_error_response("client_id and bundle_name are required"),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get client and reseller
            client = Client.objects.get(id=client_id)
            reseller = None

            if hasattr(request.user, "reseller"):
                reseller = request.user.reseller
            elif hasattr(request.user, "is_reseller") and request.user.is_reseller:
                reseller = Reseller.objects.get(user=request.user)

            # Get bundle details and calculate pricing
            traveroam_client = TraveRoamAPIClient()
            bundle_details = traveroam_client.get_bundle_details(bundle_name)

            if not bundle_details:
                return Response(
                    create_error_response("Bundle not found"),
                    status=status.HTTP_404_NOT_FOUND,
                )

            base_price = Decimal(str(bundle_details.get("price", 0)))
            markup_percent = Decimal(str(reseller_markup_percent))
            markup_amount = (
                base_price * (markup_percent / 100)
                if markup_percent > 0
                else Decimal("0.00")
            )
            final_price = base_price + markup_amount

            # Create payment intent
            metadata = {
                "bundle_name": bundle_name,
                "client_id": str(client.id),
                "client_email": client.email,
                "purchase_type": "traveroam_bundle",
            }

            if reseller:
                metadata["reseller_id"] = str(reseller.id)
                metadata["reseller_name"] = getattr(
                    reseller,
                    "company_name",
                    reseller.user.get_full_name() or reseller.user.email,
                )

            payment_intent_result = stripe_service.create_payment_intent(
                amount=final_price, metadata=metadata
            )

            if not payment_intent_result["success"]:
                return Response(
                    create_error_response("Failed to create payment intent"),
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            # Create payment record
            with transaction.atomic():
                payment = Payment.objects.create(
                    order=None,  # Will be linked when order is created
                    amount=final_price,
                    currency=stripe_service.currency[:3],  # Ensure only 3 characters
                    payment_method="stripe",
                    payment_type=Payment.PaymentType.STRIPE,
                    status=Payment.PaymentStatus.PROCESSING,
                    bundle_name=bundle_name,
                    bundle_details=bundle_details,
                    base_price=base_price,
                    reseller_markup_percent=markup_percent,
                    reseller_markup_amount=markup_amount,
                    gateway_response=metadata,
                )

                # Set Stripe metadata
                payment.set_stripe_metadata(
                    payment_intent_id=payment_intent_result["payment_intent_id"],
                    client_secret=payment_intent_result["client_secret"],
                )

                logger.info(
                    f"Created payment intent {payment_intent_result['payment_intent_id']}"
                )

                return Response(
                    create_success_response(
                        data={
                            "payment_intent_id": payment_intent_result[
                                "payment_intent_id"
                            ],
                            "client_secret": payment_intent_result["client_secret"],
                            "payment_id": payment.id,
                            "amount": float(final_price),
                            "currency": stripe_service.currency,
                            "publishable_key": stripe_service.public_key,
                        },
                        message="Payment intent created successfully",
                    ),
                    status=status.HTTP_200_OK,
                )

        except Client.DoesNotExist:
            return Response(
                create_error_response("Client not found"),
                status=status.HTTP_404_NOT_FOUND,
            )
        except Reseller.DoesNotExist:
            return Response(
                create_error_response("Reseller not found"),
                status=status.HTTP_404_NOT_FOUND,
            )
        except StripePaymentError as e:
            logger.error(f"Stripe payment error: {str(e)}")
            return Response(
                create_error_response(f"Payment processing error: {str(e)}"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(f"Error creating payment intent: {str(e)}")
            return Response(
                create_error_response(f"Failed to create payment intent: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class StripePaymentStatusAPIView(APIView):
    """
    Check Stripe payment status
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, payment_intent_id=None):
        """Get payment status"""
        try:
            # Check if we're retrieving a checkout session
            session_id = request.query_params.get("session_id")

            if session_id:
                # Retrieve checkout session
                payment_result = stripe_service.retrieve_checkout_session(session_id)

                if not payment_result["success"]:
                    return Response(
                        create_error_response("Checkout session not found"),
                        status=status.HTTP_404_NOT_FOUND,
                    )

                # Get local payment record
                payment = Payment.objects.filter(
                    stripe_checkout_session_id=session_id
                ).first()

                response_data = {
                    "session_id": session_id,
                    "payment_status": payment_result["payment_status"],
                    "amount_total": payment_result["amount_total"],
                    "currency": payment_result["currency"],
                    "metadata": payment_result["metadata"],
                }

                if payment:
                    response_data.update(
                        {
                            "payment_id": payment.id,
                            "bundle_name": payment.bundle_name,
                            "local_status": payment.status,
                            "is_bundle_purchase": payment.is_bundle_purchase,
                        }
                    )

                return Response(
                    create_success_response(
                        data=response_data,
                        message="Checkout session status retrieved successfully",
                    ),
                    status=status.HTTP_200_OK,
                )

            elif payment_intent_id:
                # Retrieve payment intent
                payment_result = stripe_service.retrieve_payment_intent(
                    payment_intent_id
                )

                if not payment_result["success"]:
                    return Response(
                        create_error_response("Payment not found"),
                        status=status.HTTP_404_NOT_FOUND,
                    )

                # Get local payment record
                payment = Payment.objects.filter(
                    stripe_payment_intent_id=payment_intent_id
                ).first()

                response_data = {
                    "payment_intent_id": payment_intent_id,
                    "status": payment_result["status"],
                    "amount": payment_result["amount"],
                    "currency": payment_result["currency"],
                    "metadata": payment_result["metadata"],
                }

                if payment:
                    response_data.update(
                        {
                            "payment_id": payment.id,
                            "bundle_name": payment.bundle_name,
                            "local_status": payment.status,
                            "is_bundle_purchase": payment.is_bundle_purchase,
                        }
                    )

                return Response(
                    create_success_response(
                        data=response_data,
                        message="Payment status retrieved successfully",
                    ),
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    create_error_response(
                        "Either payment_intent_id or session_id is required"
                    ),
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except StripePaymentError as e:
            logger.error(f"Stripe payment error: {str(e)}")
            return Response(
                create_error_response(f"Payment processing error: {str(e)}"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(f"Error retrieving payment status: {str(e)}")
            return Response(
                create_error_response(f"Failed to retrieve payment status: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@api_view(["POST"])
@permission_classes([AllowAny])
@csrf_exempt
@require_http_methods(["POST"])
def stripe_webhook(request):
    """
    Handle Stripe webhook events for payment confirmation
    """
    try:
        # Get webhook payload and signature
        payload = request.body
        signature = request.headers.get("Stripe-Signature", "")

        if not signature:
            logger.warning("Missing Stripe webhook signature")
            return Response(
                {"error": "Missing signature"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Verify and construct webhook event
        try:
            webhook_result = stripe_service.construct_webhook_event(payload, signature)
            event = webhook_result["event"]
            event_type = webhook_result["event_type"]
            event_data = webhook_result["data"]

        except StripePaymentError as e:
            logger.error(f"Webhook verification failed: {str(e)}")
            return Response(
                {"error": "Invalid signature"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Log webhook receipt
        logger.info(f"Received Stripe webhook: {event_type} (ID: {event['id']})")

        # Check if webhook already processed (idempotency)
        existing_webhook = StripeWebhook.objects.filter(
            stripe_event_id=event["id"]
        ).first()

        if existing_webhook:
            logger.info(f"Webhook already processed: {event['id']}")
            return Response({"status": "already_processed"}, status=status.HTTP_200_OK)

        # Create webhook record
        stripe_webhook = StripeWebhook.objects.create(
            stripe_event_id=event["id"],
            event_type=event_type,
            event_data=event_data,
            stripe_created_at=timezone.datetime.fromtimestamp(
                event["created"], tz=timezone.utc
            ),
        )

        # Process webhook based on event type
        try:
            with transaction.atomic():
                if event_type == "payment_intent.succeeded":
                    process_payment_intent_succeeded(event_data, stripe_webhook)
                elif event_type == "payment_intent.payment_failed":
                    process_payment_intent_failed(event_data, stripe_webhook)
                elif event_type == "checkout.session.completed":
                    process_checkout_session_completed(event_data, stripe_webhook)
                elif event_type == "checkout.session.expired":
                    process_checkout_session_expired(event_data, stripe_webhook)
                else:
                    logger.info(f"Unhandled webhook event type: {event_type}")

                stripe_webhook.mark_processed()

        except Exception as e:
            logger.error(f"Error processing webhook {event['id']}: {str(e)}")
            stripe_webhook.increment_processing_attempts(str(e))
            return Response(
                {"error": "Processing failed"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        logger.info(f"Successfully processed webhook: {event_type} (ID: {event['id']})")
        return Response({"status": "success"}, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Unexpected error processing Stripe webhook: {str(e)}")
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


def process_payment_intent_succeeded(event_data, stripe_webhook):
    """Process successful payment intent"""
    payment_intent_id = event_data.get("id")

    # Find corresponding payment
    payment = Payment.objects.filter(stripe_payment_intent_id=payment_intent_id).first()

    if not payment:
        logger.warning(f"No payment found for payment intent: {payment_intent_id}")
        return

    # Update payment status
    payment.mark_stripe_succeeded()
    stripe_webhook.payment = payment
    stripe_webhook.save()

    # Trigger eSIM provisioning if this is a bundle purchase
    if payment.is_bundle_purchase and payment.bundle_name:
        try:
            # Get client from order or create a temporary one
            client = None
            if payment.order and payment.order.client:
                client = payment.order.client
            else:
                # Try to get client from metadata
                metadata = payment.gateway_response or {}
                client_id = metadata.get("client_id")
                if client_id:
                    client = Client.objects.get(id=client_id)

            if client:
                # Get reseller
                reseller = None
                if payment.order and payment.order.reseller:
                    reseller = payment.order.reseller
                else:
                    metadata = payment.gateway_response or {}
                    reseller_id = metadata.get("reseller_id")
                    if reseller_id:
                        reseller = Reseller.objects.get(id=reseller_id)

                # Assign eSIM to client
                esim_result = ESIMWorkflowService.assign_esim_to_client(
                    client=client, bundle_name=payment.bundle_name, reseller=reseller
                )

                if esim_result["success"]:
                    logger.info(
                        f"eSIM {esim_result['esim_id']} provisioned for payment {payment.id}"
                    )
                else:
                    logger.error(f"eSIM provisioning failed: {esim_result['error']}")

        except Exception as e:
            logger.error(f"Error provisioning eSIM for payment {payment.id}: {str(e)}")

    logger.info(f"Processed payment success for payment intent: {payment_intent_id}")


def process_payment_intent_failed(event_data, stripe_webhook):
    """Process failed payment intent"""
    payment_intent_id = event_data.get("id")
    error_message = event_data.get("last_payment_error", {}).get(
        "message", "Payment failed"
    )

    # Find corresponding payment
    payment = Payment.objects.filter(stripe_payment_intent_id=payment_intent_id).first()

    if not payment:
        logger.warning(f"No payment found for payment intent: {payment_intent_id}")
        return

    # Update payment status
    payment.mark_stripe_failed(error_message)
    stripe_webhook.payment = payment
    stripe_webhook.save()

    logger.info(f"Processed payment failure for payment intent: {payment_intent_id}")


def process_checkout_session_completed(event_data, stripe_webhook):
    """Process completed checkout session"""
    session_id = event_data.get("id")
    payment_intent_id = event_data.get("payment_intent")

    # Find payment by session ID
    payment = Payment.objects.filter(stripe_checkout_session_id=session_id).first()

    if not payment:
        logger.warning(f"No payment found for checkout session: {session_id}")
        return

    # If there's a payment intent, link it
    if payment_intent_id and not payment.stripe_payment_intent_id:
        payment.set_stripe_metadata(payment_intent_id=payment_intent_id)

    # Mark as succeeded and trigger eSIM provisioning (similar to payment_intent_succeeded)
    payment.mark_stripe_succeeded()
    stripe_webhook.payment = payment
    stripe_webhook.save()

    # Trigger eSIM provisioning
    if payment.is_bundle_purchase and payment.bundle_name:
        try:
            # Get client from order or metadata
            client = None
            if payment.order and payment.order.client:
                client = payment.order.client
            else:
                metadata = payment.gateway_response or {}
                client_id = metadata.get("client_id")
                if client_id:
                    client = Client.objects.get(id=client_id)

            if client:
                # Get reseller
                reseller = None
                if payment.order and payment.order.reseller:
                    reseller = payment.order.reseller
                else:
                    metadata = payment.gateway_response or {}
                    reseller_id = metadata.get("reseller_id")
                    if reseller_id:
                        reseller = Reseller.objects.get(id=reseller_id)

                esim_result = ESIMWorkflowService.assign_esim_to_client(
                    client=client, bundle_name=payment.bundle_name, reseller=reseller
                )

                if esim_result["success"]:
                    logger.info(
                        f"eSIM {esim_result['esim_id']} provisioned for checkout session {session_id}"
                    )
                else:
                    logger.error(f"eSIM provisioning failed: {esim_result['error']}")

        except Exception as e:
            logger.error(
                f"Error provisioning eSIM for checkout session {session_id}: {str(e)}"
            )

    logger.info(f"Processed checkout session completion: {session_id}")


def process_checkout_session_expired(event_data, stripe_webhook):
    """Process expired checkout session"""
    session_id = event_data.get("id")

    # Find and cancel payment
    payment = Payment.objects.filter(stripe_checkout_session_id=session_id).first()

    if payment:
        payment.status = Payment.PaymentStatus.CANCELLED
        payment.save()

    logger.info(f"Processed checkout session expiration: {session_id}")


class StripeBundlePurchaseListAPIView(APIView):
    """
    List bundle purchases for authenticated user
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        """Get bundle purchases"""
        try:
            # If pk is provided, return specific bundle purchase
            if pk:
                try:
                    payment = Payment.objects.get(id=pk, bundle_name__isnull=False)

                    # Check permissions
                    if (
                        hasattr(request.user, "is_reseller")
                        and request.user.is_reseller
                    ):
                        reseller = Reseller.objects.get(user=request.user)
                        if payment.order and payment.order.reseller != reseller:
                            return Response(
                                create_error_response("Access denied"),
                                status=status.HTTP_403_FORBIDDEN,
                            )
                    elif hasattr(request.user, "is_client") and request.user.is_client:
                        client = Client.objects.get(user=request.user)
                        if payment.order and payment.order.client != client:
                            return Response(
                                create_error_response("Access denied"),
                                status=status.HTTP_403_FORBIDDEN,
                            )
                    elif not (
                        hasattr(request.user, "is_admin") and request.user.is_admin
                    ):
                        return Response(
                            create_error_response("Access denied"),
                            status=status.HTTP_403_FORBIDDEN,
                        )

                    payment_data = {
                        "id": payment.id,
                        "bundle_name": payment.bundle_name,
                        "status": payment.status,
                        "amount": float(payment.amount),
                        "currency": payment.currency,
                        "total_amount_with_markup": float(
                            payment.total_amount_with_markup
                        ),
                        "base_price": float(payment.base_price),
                        "reseller_markup_amount": float(payment.reseller_markup_amount),
                        "created_at": payment.created_at.isoformat(),
                        "completed_at": (
                            payment.completed_at.isoformat()
                            if payment.completed_at
                            else None
                        ),
                        "is_stripe_payment": payment.is_stripe_payment,
                        "stripe_payment_intent_id": payment.stripe_payment_intent_id,
                        "stripe_checkout_session_id": payment.stripe_checkout_session_id,
                    }

                    if payment.order:
                        payment_data.update(
                            {
                                "client_name": payment.customer_name,
                                "client_email": (
                                    payment.order.client.email
                                    if payment.order.client
                                    else None
                                ),
                                "reseller_name": payment.reseller_name,
                            }
                        )

                    return Response(
                        create_success_response(
                            data=payment_data,
                            message="Bundle purchase retrieved successfully",
                        ),
                        status=status.HTTP_200_OK,
                    )

                except Payment.DoesNotExist:
                    return Response(
                        create_error_response("Bundle purchase not found"),
                        status=status.HTTP_404_NOT_FOUND,
                    )

            # Filter payments based on user role
            if hasattr(request.user, "is_reseller") and request.user.is_reseller:
                reseller = Reseller.objects.get(user=request.user)
                payments = Payment.objects.filter(bundle_name__isnull=False).filter(
                    models.Q(order__reseller=reseller) | models.Q(order__isnull=True)
                )
            elif hasattr(request.user, "is_client") and request.user.is_client:
                client = Client.objects.get(user=request.user)
                payments = Payment.objects.filter(bundle_name__isnull=False).filter(
                    models.Q(order__client=client) | models.Q(order__isnull=True)
                )
            elif hasattr(request.user, "is_admin") and request.user.is_admin:
                payments = Payment.objects.filter(bundle_name__isnull=False)
            else:
                return Response(
                    create_error_response("Access denied"),
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Apply filters
            status_filter = request.query_params.get("status")
            if status_filter:
                payments = payments.filter(status=status_filter)

            # Paginate
            page_size = int(request.query_params.get("page_size", 20))
            page = int(request.query_params.get("page", 1))
            start = (page - 1) * page_size
            end = start + page_size

            payments = payments[start:end]

            # Serialize data
            payments_data = []
            for payment in payments:
                payment_data = {
                    "id": payment.id,
                    "bundle_name": payment.bundle_name,
                    "status": payment.status,
                    "amount": float(payment.amount),
                    "currency": payment.currency,
                    "total_amount_with_markup": float(payment.total_amount_with_markup),
                    "base_price": float(payment.base_price),
                    "reseller_markup_amount": float(payment.reseller_markup_amount),
                    "created_at": payment.created_at.isoformat(),
                    "completed_at": (
                        payment.completed_at.isoformat()
                        if payment.completed_at
                        else None
                    ),
                    "is_stripe_payment": payment.is_stripe_payment,
                    "stripe_payment_intent_id": payment.stripe_payment_intent_id,
                    "stripe_checkout_session_id": payment.stripe_checkout_session_id,
                }

                if payment.order:
                    payment_data.update(
                        {
                            "client_name": payment.customer_name,
                            "client_email": (
                                payment.order.client.email
                                if payment.order.client
                                else None
                            ),
                            "reseller_name": payment.reseller_name,
                        }
                    )

                payments_data.append(payment_data)

            return Response(
                create_success_response(
                    data=payments_data,
                    message="Bundle purchases retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Error retrieving bundle purchases: {str(e)}")
            return Response(
                create_error_response(f"Failed to retrieve purchases: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
