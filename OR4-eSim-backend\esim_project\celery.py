import os

from celery import Ce<PERSON>y

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "esim_project.settings")

app = Celery("esim_project")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django apps.
app.autodiscover_tasks()


@app.task(bind=True)
def debug_task(self):
    print(f"Request: {self.request!r}")


# Configure Celery Beat schedule
app.conf.beat_schedule = {
    "update-esim-status": {
        "task": "esim_management.tasks.update_esim_status",
        "schedule": 300.0,  # Every 5 minutes
    },
    "generate-daily-reports": {
        "task": "reports.tasks.generate_daily_reports",
        "schedule": 86400.0,  # Every 24 hours
    },
    "cleanup-expired-sessions": {
        "task": "accounts.tasks.cleanup_expired_sessions",
        "schedule": 3600.0,  # Every hour
    },
    "cleanup-password-reset-tokens": {
        "task": "accounts.tasks.cleanup_old_password_reset_tokens",
        "schedule": 7200.0,  # Every 2 hours
    },
    "cleanup-inactive-users": {
        "task": "accounts.tasks.cleanup_inactive_users",
        "schedule": 604800.0,  # Every 7 days
    },
}
