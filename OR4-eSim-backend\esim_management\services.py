# -*- coding: utf-8 -*-
"""
Professional eSIM Workflow Service for TraveRoam Integration
Complete service layer for reseller workflow management
"""

import base64
import io
import logging
import uuid
from typing import Any, Dict, List, Optional

import qrcode
from django.conf import settings
from django.core.cache import cache
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone

from clients.models import Client
from orders.models import Order, OrderItem, OrderStatusHistory
from payments.models import Payment
from resellers.models import Reseller

from .models import ESIM, ESIMDelivery, ESIMPlan, ESIMUsage, TraveRoamWebhook
from .traveroam_api import TraveRoamAPIError, traveroam_client

logger = logging.getLogger(__name__)


class ESIMWorkflowService:
    """Professional service class for complete eSIM workflow operations"""

    @staticmethod
    def get_available_plans(
        country: Optional[str] = None, region: Optional[str] = None
    ) -> List[Dict]:
        """Get available eSIM plans from TraveRoam - Step 2 of workflow"""
        try:
            plans = traveroam_client.get_available_plans(country=country, region=region)
            logger.info(
                f"Retrieved {len(plans)} plans from TraveRoam for {country}/{region}"
            )
            return plans
        except TraveRoamAPIError as e:
            logger.error(f"Failed to get plans from TraveRoam: {str(e)}")
            return []

    @staticmethod
    def get_networks(countries: Optional[str] = None) -> List[Dict]:
        """Get available networks from TraveRoam"""
        try:
            networks = traveroam_client.get_networks(countries=countries)
            logger.info(f"Retrieved {len(networks)} networks from TraveRoam")
            return networks
        except TraveRoamAPIError as e:
            logger.error(f"Failed to get networks from TraveRoam: {str(e)}")
            return []

    @staticmethod
    def get_organization_details() -> Dict:
        """Get organization details from TraveRoam"""
        try:
            org_details = traveroam_client.get_organization_details()
            logger.info("Retrieved organization details from TraveRoam")
            return org_details
        except TraveRoamAPIError as e:
            logger.error(f"Failed to get organization details: {str(e)}")
            return {}

    @staticmethod
    def create_client(user_data: Dict, reseller: Reseller) -> Dict:
        """Create new client - Step 1 of workflow"""
        try:
            # Validate required fields
            required_fields = ["full_name", "phone_number", "email", "passport_id"]
            for field in required_fields:
                if not user_data.get(field):
                    raise ValueError(f"Missing required field: {field}")

            # Check if client already exists
            existing_client = Client.objects.filter(
                email=user_data["email"], reseller=reseller
            ).first()

            if existing_client:
                return {
                    "success": False,
                    "error": "Client with this email already exists",
                    "client_id": existing_client.id,
                }

            # Create client
            client = Client.objects.create(
                reseller=reseller,
                full_name=user_data["full_name"],
                phone_number=user_data["phone_number"],
                email=user_data["email"],
                passport_id=user_data["passport_id"],
                country_of_travel=user_data.get("country_of_travel", {}),
                date_of_travel=user_data.get("date_of_travel"),
                created_at=timezone.now(),
            )

            logger.info(f"Created client {client.id} for reseller {reseller.id}")

            return {
                "success": True,
                "client_id": client.id,
                "client": {
                    "id": client.id,
                    "full_name": client.full_name,
                    "email": client.email,
                    "phone_number": client.phone_number,
                    "country_of_travel": client.country_of_travel,
                    "created_at": client.created_at.isoformat(),
                },
            }

        except Exception as e:
            logger.error(f"Error creating client: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def assign_esim_to_client(
        client: Client,
        bundle_name: str,
        reseller: Reseller,
        payment_data: Optional[Dict] = None,
    ) -> Dict:
        """Assign eSIM to client - Step 2 of workflow with comprehensive validation and order creation"""
        try:
            # Check reseller credit
            if reseller.current_credit < 1:  # Assuming minimum credit requirement
                raise ValueError("Insufficient credit for this plan")

            # Validate bundle name format
            if not bundle_name.startswith("esimp_"):
                raise ValueError("Invalid bundle name format. Must start with 'esimp_'")

            # Get bundle details from TraveRoam
            bundle_details = traveroam_client.get_bundle_details(bundle_name)
            if not bundle_details:
                raise ValueError(f"Bundle {bundle_name} not found")

            # Step 1: Validate with TraveRoam API - Check for active bundles
            logger.info(
                f"Validating bundle assignment for {client.phone_number} with bundle {bundle_name}"
            )
            validation_result = traveroam_client.validate_bundle_assignment(
                phone_number=client.phone_number, bundle_name=bundle_name
            )

            if not validation_result.get("valid", True):
                return {
                    "success": False,
                    "error": validation_result.get(
                        "message", "Bundle validation failed"
                    ),
                    "validation_details": validation_result,
                }

            # Step 2: Check for duplicate assignments via TraveRoam
            duplicate_check = traveroam_client.check_duplicate_assignment(
                phone_number=client.phone_number, bundle_name=bundle_name
            )

            if duplicate_check.get("duplicate", False):
                return {
                    "success": False,
                    "error": duplicate_check.get(
                        "message", "Duplicate assignment detected"
                    ),
                    "duplicate_details": duplicate_check,
                }

            # Step 3: Check local database for existing assignments
            active_esim = ESIM.objects.filter(
                client=client,
                status__in=["provisioned", "assigned", "activated"],
                expires_at__gt=timezone.now(),
            ).first()

            if active_esim:
                raise ValueError(
                    f"Client already has an active eSIM (ID: {active_esim.id}) that expires on {active_esim.expires_at.strftime('%Y-%m-%d %H:%M')}. "
                    "Cannot assign new eSIM until current one expires or is cancelled."
                )

            # Step 4: Check if phone number is already assigned to another client
            existing_esim = (
                ESIM.objects.filter(
                    client__phone_number=client.phone_number,
                    status__in=["provisioned", "assigned", "activated"],
                    expires_at__gt=timezone.now(),
                )
                .exclude(client=client)
                .first()
            )

            if existing_esim:
                raise ValueError(
                    f"Phone number {client.phone_number} is already assigned to another client (ID: {existing_esim.client_id}) "
                    "with an active eSIM. Cannot reassign until current eSIM expires."
                )

            # Step 5: Create Order record first
            bundle_price = bundle_details.get("price", 0)
            markup_percent = (
                payment_data.get("markup_percent", 0) if payment_data else 0
            )
            markup_amount = (
                (bundle_price * markup_percent / 100) if markup_percent else 0
            )
            final_price = bundle_price + markup_amount

            order = Order.objects.create(
                order_number=f"ESIM-{uuid.uuid4().hex[:8].upper()}",
                order_type="esim",
                order_source="reseller",
                status="pending",
                reseller=reseller,
                client=client,
                product_name=f"eSIM Bundle - {bundle_details.get('name', bundle_name)}",
                product_description=f"{bundle_details.get('data', 'N/A')} for {bundle_details.get('duration', 'N/A')} in {bundle_details.get('country', 'N/A')}",
                quantity=1,
                unit_price=bundle_price,
                subtotal=bundle_price,
                tax_amount=0.00,  # Can be calculated based on requirements
                delivery_fee=0.00,  # eSIM delivery is digital
                total_amount=final_price,
                notes=f"eSIM bundle assignment for {client.phone_number}",
            )

            # Create Order Item
            OrderItem.objects.create(
                order=order,
                product_name=f"eSIM Bundle - {bundle_name}",
                product_description=bundle_details.get("description", ""),
                quantity=1,
                unit_price=bundle_price,
                total_price=bundle_price,
                product_metadata={
                    "bundle_name": bundle_name,
                    "bundle_details": bundle_details,
                    "traveroam_product": True,
                },
            )

            # Step 6: Provision eSIM with TraveRoam
            logger.info(
                f"Provisioning eSIM for client {client.id} with bundle {bundle_name}"
            )
            provision_result = traveroam_client.provision_esim(
                bundle_name,
                {
                    "full_name": client.full_name,
                    "email": client.email,
                    "phone_number": client.phone_number,
                    "order_reference": order.order_number,
                },
            )

            if not provision_result.get("success"):
                # Update order status to failed
                order.status = "failed"
                order.save()
                OrderStatusHistory.objects.create(
                    order=order,
                    status="failed",
                    notes=f"TraveRoam API error: {provision_result.get('error', 'Unknown error')}",
                    changed_by_id=reseller.user_id,
                )
                raise ValueError(
                    f"Failed to provision eSIM: {provision_result.get('error')}"
                )

            # Extract eSIM details
            esim_details = provision_result["esim_details"]
            order_reference = provision_result["order_reference"]

            # Step 7: Create eSIM record
            esim_data = {
                "client": client,
                "reseller": reseller,
                "traveroam_order_reference": order_reference,
                "bundle_name": bundle_name,
                "iccid": esim_details.get("iccid"),
                "smdp_address": esim_details.get("smdp_address"),
                "matching_id": esim_details.get("matching_id"),
                "qr_code": esim_details.get("qr_code"),
                "activation_code": esim_details.get("activation_code"),
                "status": "provisioned",
                "assigned_at": timezone.now(),
                "bundle_details": bundle_details,
            }

            esim = ESIM.objects.create(**esim_data)

            # Step 8: Update order with eSIM details and mark as confirmed
            order.status = "confirmed"
            order.esim_id = esim.id  # Link order to eSIM
            order.traveroam_order_reference = order_reference
            order.confirmed_at = timezone.now()
            order.save()

            # Create order status history
            OrderStatusHistory.objects.create(
                order=order,
                status="confirmed",
                notes=f"eSIM provisioned successfully. ICCID: {esim_details.get('iccid', 'N/A')}",
                changed_by_id=reseller.user_id,
            )

            # Step 9: Create Payment record if payment data provided
            if payment_data:
                Payment.objects.create(
                    order=order,
                    amount=payment_data.get("amount", final_price),
                    currency=payment_data.get("currency", "USD"),
                    payment_method=payment_data.get("payment_method", "stripe"),
                    payment_type=payment_data.get("payment_type", "stripe"),
                    status=payment_data.get("status", "completed"),
                    transaction_id=payment_data.get("transaction_id", ""),
                    stripe_payment_intent_id=payment_data.get("payment_intent_id", ""),
                    stripe_checkout_session_id=payment_data.get("session_id", ""),
                    bundle_name=bundle_name,
                    bundle_details=bundle_details,
                    base_price=bundle_price,
                    reseller_markup_percent=markup_percent,
                    reseller_markup_amount=markup_amount,
                    gateway_response=payment_data.get("gateway_response", {}),
                    completed_at=(
                        timezone.now()
                        if payment_data.get("status") == "completed"
                        else None
                    ),
                )

            # Step 10: Deduct credit from reseller
            reseller.current_credit -= bundle_price
            reseller.save()

            # Step 11: Send delivery email
            delivery_result = ESIMWorkflowService.deliver_esim_to_client(esim, order)

            # Step 12: Update order status to delivered if email sent successfully
            if delivery_result.get("success", False):
                order.status = "delivered"
                order.delivered_at = timezone.now()
                order.save()

                OrderStatusHistory.objects.create(
                    order=order,
                    status="delivered",
                    notes=f"eSIM delivery email sent to {client.email}",
                    changed_by_id=reseller.user_id,
                )

            logger.info(
                f"Successfully assigned eSIM {esim.id} to client {client.id} with order {order.order_number}"
            )

            return {
                "success": True,
                "esim_id": esim.id,
                "order_id": order.id,
                "order_number": order.order_number,
                "order_reference": order_reference,
                "iccid": esim_details.get("iccid"),
                "qr_code": esim_details.get("qr_code"),
                "bundle_name": bundle_name,
                "bundle_details": bundle_details,
                "delivery_sent": delivery_result.get("success", False),
                "total_amount": final_price,
                "provision_result": provision_result,
            }

        except TraveRoamAPIError as e:
            logger.error(f"TraveRoam API error during eSIM assignment: {str(e)}")
            return {"success": False, "error": f"Failed to provision eSIM: {str(e)}"}
        except Exception as e:
            logger.error(f"Error assigning eSIM to client: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def deliver_esim_to_client(esim: ESIM, order: Optional[Order] = None) -> Dict:
        """Deliver eSIM to client via email - Step 3 of workflow"""
        try:
            if not esim.qr_code:
                logger.error(f"No QR code available for eSIM {esim.id}")
                return False

            # Generate QR code if not already available
            qr_code_data = esim.qr_code
            if not qr_code_data.startswith("data:image"):
                # Generate QR code from activation code
                qr = qrcode.QRCode(version=1, box_size=10, border=5)
                qr.add_data(esim.activation_code or esim.qr_code)
                qr.make(fit=True)

                img = qr.make_image(fill_color="black", back_color="white")
                buffer = io.BytesIO()
                img.save(buffer, format="PNG")
                qr_code_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"

            # Prepare email context with branding and support info
            context = {
                "client_name": esim.client.full_name,
                "esim_id": esim.id,
                "bundle_name": esim.bundle_name,
                "qr_code": qr_code_data,
                "iccid": esim.iccid,
                "smdp_address": esim.smdp_address,
                "matching_id": esim.matching_id,
                "activation_code": esim.activation_code,
                "assigned_at": esim.assigned_at,
                "delivery_date": timezone.now(),
                "reseller_name": esim.reseller.user.get_full_name(),
                # Add branding and support information from environment
                "company_name": settings.COMPANY_NAME,
                "company_logo_url": settings.COMPANY_LOGO_URL,
                "support_email": settings.SUPPORT_EMAIL,
                "support_phone": settings.SUPPORT_PHONE,
                "company_website": settings.COMPANY_WEBSITE,
                "client_email": esim.client.email,
            }

            # Render email templates
            html_content = render_to_string(
                "esim_management/email/esim_delivery.html", context
            )
            text_content = render_to_string(
                "esim_management/email/esim_delivery.txt", context
            )

            # Send email with enhanced subject and error handling
            if not settings.EMAIL_DELIVERY_ENABLED:
                logger.warning("Email delivery is disabled in settings")
                return False

            subject = f"{settings.EMAIL_SUBJECT_PREFIX}Your eSIM is Ready - {esim.bundle_name}"

            try:
                send_mail(
                    subject=subject,
                    message=text_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[esim.client.email],
                    html_message=html_content,
                    fail_silently=False,
                )
                logger.info(f"Email sent successfully to {esim.client.email}")
            except Exception as e:
                logger.error(f"Failed to send email to {esim.client.email}: {str(e)}")
                # Still create delivery record but mark as failed
                ESIMDelivery.objects.create(
                    esim=esim,
                    delivery_method="email",
                    delivery_status="failed",
                    delivered_at=timezone.now(),
                    delivery_data={
                        "email_sent": False,
                        "recipient": esim.client.email,
                        "error": str(e),
                        "qr_code_included": bool(qr_code_data),
                    },
                )
                return False

            # Create delivery record
            ESIMDelivery.objects.create(
                esim=esim,
                delivery_method="email",
                delivery_status="sent",
                delivered_at=timezone.now(),
                delivery_data={
                    "email_sent": True,
                    "recipient": esim.client.email,
                    "qr_code_included": bool(qr_code_data),
                },
            )

            # Update eSIM status
            esim.status = "delivered"
            esim.delivered_at = timezone.now()
            esim.save()

            logger.info(f"Successfully delivered eSIM {esim.id} to {esim.client.email}")
            return True

        except Exception as e:
            logger.error(f"Error delivering eSIM {esim.id}: {str(e)}")
            return False

    @staticmethod
    def get_esim_status(esim: ESIM) -> Dict:
        """Get current eSIM status from TraveRoam"""
        try:
            if not esim.iccid:
                return {
                    "success": False,
                    "status": "no_iccid",
                    "message": "No ICCID available",
                }

            # Get comprehensive status from TraveRoam
            status_data = traveroam_client.get_esim_status(esim.iccid)

            if status_data.get("success"):
                # Update local eSIM record with latest status
                esim.last_status_check = timezone.now()
                esim.save()

            return status_data

        except TraveRoamAPIError as e:
            logger.error(f"Failed to get eSIM status: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def get_esim_usage(esim: ESIM) -> Dict:
        """Get eSIM usage data from TraveRoam"""
        try:
            if not esim.iccid:
                return {
                    "success": False,
                    "status": "no_iccid",
                    "message": "No ICCID available",
                }

            # Get bundle information which includes usage data
            bundles = traveroam_client.get_esim_bundles(esim.iccid)

            # Extract usage information from bundles
            usage_data = []
            total_data_used = 0
            total_data_remaining = 0

            for bundle in bundles:
                if isinstance(bundle, dict):
                    bundle_usage = {
                        "bundle_name": bundle.get("name"),
                        "data_used": bundle.get("data_used", 0),
                        "data_remaining": bundle.get("data_remaining", 0),
                        "total_data": bundle.get("total_data", 0),
                        "status": bundle.get("status"),
                        "start_date": bundle.get("start_date"),
                        "end_date": bundle.get("end_date"),
                    }
                    usage_data.append(bundle_usage)

                    # Calculate totals
                    total_data_used += bundle_usage["data_used"]
                    total_data_remaining += bundle_usage["data_remaining"]

            return {
                "success": True,
                "usage_data": usage_data,
                "summary": {
                    "total_data_used": total_data_used,
                    "total_data_remaining": total_data_remaining,
                    "total_bundles": len(usage_data),
                    "active_bundles": len(
                        [b for b in usage_data if b["status"] == "Active"]
                    ),
                },
                "last_updated": timezone.now().isoformat(),
            }

        except TraveRoamAPIError as e:
            logger.error(f"Failed to get eSIM usage: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def cancel_esim(esim: ESIM, reason: str = "") -> bool:
        """Cancel eSIM (revoke bundles)"""
        try:
            if not esim.iccid:
                logger.error(f"No ICCID available for eSIM {esim.id}")
                return False

            # Get current bundles
            bundles = traveroam_client.get_esim_bundles(esim.iccid)

            # Revoke all active bundles
            revoked_count = 0
            for bundle in bundles:
                if isinstance(bundle, dict) and bundle.get("status") in [
                    "Active",
                    "Queued",
                ]:
                    try:
                        traveroam_client.revoke_bundle(
                            iccid=esim.iccid,
                            bundle_name=bundle.get("name"),
                            assignment_id=bundle.get("assignment_id"),
                        )
                        revoked_count += 1
                    except TraveRoamAPIError as e:
                        logger.warning(
                            f"Failed to revoke bundle {bundle.get('name')}: {str(e)}"
                        )

            # Update eSIM status
            esim.status = "cancelled"
            esim.cancelled_at = timezone.now()
            esim.cancellation_reason = reason
            esim.save()

            logger.info(
                f"Successfully cancelled eSIM {esim.id}, revoked {revoked_count} bundles"
            )
            return True

        except Exception as e:
            logger.error(f"Error cancelling eSIM {esim.id}: {str(e)}")
            return False

    @staticmethod
    def get_client_esim_history(client: Client) -> List[Dict]:
        """Get eSIM history for client"""
        try:
            esims = ESIM.objects.filter(client=client).order_by("-assigned_at")

            history = []
            for esim in esims:
                history.append(
                    {
                        "id": esim.id,
                        "bundle_name": esim.bundle_name,
                        "status": esim.status,
                        "assigned_at": (
                            esim.assigned_at.isoformat() if esim.assigned_at else None
                        ),
                        "delivered_at": (
                            esim.delivered_at.isoformat() if esim.delivered_at else None
                        ),
                        "iccid": esim.iccid,
                        "order_reference": esim.traveroam_order_reference,
                        "qr_code_available": bool(esim.qr_code),
                    }
                )

            return history

        except Exception as e:
            logger.error(f"Error getting client eSIM history: {str(e)}")
            return []

    @staticmethod
    def get_reseller_dashboard_data(reseller: Reseller) -> Dict:
        """Get dashboard data for reseller"""
        try:
            # Get basic stats
            total_clients = Client.objects.filter(reseller=reseller).count()
            total_esims = ESIM.objects.filter(reseller=reseller).count()
            active_esims = ESIM.objects.filter(
                reseller=reseller, status__in=["provisioned", "delivered", "activated"]
            ).count()

            # Get recent orders
            recent_esims = ESIM.objects.filter(reseller=reseller).order_by(
                "-assigned_at"
            )[:5]

            # Get organization details
            org_details = traveroam_client.get_organization_details()

            return {
                "summary": {
                    "total_clients": total_clients,
                    "total_esims": total_esims,
                    "active_esims": active_esims,
                    "credit_balance": reseller.credit,
                },
                "recent_esims": [
                    {
                        "id": esim.id,
                        "client_name": esim.client.full_name,
                        "bundle_name": esim.bundle_name,
                        "status": esim.status,
                        "assigned_at": (
                            esim.assigned_at.isoformat() if esim.assigned_at else None
                        ),
                    }
                    for esim in recent_esims
                ],
                "organization": org_details,
            }

        except Exception as e:
            logger.error(f"Error getting dashboard data: {str(e)}")
            return {
                "summary": {
                    "total_clients": 0,
                    "total_esims": 0,
                    "active_esims": 0,
                    "credit_balance": 0,
                },
                "recent_esims": [],
                "organization": {},
            }

    @staticmethod
    def get_all_orders() -> List[Dict]:
        """Get all orders from TraveRoam"""
        try:
            orders = traveroam_client.get_orders()
            logger.info(
                f"Retrieved {len(orders) if isinstance(orders, list) else 0} orders from TraveRoam"
            )
            return orders
        except TraveRoamAPIError as e:
            logger.error(f"Failed to get orders: {str(e)}")
            return []

    @staticmethod
    def get_all_esims() -> List[Dict]:
        """Get all eSIMs from TraveRoam"""
        try:
            esims = traveroam_client.get_all_esims()
            logger.info(
                f"Retrieved {len(esims) if isinstance(esims, list) else 0} eSIMs from TraveRoam"
            )
            return esims
        except TraveRoamAPIError as e:
            logger.error(f"Failed to get eSIMs: {str(e)}")
            return []

    @staticmethod
    def process_traveroam_webhook(webhook_data: Dict) -> bool:
        """Process webhook from TraveRoam"""
        try:
            # Create webhook record
            webhook = TraveRoamWebhook.objects.create(
                webhook_type=webhook_data.get("event_type", "unknown"),
                payload=webhook_data,
                processed=True,
                processed_at=timezone.now(),
            )

            # Process based on event type
            event_type = webhook_data.get("event_type", "").lower()

            if "activation" in event_type:
                # Handle eSIM activation
                iccid = webhook_data.get("iccid")
                if iccid:
                    esim = ESIM.objects.filter(iccid=iccid).first()
                    if esim:
                        esim.status = "activated"
                        esim.activated_at = timezone.now()
                        esim.save()
                        logger.info(f"eSIM {esim.id} activated via webhook")

            elif "usage" in event_type:
                # Handle usage updates
                iccid = webhook_data.get("iccid")
                if iccid:
                    esim = ESIM.objects.filter(iccid=iccid).first()
                    if esim:
                        # Create usage record
                        ESIMUsage.objects.create(
                            esim=esim,
                            data_used=webhook_data.get("data_used", 0),
                            timestamp=timezone.now(),
                            usage_data=webhook_data,
                        )
                        logger.info(f"Usage recorded for eSIM {esim.id}")

            elif "expiry" in event_type:
                # Handle eSIM expiry
                iccid = webhook_data.get("iccid")
                if iccid:
                    esim = ESIM.objects.filter(iccid=iccid).first()
                    if esim:
                        esim.status = "expired"
                        esim.expired_at = timezone.now()
                        esim.save()
                        logger.info(f"eSIM {esim.id} expired via webhook")

            logger.info(f"Successfully processed TraveRoam webhook: {event_type}")
            return True

        except Exception as e:
            logger.error(f"Error processing TraveRoam webhook: {str(e)}")
            return False

    @staticmethod
    def generate_qr_code(activation_code: str) -> str:
        """Generate QR code from activation code"""
        try:
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(activation_code)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")
            buffer = io.BytesIO()
            img.save(buffer, format="PNG")
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()

            return f"data:image/png;base64,{qr_base64}"

        except Exception as e:
            logger.error(f"Error generating QR code: {str(e)}")
            return ""

    @staticmethod
    def validate_bundle_name(bundle_name: str) -> bool:
        """Validate bundle name format"""
        # Basic validation - bundle names should follow TraveRoam format
        if not bundle_name:
            return False

        # Check if it starts with 'esimp_' and contains country code
        if not bundle_name.startswith("esimp_"):
            return False

        # Should contain country code (2-3 letters)
        parts = bundle_name.split("_")
        if len(parts) < 4:
            return False

        return True

    @staticmethod
    def get_esim_status_from_traveroam(esim: ESIM) -> Dict:
        """Get eSIM status from TraveRoam API"""
        try:
            if not esim.traveroam_esim_id:
                return {"success": False, "error": "No TraveRoam eSIM ID available"}

            status_data = traveroam_client.check_esim_status(esim.traveroam_esim_id)

            # Update local eSIM status if different
            if status_data.get("status") and status_data["status"] != esim.status:
                esim.status = status_data["status"]
                esim.save()

            return {"success": True, "status_data": status_data}

        except Exception as e:
            logger.error(f"Failed to get eSIM status for {esim.id}: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def refresh_esim_data_from_traveroam(esim: ESIM) -> Dict:
        """Refresh eSIM data from TraveRoam API"""
        try:
            if not esim.traveroam_esim_id:
                return {"success": False, "error": "No TraveRoam eSIM ID available"}

            refresh_result = traveroam_client.refresh_esim_data(esim.traveroam_esim_id)

            if refresh_result.get("success"):
                # Update eSIM with fresh data
                esim_data = refresh_result.get("esim_data", {})
                if esim_data:
                    esim.status = esim_data.get("status", esim.status)
                    esim.qr_code = esim_data.get("qr_code", esim.qr_code)
                    esim.activation_code = esim_data.get(
                        "activation_code", esim.activation_code
                    )
                    esim.save()

            return refresh_result

        except Exception as e:
            logger.error(f"Failed to refresh eSIM data for {esim.id}: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def send_sms_via_traveroam(esim: ESIM, message: str) -> Dict:
        """Send SMS notification via TraveRoam API"""
        try:
            if not esim.client or not esim.client.phone_number:
                return {"success": False, "error": "No phone number available"}

            sms_result = traveroam_client.send_sms(
                phone_number=esim.client.phone_number, message=message
            )

            return sms_result

        except Exception as e:
            logger.error(f"Failed to send SMS for eSIM {esim.id}: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def revoke_esim_bundle_via_traveroam(esim: ESIM, reason: str = "") -> Dict:
        """Revoke eSIM bundle via TraveRoam API and update order"""
        try:
            if not esim.traveroam_esim_id:
                return {"success": False, "error": "No TraveRoam eSIM ID available"}

            revoke_result = traveroam_client.revoke_bundle(
                iccid=esim.traveroam_esim_id,
                bundle_name=esim.bundle_name,
                reason=reason,
            )

            if revoke_result.get("success"):
                # Update eSIM status
                esim.status = "revoked"
                esim.revoked_at = timezone.now()
                esim.save()

                # Update related order if exists
                try:
                    order = Order.objects.get(esim_id=esim.id)
                    order.status = "cancelled"
                    order.save()

                    OrderStatusHistory.objects.create(
                        order=order,
                        status="cancelled",
                        notes=f"eSIM bundle revoked via TraveRoam. Reason: {reason}",
                        changed_by_id=esim.reseller.user_id,
                    )
                except Order.DoesNotExist:
                    pass

            return revoke_result

        except Exception as e:
            logger.error(f"Failed to revoke eSIM bundle for {esim.id}: {str(e)}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def get_comprehensive_reseller_analytics(
        reseller: Reseller,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
    ) -> Dict:
        """Get comprehensive analytics for a reseller including orders and payments"""
        try:
            from django.db import models

            # eSIM analytics
            esim_queryset = ESIM.objects.filter(reseller=reseller)
            if date_from:
                esim_queryset = esim_queryset.filter(assigned_at__gte=date_from)
            if date_to:
                esim_queryset = esim_queryset.filter(assigned_at__lte=date_to)

            total_esims = esim_queryset.count()
            active_esims = esim_queryset.filter(
                status__in=["provisioned", "assigned", "activated"]
            ).count()
            revoked_esims = esim_queryset.filter(status="revoked").count()

            # Order analytics
            order_queryset = Order.objects.filter(reseller=reseller, order_type="esim")
            if date_from:
                order_queryset = order_queryset.filter(created_at__gte=date_from)
            if date_to:
                order_queryset = order_queryset.filter(created_at__lte=date_to)

            total_orders = order_queryset.count()
            completed_orders = order_queryset.filter(status="delivered").count()
            cancelled_orders = order_queryset.filter(status="cancelled").count()

            # Revenue analytics
            revenue_data = order_queryset.aggregate(
                total_revenue=models.Sum("total_amount"),
                avg_order_value=models.Avg("total_amount"),
            )

            # Payment analytics
            payment_queryset = Payment.objects.filter(
                order__reseller=reseller, order__order_type="esim"
            )
            if date_from:
                payment_queryset = payment_queryset.filter(created_at__gte=date_from)
            if date_to:
                payment_queryset = payment_queryset.filter(created_at__lte=date_to)

            payment_stats = payment_queryset.aggregate(
                total_payments=models.Sum("amount"),
                successful_payments=models.Count(
                    "id", filter=models.Q(status="completed")
                ),
                failed_payments=models.Count("id", filter=models.Q(status="failed")),
            )

            # Bundle distribution
            bundle_stats = (
                esim_queryset.values("bundle_name")
                .annotate(count=models.Count("id"))
                .order_by("-count")
            )

            return {
                "success": True,
                "analytics": {
                    "esim_stats": {
                        "total_esims": total_esims,
                        "active_esims": active_esims,
                        "revoked_esims": revoked_esims,
                        "activation_rate": (
                            (active_esims / total_esims * 100) if total_esims > 0 else 0
                        ),
                    },
                    "order_stats": {
                        "total_orders": total_orders,
                        "completed_orders": completed_orders,
                        "cancelled_orders": cancelled_orders,
                        "completion_rate": (
                            (completed_orders / total_orders * 100)
                            if total_orders > 0
                            else 0
                        ),
                    },
                    "revenue_stats": {
                        "total_revenue": float(revenue_data["total_revenue"] or 0),
                        "average_order_value": float(
                            revenue_data["avg_order_value"] or 0
                        ),
                        "total_payments": float(payment_stats["total_payments"] or 0),
                        "successful_payments": payment_stats["successful_payments"]
                        or 0,
                        "failed_payments": payment_stats["failed_payments"] or 0,
                    },
                    "bundle_distribution": list(bundle_stats),
                    "reseller_info": {
                        "company_name": reseller.user.get_full_name(),
                        "current_credit": float(reseller.current_credit),
                        "credit_limit": float(reseller.credit_limit),
                        "credit_utilization": (
                            (
                                float(reseller.current_credit)
                                / float(reseller.credit_limit)
                                * 100
                            )
                            if reseller.credit_limit > 0
                            else 0
                        ),
                        "max_clients": reseller.max_clients,
                        "max_sims": reseller.max_sims,
                        "status": (
                            "active" if not reseller.is_suspended else "suspended"
                        ),
                    },
                },
            }

        except Exception as e:
            logger.error(
                f"Failed to get comprehensive analytics for reseller {reseller.id}: {str(e)}"
            )
            return {"success": False, "error": str(e)}
