import os

import django
from django.conf import settings

# Configure Django settings for testing
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "esim_project.settings")
django.setup()

import factory
import pytest
from django.contrib.auth import get_user_model
from faker import Faker
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.models import User, UserProfile

User = get_user_model()
fake = Faker()


@pytest.fixture
def api_client():
    """API client fixture"""
    return APIClient()


@pytest.fixture
def user_factory():
    """Factory for creating test users"""

    class UserFactory(factory.django.DjangoModelFactory):
        class Meta:
            model = User

        email = factory.Sequence(lambda n: f"user{n}@example.com")
        first_name = factory.Faker("first_name")
        last_name = factory.Faker("last_name")
        password = factory.PostGenerationMethodCall("set_password", "Test@123")
        role = "public_user"
        is_active = True
        phone_number = factory.LazyFunction(lambda: fake.phone_number()[:15])

    return UserFactory


@pytest.fixture
def admin_user_factory():
    """Factory for creating admin users"""

    class AdminUserFactory(factory.django.DjangoModelFactory):
        class Meta:
            model = User

        email = factory.Sequence(lambda n: f"admin{n}@example.com")
        first_name = factory.Faker("first_name")
        last_name = factory.Faker("last_name")
        password = factory.PostGenerationMethodCall("set_password", "Admin@123")
        role = "admin"
        is_active = True
        phone_number = factory.LazyFunction(lambda: fake.phone_number()[:15])

    return AdminUserFactory


@pytest.fixture
def user_profile_factory():
    """Factory for creating user profiles"""

    class UserProfileFactory(factory.django.DjangoModelFactory):
        class Meta:
            model = UserProfile

        user = factory.SubFactory("tests.conftest.user_factory")
        date_of_birth = factory.Faker("date_of_birth")
        gender = factory.Iterator(["male", "female", "other"])
        address = factory.Faker("address")
        city = factory.Faker("city")
        state = factory.Faker("state")
        country = factory.Faker("country")
        postal_code = factory.Faker("postcode")
        emergency_contact_name = factory.Faker("name")
        emergency_contact_phone = factory.Faker("phone_number")
        profile_image_url = factory.Faker("url")
        preferences = factory.LazyFunction(dict)

    return UserProfileFactory


@pytest.fixture
def authenticated_client(api_client, user_factory):
    """Authenticated API client with a test user"""
    user = user_factory()
    refresh = RefreshToken.for_user(user)
    api_client.credentials(HTTP_AUTHORIZATION=f"Bearer {refresh.access_token}")
    return api_client, user


@pytest.fixture
def admin_client(api_client, admin_user_factory):
    """Authenticated API client with an admin user"""
    admin_user = admin_user_factory()
    refresh = RefreshToken.for_user(admin_user)
    api_client.credentials(HTTP_AUTHORIZATION=f"Bearer {refresh.access_token}")
    return api_client, admin_user


@pytest.fixture
def test_image():
    """Create a test image file"""
    from django.core.files.uploadedfile import SimpleUploadedFile

    return SimpleUploadedFile(
        name="test_image.jpg", content=b"fake-image-content", content_type="image/jpeg"
    )


@pytest.fixture
def mock_firebase_storage(monkeypatch):
    """Mock Firebase storage to avoid actual uploads during testing"""

    class MockFirebaseStorage:
        def upload_profile_image(self, image_file, user_id, user_full_name):
            return (
                f"https://storage.googleapis.com/test-bucket/users/{user_id}/{user_id}_{user_full_name}.jpg",
                f"users/{user_id}/{user_id}_{user_full_name}.jpg",
            )

        def upload_document_image(
            self, image_file, document_type, user_id, user_full_name
        ):
            return (
                f"https://storage.googleapis.com/test-bucket/documents/{document_type}/{user_id}/{user_id}_{user_full_name}_{document_type}.jpg",
                f"documents/{document_type}/{user_id}/{user_id}_{user_full_name}_{document_type}.jpg",
            )

    mock_storage = MockFirebaseStorage()
    monkeypatch.setattr("accounts.views.firebase_storage", mock_storage)
    monkeypatch.setattr("api.views.firebase_storage", mock_storage)
    return mock_storage


@pytest.fixture
def reseller_user(user_factory):
    """Create a reseller user and reseller profile"""
    user = user_factory(role="reseller")
    from resellers.models import Reseller

    reseller = Reseller.objects.create(
        user=user,
        max_clients=100,
        max_sims=1000,
        credit_limit=1000.00,
        current_credit=1000.00,
        is_suspended=False,
    )
    return user, reseller


@pytest.fixture
def reseller_factory(user_factory):
    """Factory for creating test resellers"""

    def _create_reseller(**kwargs):
        user = kwargs.pop("user", None) or user_factory(role="reseller")

        defaults = {
            "user": user,
            "max_clients": 100,
            "max_sims": 1000,
            "credit_limit": 1000.00,
            "current_credit": 0.00,
            "is_suspended": False,
        }
        defaults.update(kwargs)

        from resellers.models import Reseller

        return Reseller.objects.create(**defaults)

    return _create_reseller


@pytest.fixture
def order_factory(user_factory, reseller_user):
    """Factory for creating test orders"""

    def _create_order(**kwargs):
        user, reseller = reseller_user
        from clients.models import Client
        from orders.models import Order

        client_user = user_factory()
        client = Client.objects.create(
            user=client_user,
            reseller=reseller,
            full_name="Test Client",
            phone_number="+1234567890",
            email="<EMAIL>",
            client_type="reseller_client",
        )

        # Generate unique order number using timestamp and random string
        import time
        import uuid

        order_number = f"ORDER-{int(time.time())}-{uuid.uuid4().hex[:6].upper()}"

        defaults = {
            "order_number": order_number,
            "order_type": "sim",
            "order_source": "reseller",
            "status": "pending",
            "reseller": reseller,
            "client": client,
            "product_name": "Test SIM Card",
            "product_description": "Test SIM card for testing",
            "quantity": 1,
            "unit_price": "10.00",
            "subtotal": "10.00",
            "tax_amount": "0.80",
            "delivery_fee": "2.00",
            "total_amount": "12.80",
        }
        defaults.update(kwargs)

        return Order.objects.create(**defaults)

    return _create_order


@pytest.fixture
def client_factory(user_factory, reseller_user):
    """Factory for creating test clients"""

    def _create_client(**kwargs):
        user, reseller = reseller_user
        from clients.models import Client

        # Create a new user for the client if not provided
        client_user = kwargs.pop("user", None) or user_factory()

        defaults = {
            "user": client_user,
            "reseller": kwargs.pop("reseller", reseller),
            "full_name": "Test Client",
            "phone_number": "+1234567890",
            "email": client_user.email,
            "client_type": "reseller_client",
            "status": "active",
            "tier": "basic",
        }
        defaults.update(kwargs)

        return Client.objects.create(**defaults)

    return _create_client


@pytest.fixture
def reseller_client(api_client, reseller_user):
    """Authenticated API client with reseller user"""
    user, reseller = reseller_user
    from rest_framework_simplejwt.tokens import RefreshToken

    refresh = RefreshToken.for_user(user)
    api_client.credentials(HTTP_AUTHORIZATION=f"Bearer {refresh.access_token}")
    return api_client, user, reseller


@pytest.fixture
def payment_factory(user_factory, reseller_user):
    """Factory for creating test payments"""

    def _create_payment(**kwargs):
        user, reseller = reseller_user
        from clients.models import Client
        from orders.models import Order
        from payments.models import Payment

        # Create a client for the order
        client_user = user_factory()
        client = Client.objects.create(
            user=client_user,
            reseller=reseller,
            full_name="Test Client",
            phone_number="+1234567890",
            email="<EMAIL>",
            client_type="reseller_client",
        )

        # Create an order
        import time
        import uuid

        order_number = f"ORDER-{int(time.time())}-{uuid.uuid4().hex[:6].upper()}"

        order = Order.objects.create(
            order_number=order_number,
            order_type="sim",
            order_source="reseller",
            status="pending",
            reseller=reseller,
            client=client,
            product_name="Test SIM Card",
            product_description="Test SIM card for testing",
            quantity=1,
            unit_price="10.00",
            subtotal="10.00",
            tax_amount="0.80",
            delivery_fee="2.00",
            total_amount="12.80",
        )

        # Generate unique transaction ID
        transaction_id = f"TXN-{int(time.time())}-{uuid.uuid4().hex[:6].upper()}"

        defaults = {
            "order": order,
            "amount": "100.00",
            "currency": "USD",
            "payment_method": "stripe",
            "payment_type": "stripe",
            "status": "pending",
            "transaction_id": transaction_id,
        }
        defaults.update(kwargs)

        return Payment.objects.create(**defaults)

    return _create_payment
