import { 
  TRAVEROAM_PLANS_URL,
  TRAVEROAM_NETWORKS_URL,
  TRAVEROAM_ESIM_ASSIGN_URL,
  TRAVEROAM_ORDER_PROCESS_URL,
  TRAVEROAM_CLIENT_VALIDATE_URL,
  API_ENDPOINTS,
  buildApiUrl
} from '../config/api'
import { apiService } from './apiService'

// Helper function to replace URL parameters
const replaceUrlParams = (url, params) => {
  let finalUrl = url
  Object.keys(params).forEach(key => {
    finalUrl = finalUrl.replace(`{${key}}`, params[key])
  })
  return finalUrl
}

// TraveRoam API integration service
export const traveRoamService = {
  // ===== Plans and Bundles =====
  
  // Get available plans from TraveRoam
  async getAvailablePlans(params = {}) {
    try {
      const queryParams = new URLSearchParams()

      if (params.countries) queryParams.append('countries', params.countries)
      if (params.region) queryParams.append('region', params.region)
      if (params.page) queryParams.append('page', params.page)
      if (params.limit) queryParams.append('limit', params.limit)

      const url = queryParams.toString() ? `${TRAVEROAM_PLANS_URL}?${queryParams.toString()}` : TRAVEROAM_PLANS_URL

      const response = await apiService.get(url, { requiresAuth: true })

      const data = response.data || response

      return {
        success: true,
        data: data.data || data,
        message: 'Plans retrieved successfully'
      }
    } catch (error) {
      console.error('❌ Failed to fetch TraveRoam plans:', error)
      return {
        success: false,
        error: error.message || 'Failed to fetch plans',
        data: []
      }
    }
  },

  // Get TraveRoam catalogue (alternative endpoint)
  async getCatalogue(params = {}) {
    try {
      const queryParams = new URLSearchParams()

      if (params.countries) queryParams.append('countries', params.countries)
      if (params.region) queryParams.append('region', params.region)

      const url = queryParams.toString() ? `${TRAVEROAM_PLANS_URL}?${queryParams.toString()}` : TRAVEROAM_PLANS_URL

      const response = await apiService.get(url, { requiresAuth: true })

      const data = response.data || response

      return {
        success: true,
        data: data.data || data,
        message: 'Catalogue retrieved successfully'
      }
    } catch (error) {
      console.error('❌ Failed to fetch TraveRoam catalogue:', error)
      return {
        success: false,
        error: error.message || 'Failed to fetch catalogue',
        data: []
      }
    }
  },

  // Get networks for countries
  async getNetworks(networkData) {
    try {
      // Prepare network request payload
      const payload = {
        countries: networkData.countries || '',
        isos: networkData.isos || '',
        returnall: networkData.returnall || false
      }

      const response = await apiService.post(TRAVEROAM_NETWORKS_URL, payload, { requiresAuth: true })

      const data = response.data || response

      return {
        success: true,
        data: data.data || data,
        message: 'Networks retrieved successfully'
      }
    } catch (error) {
      console.error('❌ Failed to fetch TraveRoam networks:', error)
      return {
        success: false,
        error: error.message || 'Failed to fetch networks',
        data: []
      }
    }
  },

  // Get networks by country codes
  async getNetworksByCountries(countryCodes) {
    try {
      const countries = Array.isArray(countryCodes) ? countryCodes.join(',') : countryCodes

      return await this.getNetworks({
        countries: countries,
        returnall: false
      })
    } catch (error) {
      console.error('❌ Failed to fetch networks by countries:', error)
      return {
        success: false,
        error: error.message || 'Failed to fetch networks by countries',
        data: []
      }
    }
  },

  // Get all available networks
  async getAllNetworks() {
    try {
      return await this.getNetworks({
        returnall: true
      })
    } catch (error) {
      console.error('❌ Failed to fetch all networks:', error)
      return {
        success: false,
        error: error.message || 'Failed to fetch all networks',
        data: []
      }
    }
  },

  // ===== eSIM Assignment and Management =====
  
  // Assign eSIM to client
  async assignEsim(assignmentData) {
    try {
      // Validate assignment data
      const validation = this.validateAssignmentData(assignmentData)
      if (!validation.isValid) {
        return {
          success: false,
          error: 'Invalid assignment data',
          errors: validation.errors
        }
      }

      // Prepare the assignment payload according to backend API
      const payload = {
        client_id: assignmentData.client_id,
        plan_id: assignmentData.plan_id,
        email: assignmentData.email,
        customer_name: assignmentData.customer_name,
        quantity: assignmentData.quantity || 1,
        bundle_id: assignmentData.bundle_id || assignmentData.plan_id,
        customer_phone: assignmentData.customer_phone,
        notes: assignmentData.notes
      }

      const response = await apiService.post(TRAVEROAM_ESIM_ASSIGN_URL, payload, { requiresAuth: true })

      const data = response.data || response

      return {
        success: true,
        data: data.data || data,
        message: 'eSIM assigned successfully'
      }
    } catch (error) {
      console.error('❌ Failed to assign eSIM:', error)
      return {
        success: false,
        error: error.message || 'Failed to assign eSIM'
      }
    }
  },

  // Bulk assign eSIMs
  async bulkAssignEsims(assignmentDataList) {
    try {
      const results = []

      for (const assignmentData of assignmentDataList) {
        const result = await this.assignEsim(assignmentData)
        results.push({
          ...assignmentData,
          result
        })
      }

      const successCount = results.filter(r => r.result.success).length
      const failureCount = results.length - successCount

      return {
        success: failureCount === 0,
        data: results,
        message: `Bulk assignment completed: ${successCount} successful, ${failureCount} failed`
      }
    } catch (error) {
      console.error('❌ Failed to bulk assign eSIMs:', error)
      return {
        success: false,
        error: error.message || 'Failed to bulk assign eSIMs'
      }
    }
  },

  // Get eSIM status
  async getEsimStatus(esimId) {
    try {
      const url = replaceUrlParams(buildApiUrl(API_ENDPOINTS.TRAVEROAM.ESIM_STATUS), { esim_id: esimId })
      const response = await apiService.get(url, { requiresAuth: true })
      
      const data = response.data || response
      
      return {
        success: true,
        data: data.data || data
      }
    } catch (error) {
      console.error(`❌ Failed to get eSIM status for ${esimId}:`, error)
      return {
        success: false,
        error: error.message || 'Failed to get eSIM status'
      }
    }
  },

  // Get eSIM usage
  async getEsimUsage(esimId) {
    try {
      const url = replaceUrlParams(buildApiUrl(API_ENDPOINTS.TRAVEROAM.ESIM_USAGE), { esim_id: esimId })
      const response = await apiService.get(url, { requiresAuth: true })
      
      const data = response.data || response
      
      return {
        success: true,
        data: data.data || data
      }
    } catch (error) {
      console.error(`❌ Failed to get eSIM usage for ${esimId}:`, error)
      return {
        success: false,
        error: error.message || 'Failed to get eSIM usage'
      }
    }
  },

  // ===== Order Processing =====

  // Process order through TraveRoam
  async processOrder(orderData) {
    try {
      // Validate order data
      const validation = this.validateOrderData(orderData)
      if (!validation.isValid) {
        return {
          success: false,
          error: 'Invalid order data',
          errors: validation.errors
        }
      }

      // Prepare order payload according to backend API
      const payload = {
        bundle_id: orderData.bundle_id,
        customer_email: orderData.customer_email,
        customer_name: orderData.customer_name,
        quantity: orderData.quantity || 1,
        customer_phone: orderData.customer_phone,
        notes: orderData.notes,
        reference: orderData.reference || `order_${Date.now()}`
      }

      const response = await apiService.post(TRAVEROAM_ORDER_PROCESS_URL, payload, { requiresAuth: true })

      const data = response.data || response

      return {
        success: true,
        data: data.data || data,
        message: 'Order processed successfully'
      }
    } catch (error) {
      console.error('❌ Failed to process order:', error)
      return {
        success: false,
        error: error.message || 'Failed to process order'
      }
    }
  },

  // Get order status
  async getOrderStatus(orderId) {
    try {
      const response = await apiService.get(`${TRAVEROAM_ORDER_PROCESS_URL}${orderId}/`, { requiresAuth: true })

      const data = response.data || response

      return {
        success: true,
        data: data.data || data
      }
    } catch (error) {
      console.error(`❌ Failed to get order status for ${orderId}:`, error)
      return {
        success: false,
        error: error.message || 'Failed to get order status'
      }
    }
  },

  // ===== Client Validation =====
  
  // Validate client data
  async validateClient(clientData) {
    try {
      const response = await apiService.post(TRAVEROAM_CLIENT_VALIDATE_URL, clientData, { requiresAuth: true })
      
      const data = response.data || response
      
      return {
        success: true,
        data: data.data || data,
        message: 'Client validated successfully'
      }
    } catch (error) {
      console.error('❌ Failed to validate client:', error)
      return {
        success: false,
        error: error.message || 'Failed to validate client'
      }
    }
  },

  // ===== Data Validation =====
  
  // Validate eSIM assignment data
  validateAssignmentData(assignmentData) {
    const errors = {}
    
    if (!assignmentData.client_id) {
      errors.client_id = 'Client ID is required'
    }
    
    if (!assignmentData.plan_id) {
      errors.plan_id = 'Plan ID is required'
    }
    
    if (!assignmentData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(assignmentData.email)) {
      errors.email = 'Valid email address is required'
    }
    
    if (!assignmentData.customer_name || assignmentData.customer_name.trim().length < 2) {
      errors.customer_name = 'Customer name is required'
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  },

  // Validate order data
  validateOrderData(orderData) {
    const errors = {}
    
    if (!orderData.bundle_id) {
      errors.bundle_id = 'Bundle ID is required'
    }
    
    if (!orderData.customer_email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orderData.customer_email)) {
      errors.customer_email = 'Valid customer email is required'
    }
    
    if (!orderData.customer_name || orderData.customer_name.trim().length < 2) {
      errors.customer_name = 'Customer name is required'
    }
    
    if (!orderData.quantity || orderData.quantity < 1) {
      errors.quantity = 'Quantity must be at least 1'
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  },

  // ===== Data Formatting =====
  
  // Format TraveRoam plan data for frontend
  formatPlanData(plan) {
    if (!plan) return null
    
    return {
      id: plan.id || plan.bundle_id,
      name: plan.name || plan.bundle_name,
      description: plan.description || '',
      country: plan.country || plan.countries?.[0] || '',
      countries: plan.countries || [],
      region: plan.region || '',
      dataAllowance: plan.data_allowance || plan.data || 0,
      validityDays: plan.validity_days || plan.validity || 0,
      price: plan.price || plan.cost || 0,
      currency: plan.currency || 'USD',
      networks: plan.networks || [],
      features: plan.features || [],
      isActive: plan.is_active !== undefined ? plan.is_active : true,
      traveroamId: plan.traveroam_id || plan.bundle_id,
      type: plan.type || 'data'
    }
  },

  // Format network data
  formatNetworkData(network) {
    if (!network) return null
    
    return {
      id: network.id,
      name: network.name || '',
      country: network.country || '',
      countryCode: network.country_code || '',
      operator: network.operator || '',
      technology: network.technology || '',
      coverage: network.coverage || '',
      isActive: network.is_active !== undefined ? network.is_active : true
    }
  },

  // Format plans list
  formatPlansList(plans) {
    if (!Array.isArray(plans)) {
      return []
    }
    
    return plans.map(plan => this.formatPlanData(plan))
  },

  // Format networks list
  formatNetworksList(networks) {
    if (!Array.isArray(networks)) {
      return []
    }
    
    return networks.map(network => this.formatNetworkData(network))
  },

  // ===== Utility Functions =====
  
  // Get countries list from plans
  getCountriesFromPlans(plans) {
    if (!Array.isArray(plans)) return []
    
    const countries = new Set()
    plans.forEach(plan => {
      if (plan.country) countries.add(plan.country)
      if (plan.countries && Array.isArray(plan.countries)) {
        plan.countries.forEach(country => countries.add(country))
      }
    })
    
    return Array.from(countries).sort()
  },

  // Filter plans by country
  filterPlansByCountry(plans, country) {
    if (!Array.isArray(plans) || !country || country === 'all') {
      return plans
    }
    
    return plans.filter(plan => 
      plan.country === country || 
      (plan.countries && plan.countries.includes(country)) ||
      plan.country === 'GLOBAL'
    )
  }
}

export default traveRoamService
