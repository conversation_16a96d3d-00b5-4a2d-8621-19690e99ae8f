# Generated by Django 4.2.7 on 2025-08-07 15:08

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('resellers', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='resellerlimit',
            name='changed_by',
        ),
        migrations.RemoveField(
            model_name='resellerlimit',
            name='reseller',
        ),
        migrations.RemoveField(
            model_name='resellerpayment',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='resellerpayment',
            name='reseller',
        ),
        migrations.RemoveField(
            model_name='historicalreseller',
            name='business_address',
        ),
        migrations.RemoveField(
            model_name='historicalreseller',
            name='commission_rate',
        ),
        migrations.RemoveField(
            model_name='historicalreseller',
            name='company_name',
        ),
        migrations.RemoveField(
            model_name='historicalreseller',
            name='markup_percentage',
        ),
        migrations.RemoveField(
            model_name='historicalreseller',
            name='tax_id',
        ),
        migrations.RemoveField(
            model_name='reseller',
            name='business_address',
        ),
        migrations.RemoveField(
            model_name='reseller',
            name='commission_rate',
        ),
        migrations.RemoveField(
            model_name='reseller',
            name='company_name',
        ),
        migrations.RemoveField(
            model_name='reseller',
            name='markup_percentage',
        ),
        migrations.RemoveField(
            model_name='reseller',
            name='tax_id',
        ),
        migrations.DeleteModel(
            name='ResellerActivity',
        ),
        migrations.DeleteModel(
            name='ResellerLimit',
        ),
        migrations.DeleteModel(
            name='ResellerPayment',
        ),
    ]
