from django.db.models import Sum
from rest_framework import serializers

from clients.models import Client
from resellers.models import Reseller

from .models import ESIM, ESIMDelivery, ESIMPlan, ESIMUsage, TraveRoamWebhook


class ClientSerializer(serializers.ModelSerializer):
    """Client serializer for reseller views"""

    class Meta:
        model = Client
        fields = [
            "id",
            "full_name",
            "email",
            "phone_number",
            "passport_number",
            "country_of_travel",
            "date_of_travel",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class ClientCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating clients"""

    class Meta:
        model = Client
        fields = [
            "full_name",
            "email",
            "phone_number",
            "passport_number",
            "country_of_travel",
            "date_of_travel",
        ]

    def validate_email(self, value):
        """Validate email format"""
        import re

        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(pattern, value):
            raise serializers.ValidationError("Invalid email format")
        return value

    def validate_phone_number(self, value):
        """Validate phone number format"""
        import re

        # Remove spaces and special characters
        clean_phone = re.sub(r"[\s\-\(\)]", "", value)
        # Check if it starts with + and has at least 10 digits
        if not clean_phone.startswith("+") or len(clean_phone) < 10:
            raise serializers.ValidationError(
                "Phone number must start with + and have at least 10 digits"
            )
        return value


class ESIMAssignSerializer(serializers.Serializer):
    """Serializer for eSIM assignment"""

    bundle_name = serializers.CharField(max_length=100)

    def validate_bundle_name(self, value):
        """Validate bundle name format"""
        if not value.startswith("esimp_"):
            raise serializers.ValidationError("Bundle name must start with 'esimp_'")
        return value


class ESIMPlanSerializer(serializers.ModelSerializer):
    """ESIM plan serializer"""

    class Meta:
        model = ESIMPlan
        fields = [
            "id",
            "name",
            "description",
            "country",
            "region",
            "data_volume",
            "validity_days",
            "plan_type",
            "base_price",
            "reseller_price",
            "public_price",
            "traveroam_plan_id",
            "is_active",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class ESIMPlanCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIM plans"""

    class Meta:
        model = ESIMPlan
        fields = [
            "name",
            "description",
            "country",
            "region",
            "data_volume",
            "validity_days",
            "plan_type",
            "base_price",
            "reseller_price",
            "public_price",
            "traveroam_plan_id",
            "is_active",
        ]


class ESIMSerializer(serializers.ModelSerializer):
    """ESIM serializer"""

    plan = serializers.SerializerMethodField()
    reseller = serializers.SerializerMethodField()
    client = serializers.SerializerMethodField()
    public_user = serializers.SerializerMethodField()
    usage_summary = serializers.SerializerMethodField()

    class Meta:
        model = ESIM
        fields = [
            "id",
            "plan",
            "reseller",
            "client",
            "public_user",
            "status",
            "qr_code",
            "activation_code",
            "traveroam_esim_id",
            "traveroam_order_reference",
            "bundle_name",
            "smdp_address",
            "matching_id",
            "data_used",
            "data_remaining",
            "created_at",
            "assigned_at",
            "delivered_at",
            "activated_at",
            "expires_at",
            "updated_at",
            "usage_summary",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_plan(self, obj):
        return ESIMPlanSerializer(obj.plan).data if obj.plan else None

    def get_reseller(self, obj):
        if obj.reseller:
            return {
                "id": obj.reseller.id,
                "company_name": obj.reseller.company_name,
                "user": {
                    "id": obj.reseller.user.id,
                    "username": obj.reseller.user.username,
                    "email": obj.reseller.user.email,
                },
            }
        return None

    def get_client(self, obj):
        if obj.client:
            return {
                "id": obj.client.id,
                "full_name": obj.client.full_name,
                "email": obj.client.email,
                "phone_number": obj.client.phone_number,
                "country_of_travel": obj.client.country_of_travel,
            }
        return None

    def get_public_user(self, obj):
        if obj.client and obj.client.client_type == "direct_user":
            return {
                "id": obj.client.id,
                "name": obj.client.full_name,
                "email": obj.client.email,
            }
        return None

    def get_usage_summary(self, obj):
        usage = obj.usage_logs.all()
        total_data_used = usage.aggregate(total=Sum("data_used"))["total"] or 0
        return {
            "total_data_used": total_data_used,
            "usage_count": usage.count(),
            "last_usage": (
                usage.order_by("-timestamp").first().timestamp
                if usage.exists()
                else None
            ),
        }


class ESIMCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIMs"""

    class Meta:
        model = ESIM
        fields = ["plan", "reseller", "client"]


class ESIMUsageSerializer(serializers.ModelSerializer):
    """ESIM usage serializer"""

    esim = serializers.SerializerMethodField()

    class Meta:
        model = ESIMUsage
        fields = ["id", "esim", "data_used", "location", "timestamp", "webhook_data"]
        read_only_fields = ["id"]

    def get_esim(self, obj):
        return ESIMSerializer(obj.esim).data


class ESIMUsageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIM usage records"""

    class Meta:
        model = ESIMUsage
        fields = ["esim", "data_used", "location", "timestamp", "webhook_data"]


class TraveRoamWebhookSerializer(serializers.ModelSerializer):
    """TraveRoam webhook serializer"""

    class Meta:
        model = TraveRoamWebhook
        fields = [
            "id",
            "webhook_type",
            "esim",
            "payload",
            "processed",
            "processed_at",
            "created_at",
        ]
        read_only_fields = ["id", "created_at", "processed_at"]


class ESIMDeliverySerializer(serializers.ModelSerializer):
    """ESIM delivery serializer"""

    esim = serializers.SerializerMethodField()

    class Meta:
        model = ESIMDelivery
        fields = [
            "id",
            "esim",
            "delivery_method",
            "status",
            "recipient_email",
            "recipient_phone",
            "delivery_message",
            "sent_at",
            "delivered_at",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]

    def get_esim(self, obj):
        return ESIMSerializer(obj.esim).data


class ESIMDeliveryCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIM deliveries"""

    class Meta:
        model = ESIMDelivery
        fields = [
            "esim",
            "delivery_method",
            "recipient_email",
            "recipient_phone",
            "delivery_message",
        ]
