name: Django Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install psycopg2-binary

    - name: Create .env file
      run: |
        cp env.example .env
        echo "DEBUG=True" >> .env
        echo "SECRET_KEY=test-secret-key-for-ci" >> .env
        echo "CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8000,http://127.0.0.1:8000,http://localhost:8080,http://127.0.0.1:8080" >> .env
        echo "DB_NAME=test_db" >> .env
        echo "DB_USER=postgres" >> .env
        echo "DB_PASSWORD=postgres" >> .env
        echo "DB_HOST=localhost" >> .env
        echo "DB_PORT=5432" >> .env
        echo "REDIS_URL=redis://localhost:6379/0" >> .env
        # Firebase configuration for testing (will be disabled gracefully)
        echo "FIREBASE_PROJECT_ID=" >> .env
        echo "FIREBASE_PRIVATE_KEY_ID=" >> .env
        echo "FIREBASE_PRIVATE_KEY=" >> .env
        echo "FIREBASE_CLIENT_EMAIL=" >> .env
        echo "FIREBASE_CLIENT_ID=" >> .env
        echo "FIREBASE_STORAGE_BUCKET=" >> .env
        echo "FIREBASE_TYPE=service_account" >> .env
        echo "FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth" >> .env
        echo "FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token" >> .env
        echo "FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs" >> .env
        echo "FIREBASE_CLIENT_X509_CERT_URL=" >> .env
        # JWT settings
        echo "JWT_SECRET_KEY=test-jwt-secret-key" >> .env
        echo "JWT_ACCESS_TOKEN_LIFETIME=60" >> .env
        echo "JWT_REFRESH_TOKEN_LIFETIME=7" >> .env
        echo "JWT_COOKIE_SECURE=False" >> .env
        # Email settings (dummy for testing)
        echo "EMAIL_HOST=smtp.gmail.com" >> .env
        echo "EMAIL_PORT=587" >> .env
        echo "EMAIL_USE_TLS=True" >> .env
        echo "EMAIL_USE_SSL=False" >> .env
        echo "EMAIL_HOST_USER=<EMAIL>" >> .env
        echo "EMAIL_HOST_PASSWORD=test-password" >> .env
        echo "DEFAULT_FROM_EMAIL=<EMAIL>" >> .env
        echo "SERVER_EMAIL=<EMAIL>" >> .env
        echo "EMAIL_TIMEOUT=30" >> .env
        echo "EMAIL_SUBJECT_PREFIX=[eSIM]" >> .env
        echo "COMPANY_NAME=Test Company" >> .env
        echo "COMPANY_LOGO_URL=https://test.com/logo.png" >> .env
        echo "SUPPORT_EMAIL=<EMAIL>" >> .env
        echo "SUPPORT_PHONE=+1234567890" >> .env
        echo "COMPANY_WEBSITE=https://test.com" >> .env
        echo "EMAIL_DELIVERY_ENABLED=True" >> .env
        echo "EMAIL_QUEUE_ENABLED=False" >> .env
        echo "EMAIL_RETRY_ATTEMPTS=3" >> .env
        echo "EMAIL_RETRY_DELAY=300" >> .env
        # TraveRoam API (dummy for testing)
        echo "TRAVEROAM_API_KEY=test-api-key" >> .env
        echo "TRAVEROAM_SECRET_KEY=test-secret-key" >> .env
        echo "TRAVEROAM_API_BASE_URL=https://api.traveroam.com" >> .env
        echo "TRAVEROAM_WEBHOOK_SECRET=test-webhook-secret" >> .env
        echo "TRAVEROAM_CATALOGUE_ENDPOINT=/catalogue" >> .env
        echo "TRAVEROAM_BUNDLE_ENDPOINT=/bundle" >> .env
        echo "TRAVEROAM_NETWORKS_ENDPOINT=/networks" >> .env
        echo "TRAVEROAM_ORGANIZATION_ENDPOINT=/organization" >> .env
        echo "TRAVEROAM_PROCESS_ORDERS_ENDPOINT=/process-orders" >> .env
        echo "TRAVEROAM_ORDERS_ENDPOINT=/orders" >> .env
        echo "TRAVEROAM_ESIM_UPDATE_ENDPOINT=/esim/update" >> .env
        echo "TRAVEROAM_ESIM_DETAILS_ENDPOINT=/esim/details" >> .env
        echo "TRAVEROAM_ESIM_REFRESH_ENDPOINT=/esim/refresh" >> .env
        echo "TRAVEROAM_SEND_SMS_ENDPOINT=/sms/send" >> .env
        echo "TRAVEROAM_ESIM_BUNDLES_ENDPOINT=/esim/bundles" >> .env
        echo "TRAVEROAM_BUNDLE_STATUS_ENDPOINT=/bundle/status" >> .env
        echo "TRAVEROAM_ESIM_LOCATION_ENDPOINT=/esim/location" >> .env
        echo "TRAVEROAM_REVOKE_BUNDLE_ENDPOINT=/bundle/revoke" >> .env
        echo "TRAVEROAM_ALL_ESIMS_ENDPOINT=/esim/all" >> .env
        echo "TRAVEROAM_ESIM_ASSIGNMENTS_ENDPOINT=/esim/assignments" >> .env
        # Stripe (dummy for testing)
        echo "STRIPE_PUBLIC_KEY=pk_test_dummy" >> .env
        echo "STRIPE_SECRET_KEY=sk_test_dummy" >> .env
        echo "STRIPE_WEBHOOK_SECRET=whsec_dummy" >> .env
        echo "STRIPE_LIVE_MODE=False" >> .env
        echo "STRIPE_CURRENCY=USD" >> .env
        echo "STRIPE_SUCCESS_URL=http://localhost:8000/payment/success/" >> .env
        echo "STRIPE_CANCEL_URL=http://localhost:8000/payment/cancel/" >> .env
        # Rate limiting and file upload settings
        echo "RATE_LIMIT_PER_MINUTE=100" >> .env
        echo "RATE_LIMIT_PER_HOUR=1000" >> .env
        echo "MAX_UPLOAD_SIZE=10485760" >> .env
        echo "ALLOWED_UPLOAD_EXTENSIONS=jpg,jpeg,png,pdf,doc,docx" >> .env

    - name: Create static directory
      run: |
        mkdir -p static

    - name: Create media directory
      run: |
        mkdir -p media

    - name: Collect static files
      run: |
        python manage.py collectstatic --noinput

    - name: Install PostgreSQL client
      run: |
        sudo apt-get update
        sudo apt-get install -y postgresql-client redis-tools

    - name: Wait for PostgreSQL
      run: |
        until pg_isready -h localhost -p 5432 -U postgres; do
          echo "Waiting for PostgreSQL to be ready..."
          sleep 2
        done
        echo "✅ PostgreSQL is ready!"

    - name: Wait for Redis
      run: |
        until redis-cli -h localhost -p 6379 ping; do
          echo "Waiting for Redis to be ready..."
          sleep 2
        done
        echo "✅ Redis is ready!"

    - name: Test database connection
      run: |
        python -c "
        import psycopg2
        try:
            conn = psycopg2.connect(
                dbname='test_db',
                user='postgres',
                password='postgres',
                host='localhost',
                port='5432'
            )
            print('✅ Database connection successful')
            conn.close()
        except Exception as e:
            print(f'❌ Database connection failed: {e}')
            exit(1)
        "

    - name: Run migrations
      run: |
        python manage.py migrate

    - name: Create superuser
      run: |
        echo "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('<EMAIL>', 'admin', 'Admin@123') if not User.objects.filter(email='<EMAIL>').exists() else None" | python manage.py shell

    - name: Check Django configuration
      run: |
        python manage.py check --deploy
        python manage.py showmigrations

    - name: Check environment variables
      run: |
        echo "Checking environment variables..."
        python -c "
        import os
        from decouple import config
        print(f'DEBUG: {config(\"DEBUG\", default=\"Not set\")}')
        print(f'DB_NAME: {config(\"DB_NAME\", default=\"Not set\")}')
        print(f'DB_USER: {config(\"DB_USER\", default=\"Not set\")}')
        print(f'DB_HOST: {config(\"DB_HOST\", default=\"Not set\")}')
        print(f'DB_PORT: {config(\"DB_PORT\", default=\"Not set\")}')
        print(f'FIREBASE_PROJECT_ID: {config(\"FIREBASE_PROJECT_ID\", default=\"Not set\")}')
        "

    - name: Check directory structure
      run: |
        echo "Checking directory structure..."
        ls -la
        echo "Static directory:"
        ls -la static/ || echo "Static directory not found"
        echo "Media directory:"
        ls -la media/ || echo "Media directory not found"
        echo "Project root:"
        ls -la esim_project/ || echo "esim_project directory not found"

    - name: Check Django app imports
      run: |
        echo "Checking Django app imports..."
        export DJANGO_SETTINGS_MODULE=esim_project.settings
        python -c "
        import os
        print(f'DJANGO_SETTINGS_MODULE: {os.environ.get(\"DJANGO_SETTINGS_MODULE\", \"NOT SET\")}')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        print(f'After setdefault: {os.environ.get(\"DJANGO_SETTINGS_MODULE\", \"NOT SET\")}')
        import django
        django.setup()
        from django.conf import settings
        print(f'Installed apps: {len(settings.INSTALLED_APPS)}')
        for app in settings.INSTALLED_APPS:
            print(f'  - {app}')
        print(f'Database engine: {settings.DATABASES[\"default\"][\"ENGINE\"]}')
        print(f'Database name: {settings.DATABASES[\"default\"][\"NAME\"]}')
        print(f'Database user: {settings.DATABASES[\"default\"][\"USER\"]}')
        print(f'Database host: {settings.DATABASES[\"default\"][\"HOST\"]}')
        print(f'Database port: {settings.DATABASES[\"default\"][\"PORT\"]}')
        "

    - name: Check Firebase configuration
      run: |
        echo "Checking Firebase configuration..."
        python -c "
        try:
            from api.firebase_storage import FirebaseStorage
            print('✅ Firebase storage module imported successfully')
            # In CI environment, Firebase will gracefully disable itself
            fs = FirebaseStorage()
            print(f'Firebase initialized: {fs._initialized}')
            print(f'Firebase bucket: {fs.bucket_name}')
            if not fs._initialized:
                print('✅ Firebase gracefully disabled in CI environment (expected)')
            else:
                print('✅ Firebase fully initialized')
        except Exception as e:
            print(f'⚠️  Firebase configuration issue: {e}')
            print('This is expected in CI environment')
        "

    - name: Check static files configuration
      run: |
        echo "Checking static files configuration..."
        python -c "
        import os
        from pathlib import Path
        base_dir = Path('.')
        static_dir = base_dir / 'static'
        staticfiles_dir = base_dir / 'staticfiles'
        print(f'Base directory: {base_dir.absolute()}')
        print(f'Static directory exists: {static_dir.exists()}')
        print(f'Staticfiles directory exists: {staticfiles_dir.exists()}')
        if static_dir.exists():
            print(f'Static directory contents: {list(static_dir.iterdir())}')
        if staticfiles_dir.exists():
            print(f'Staticfiles directory contents: {list(staticfiles_dir.iterdir())}')
        "

    - name: Check database connection through Django
      run: |
        echo "Checking database connection through Django..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute('SELECT version();')
                version = cursor.fetchone()
                print(f'✅ Database connection successful: {version[0]}')
        except Exception as e:
            print(f'❌ Database connection failed: {e}')
            exit(1)
        "

    - name: Check migration status
      run: |
        echo "Checking migration status..."
        python manage.py showmigrations --list
        python manage.py showmigrations --plan

    - name: Check app configuration
      run: |
        echo "Checking app configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        from django.apps import apps
        print('Checking app configurations...')
        for app_config in apps.get_app_configs():
            try:
                print(f'✅ {app_config.label}: {app_config.name}')
            except Exception as e:
                print(f'❌ {app_config.label}: {e}')
        "

    - name: Check URL configuration
      run: |
        echo "Checking URL configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.urls import get_resolver
        try:
            resolver = get_resolver()
            print(f'✅ URL resolver created successfully')
            print(f'URL patterns: {len(resolver.url_patterns)}')
        except Exception as e:
            print(f'❌ URL configuration failed: {e}')
            exit(1)
        "

    - name: Check model loading
      run: |
        echo "Checking model loading..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.apps import apps
        print('Checking model loading...')
        for model in apps.get_models():
            try:
                print(f'✅ {model._meta.app_label}.{model._meta.model_name}')
            except Exception as e:
                print(f'❌ {model._meta.app_label}.{model._meta.model_name}: {e}')
        "

    - name: Check serializer loading
      run: |
        echo "Checking serializer loading..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        try:
            from accounts.serializers import UserSerializer
            print('✅ UserSerializer loaded successfully')
            from clients.serializers import ClientSerializer
            print('✅ ClientSerializer loaded successfully')
            from esim_management.serializers import ESIMSerializer
            print('✅ ESIMSerializer loaded successfully')
            from orders.serializers import OrderSerializer
            print('✅ OrderSerializer loaded successfully')
            from payments.serializers import PaymentSerializer
            print('✅ PaymentSerializer loaded successfully')
            from reports.serializers import ReportSerializer
            print('✅ ReportSerializer loaded successfully')
            from resellers.serializers import ResellerSerializer
            print('✅ ResellerSerializer loaded successfully')
        except Exception as e:
            print(f'⚠️  Serializer loading issue: {e}')
            print('This might be expected in some cases')
        "

    - name: Check view loading
      run: |
        echo "Checking view loading..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        try:
            from accounts.views import UserViewSet
            print('✅ UserViewSet loaded successfully')
            from clients.views import ClientViewSet
            print('✅ ClientViewSet loaded successfully')
            from esim_management.views import ESIMViewSet
            print('✅ ESIMViewSet loaded successfully')
            from orders.views import OrderViewSet
            print('✅ OrderViewSet loaded successfully')
            from payments.views import PaymentViewSet
            print('✅ PaymentViewSet loaded successfully')
            from reports.views import ReportViewSet
            print('✅ ReportViewSet loaded successfully')
            from resellers.views import ResellerViewSet
            print('✅ ResellerViewSet loaded successfully')
        except Exception as e:
            print(f'⚠️  View loading issue: {e}')
            print('This might be expected in some cases')
        "

    - name: Check admin configuration
      run: |
        echo "Checking admin configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        try:
            from django.contrib import admin
            from django.contrib.admin import site
            print(f'✅ Admin site loaded successfully')
            print(f'Admin models registered: {len(site._registry)}')
            for model, admin_class in site._registry.items():
                print(f'  - {model._meta.app_label}.{model._meta.model_name}')
        except Exception as e:
            print(f'⚠️  Admin configuration issue: {e}')
            print('This might be expected in some cases')
        "

    - name: Check middleware configuration
      run: |
        echo "Checking middleware configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ Middleware loaded successfully')
        print(f'Middleware classes: {len(settings.MIDDLEWARE)}')
        for middleware in settings.MIDDLEWARE:
            print(f'  - {middleware}')
        "

    - name: Check template configuration
      run: |
        echo "Checking template configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        from django.template.loader import get_template
        print(f'✅ Template configuration loaded successfully')
        print(f'Template directories: {len(settings.TEMPLATES[0][\"DIRS\"])}')
        for template_dir in settings.TEMPLATES[0]['DIRS']:
            print(f'  - {template_dir}')
        print(f'Template loaders: {len(settings.TEMPLATES[0][\"OPTIONS\"][\"loaders\"]) if \"loaders\" in settings.TEMPLATES[0][\"OPTIONS\"] else \"Default loaders\"}')
        "

    - name: Check REST framework configuration
      run: |
        echo "Checking REST framework configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ REST framework configuration loaded successfully')
        print(f'Authentication classes: {len(settings.REST_FRAMEWORK[\"DEFAULT_AUTHENTICATION_CLASSES\"])}')
        for auth_class in settings.REST_FRAMEWORK['DEFAULT_AUTHENTICATION_CLASSES']:
            print(f'  - {auth_class}')
        print(f'Permission classes: {len(settings.REST_FRAMEWORK[\"DEFAULT_PERMISSION_CLASSES\"])}')
        for perm_class in settings.REST_FRAMEWORK['DEFAULT_PERMISSION_CLASSES']:
            print(f'  - {perm_class}')
        print(f'Filter backends: {len(settings.REST_FRAMEWORK[\"DEFAULT_FILTER_BACKENDS\"])}')
        for filter_backend in settings.REST_FRAMEWORK['DEFAULT_FILTER_BACKENDS']:
            print(f'  - {filter_backend}')
        "

    - name: Check JWT configuration
      run: |
        echo "Checking JWT configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ JWT configuration loaded successfully')
        print(f'Access token lifetime: {settings.SIMPLE_JWT[\"ACCESS_TOKEN_LIFETIME\"]}')
        print(f'Refresh token lifetime: {settings.SIMPLE_JWT[\"REFRESH_TOKEN_LIFETIME\"]}')
        print(f'Algorithm: {settings.SIMPLE_JWT[\"ALGORITHM\"]}')
        print(f'Signing key: {settings.SIMPLE_JWT[\"SIGNING_KEY\"] is not None}')
        print(f'Auth header types: {settings.SIMPLE_JWT[\"AUTH_HEADER_TYPES\"]}')
        "

    - name: Check Celery configuration
      run: |
        echo "Checking Celery configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ Celery configuration loaded successfully')
        try:
            from esim_project.celery import app
            print(f'Celery app: {app}')
            print(f'Celery broker URL: {app.conf.broker_url}')
            print(f'Celery result backend: {app.conf.result_backend}')
        except Exception as e:
            print(f'⚠️  Celery configuration issue: {e}')
            print('This might be expected in CI environment')
        "

    - name: Check CORS configuration
      run: |
        echo "Checking CORS configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ CORS configuration loaded successfully')
        print(f'CORS allowed origins: {len(settings.CORS_ALLOWED_ORIGINS)}')
        for origin in settings.CORS_ALLOWED_ORIGINS:
            print(f'  - {origin}')
        print(f'CORS allowed methods: {settings.CORS_ALLOW_METHODS if hasattr(settings, \"CORS_ALLOW_METHODS\") else \"Default methods\"}')
        print(f'CORS allowed headers: {settings.CORS_ALLOW_HEADERS if hasattr(settings, \"CORS_ALLOW_HEADERS\") else \"Default headers\"}')
        "

    - name: Check email configuration
      run: |
        echo "Checking email configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ Email configuration loaded successfully')
        print(f'Email backend: {settings.EMAIL_BACKEND}')
        print(f'Email host: {settings.EMAIL_HOST}')
        print(f'Email port: {settings.EMAIL_PORT}')
        print(f'Email use TLS: {settings.EMAIL_USE_TLS}')
        print(f'Email host user: {settings.EMAIL_HOST_USER}')
        print(f'Email host password: {\"Set\" if settings.EMAIL_HOST_PASSWORD else \"Not set\"}')
        "

    - name: Check logging configuration
      run: |
        echo "Checking logging configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        import logging
        print(f'✅ Logging configuration loaded successfully')
        print(f'Logging level: {settings.LOGGING[\"root\"][\"level\"] if \"root\" in settings.LOGGING else \"Default level\"}')
        print(f'Logging handlers: {len(settings.LOGGING[\"handlers\"]) if \"handlers\" in settings.LOGGING else \"Default handlers\"}')
        if \"handlers\" in settings.LOGGING:
            for handler_name, handler_config in settings.LOGGING[\"handlers\"].items():
                print(f'  - {handler_name}: {handler_config.get(\"class\", \"Unknown\")}')
        print(f'Logging loggers: {len(settings.LOGGING[\"loggers\"]) if \"loggers\" in settings.LOGGING else \"Default loggers\"}')
        if \"loggers\" in settings.LOGGING:
            for logger_name, logger_config in settings.LOGGING[\"loggers\"].items():
                print(f'  - {logger_name}: {logger_config.get(\"level\", \"Default\")}')
        "

    - name: Check cache configuration
      run: |
        echo "Checking cache configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ Cache configuration loaded successfully')
        print(f'Cache backend: {settings.CACHES[\"default\"][\"BACKEND\"]}')
        cache_config = settings.CACHES[\"default\"]
        if \"LOCATION\" in cache_config:
            print(f'Cache location: {cache_config[\"LOCATION\"]}')
        else:
            print(f'Cache location: Not applicable for {cache_config[\"BACKEND\"]}')
        if \"OPTIONS\" in cache_config:
            print(f'Cache timeout: {cache_config[\"OPTIONS\"].get(\"TIMEOUT\", \"Default\")}')
            print(f'Cache max entries: {cache_config[\"OPTIONS\"].get(\"MAX_ENTRIES\", \"Default\")}')
        else:
            print(f'Cache options: Not configured')
        "

    - name: Check session configuration
      run: |
        echo "Checking session configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ Session configuration loaded successfully')
        print(f'Session engine: {settings.SESSION_ENGINE}')
        print(f'Session cookie age: {settings.SESSION_COOKIE_AGE}')
        print(f'Session save every request: {settings.SESSION_SAVE_EVERY_REQUEST}')
        print(f'Session expire at browser close: {settings.SESSION_EXPIRE_AT_BROWSER_CLOSE}')
        print(f'Session cookie secure: {settings.SESSION_COOKIE_SECURE}')
        print(f'Session cookie httponly: {settings.SESSION_COOKIE_HTTPONLY}')
        "

    - name: Check security configuration
      run: |
        echo "Checking security configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ Security configuration loaded successfully')
        print(f'Debug mode: {settings.DEBUG}')
        print(f'Secret key: {\"Set\" if settings.SECRET_KEY else \"Not set\"}')
        print(f'Allowed hosts: {settings.ALLOWED_HOSTS}')
        print(f'CSRF trusted origins: {getattr(settings, \"CSRF_TRUSTED_ORIGINS\", [])}')
        print(f'X-Frame-Options: {getattr(settings, \"X_FRAME_OPTIONS\", \"Default\")}')
        print(f'Content type nosniff: {getattr(settings, \"SECURE_CONTENT_TYPE_NOSNIFF\", \"Default\")}')
        print(f'XSS protection: {getattr(settings, \"SECURE_BROWSER_XSS_FILTER\", \"Default\")}')
        "

    - name: Check internationalization configuration
      run: |
        echo "Checking internationalization configuration..."
        python -c "
        import os
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'esim_project.settings')
        import django
        django.setup()
        from django.conf import settings
        print(f'✅ Internationalization configuration loaded successfully')
        print(f'Language code: {settings.LANGUAGE_CODE}')
        print(f'Time zone: {settings.TIME_ZONE}')
        print(f'Use internationalization: {settings.USE_I18N}')
        print(f'Use timezone: {settings.USE_TZ}')
        print(f'Language: {len(settings.LANGUAGES)}')
        for code, name in settings.LANGUAGES:
            print(f'  - {code}: {name}')
        print(f'Locale paths: {len(settings.LOCALE_PATHS)}')
        for path in settings.LOCALE_PATHS:
            print(f'  - {path}')
        "

    - name: Check database migration status
      run: |
        echo "Checking database migration status..."
        python manage.py showmigrations --list
        python manage.py showmigrations --plan



    - name: Run tests
      run: |
        python -m pytest tests/ -v --cov=. --cov-report=xml --cov-report=term-missing --tb=short
      env:
        DJANGO_SETTINGS_MODULE: esim_project.settings
        PYTHONPATH: ${{ github.workspace }}

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
