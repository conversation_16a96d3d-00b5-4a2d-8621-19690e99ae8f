import random
from datetime import date, timedelta

from django.contrib.auth.hashers import make_password
from django.core.management.base import BaseCommand
from django.db import models, transaction
from django.utils import timezone

from accounts.models import User, UserProfile
from clients.models import Client, SupportTicket
from resellers.models import Reseller


class Command(BaseCommand):
    help = "Create comprehensive dummy public user data for testing"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=50,
            help="Number of public users to create (default: 50)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing public users before creating new ones",
        )
        parser.add_argument(
            "--with-orders",
            action="store_true",
            help="Create some users with existing orders",
        )

    def handle(self, *args, **options):
        count = options["count"]
        clear_existing = options["clear"]
        with_orders = options["with_orders"]

        if clear_existing:
            self.stdout.write("Clearing existing public users...")
            # Delete only direct users (public users)
            Client.objects.filter(client_type=Client.ClientType.DIRECT_USER).delete()
            self.stdout.write(self.style.SUCCESS("Existing public users cleared"))

        # Get existing resellers for potential relationships
        resellers = list(Reseller.objects.all())
        admin_users = list(User.objects.filter(role="admin"))

        if not admin_users:
            self.stdout.write(
                self.style.ERROR(
                    "No admin users found. Please create admin users first."
                )
            )
            return

        # Comprehensive data for public users
        first_names = [
            "John",
            "Jane",
            "Michael",
            "Sarah",
            "David",
            "Emily",
            "James",
            "Jessica",
            "Robert",
            "Amanda",
            "William",
            "Ashley",
            "Christopher",
            "Stephanie",
            "Daniel",
            "Nicole",
            "Matthew",
            "Elizabeth",
            "Anthony",
            "Helen",
            "Mark",
            "Deborah",
            "Donald",
            "Rachel",
            "Steven",
            "Carolyn",
            "Paul",
            "Janet",
            "Andrew",
            "Catherine",
            "Joshua",
            "Maria",
            "Kenneth",
            "Heather",
            "Kevin",
            "Diane",
            "Brian",
            "Ruth",
            "George",
            "Julie",
            "Edward",
            "Joyce",
            "Ronald",
            "Virginia",
            "Timothy",
            "Victoria",
            "Jason",
            "Kelly",
            "Jeffrey",
            "Lauren",
        ]

        last_names = [
            "Smith",
            "Johnson",
            "Williams",
            "Brown",
            "Jones",
            "Garcia",
            "Miller",
            "Davis",
            "Rodriguez",
            "Martinez",
            "Hernandez",
            "Lopez",
            "Gonzalez",
            "Wilson",
            "Anderson",
            "Thomas",
            "Taylor",
            "Moore",
            "Jackson",
            "Martin",
            "Lee",
            "Perez",
            "Thompson",
            "White",
            "Harris",
            "Sanchez",
            "Clark",
            "Ramirez",
            "Lewis",
            "Robinson",
            "Walker",
            "Young",
            "Allen",
            "King",
            "Wright",
            "Scott",
            "Torres",
            "Nguyen",
            "Hill",
            "Flores",
            "Green",
            "Adams",
            "Nelson",
            "Baker",
            "Hall",
            "Rivera",
            "Campbell",
            "Mitchell",
            "Carter",
            "Roberts",
        ]

        cities_countries = [
            {"city": "New York", "country": "USA", "state": "NY"},
            {"city": "Los Angeles", "country": "USA", "state": "CA"},
            {"city": "Chicago", "country": "USA", "state": "IL"},
            {"city": "Houston", "country": "USA", "state": "TX"},
            {"city": "Phoenix", "country": "USA", "state": "AZ"},
            {"city": "Philadelphia", "country": "USA", "state": "PA"},
            {"city": "San Antonio", "country": "USA", "state": "TX"},
            {"city": "San Diego", "country": "USA", "state": "CA"},
            {"city": "Dallas", "country": "USA", "state": "TX"},
            {"city": "San Jose", "country": "USA", "state": "CA"},
            {"city": "London", "country": "UK", "state": "England"},
            {"city": "Manchester", "country": "UK", "state": "England"},
            {"city": "Birmingham", "country": "UK", "state": "England"},
            {"city": "Leeds", "country": "UK", "state": "England"},
            {"city": "Liverpool", "country": "UK", "state": "England"},
            {"city": "Toronto", "country": "Canada", "state": "Ontario"},
            {"city": "Vancouver", "country": "Canada", "state": "British Columbia"},
            {"city": "Montreal", "country": "Canada", "state": "Quebec"},
            {"city": "Calgary", "country": "Canada", "state": "Alberta"},
            {"city": "Ottawa", "country": "Canada", "state": "Ontario"},
            {"city": "Sydney", "country": "Australia", "state": "NSW"},
            {"city": "Melbourne", "country": "Australia", "state": "Victoria"},
            {"city": "Brisbane", "country": "Australia", "state": "Queensland"},
            {"city": "Perth", "country": "Australia", "state": "WA"},
            {"city": "Adelaide", "country": "Australia", "state": "SA"},
            {"city": "Paris", "country": "France", "state": "Île-de-France"},
            {
                "city": "Marseille",
                "country": "France",
                "state": "Provence-Alpes-Côte d'Azur",
            },
            {"city": "Lyon", "country": "France", "state": "Auvergne-Rhône-Alpes"},
            {"city": "Toulouse", "country": "France", "state": "Occitanie"},
            {
                "city": "Nice",
                "country": "France",
                "state": "Provence-Alpes-Côte d'Azur",
            },
            {"city": "Berlin", "country": "Germany", "state": "Berlin"},
            {"city": "Hamburg", "country": "Germany", "state": "Hamburg"},
            {"city": "Munich", "country": "Germany", "state": "Bavaria"},
            {
                "city": "Cologne",
                "country": "Germany",
                "state": "North Rhine-Westphalia",
            },
            {"city": "Frankfurt", "country": "Germany", "state": "Hesse"},
            {"city": "Tokyo", "country": "Japan", "state": "Tokyo"},
            {"city": "Osaka", "country": "Japan", "state": "Osaka"},
            {"city": "Nagoya", "country": "Japan", "state": "Aichi"},
            {"city": "Sapporo", "country": "Japan", "state": "Hokkaido"},
            {"city": "Fukuoka", "country": "Japan", "state": "Fukuoka"},
            {"city": "Singapore", "country": "Singapore", "state": "Singapore"},
            {"city": "Dubai", "country": "UAE", "state": "Dubai"},
            {"city": "Mumbai", "country": "India", "state": "Maharashtra"},
            {"city": "Delhi", "country": "India", "state": "Delhi"},
            {"city": "Bangalore", "country": "India", "state": "Karnataka"},
            {"city": "Chennai", "country": "India", "state": "Tamil Nadu"},
            {"city": "Kolkata", "country": "India", "state": "West Bengal"},
        ]

        streets = [
            "Main Street",
            "Oak Avenue",
            "Pine Road",
            "Elm Street",
            "Maple Drive",
            "Cedar Lane",
            "Birch Boulevard",
            "Willow Way",
            "Cherry Court",
            "Poplar Place",
            "Sycamore Street",
            "Magnolia Avenue",
            "Juniper Road",
            "Cypress Lane",
            "Redwood Drive",
            "Spruce Street",
            "Fir Avenue",
            "Hemlock Road",
            "Larch Lane",
            "Aspen Drive",
            "Beech Street",
            "Hickory Avenue",
            "Walnut Road",
            "Pecan Lane",
            "Almond Drive",
        ]

        preferred_packages = [
            "Basic",
            "Premium",
            "Business",
            "Student",
            "Family",
            "Traveler",
            "Professional",
            "Enterprise",
        ]

        preferred_networks = [
            "Global",
            "Europe",
            "Asia",
            "Americas",
            "Africa",
            "Oceania",
            "Worldwide",
            "Regional",
        ]

        current_plans = [
            "Global eSIM Plan Basic",
            "Global eSIM Plan Premium",
            "Europe eSIM Plan",
            "Asia eSIM Plan",
            "Business eSIM Plan",
            "Student eSIM Plan",
            "Family eSIM Plan",
            "Traveler eSIM Plan",
        ]

        # Status distribution with probabilities
        status_distribution = [
            ("active", 0.75),
            ("inactive", 0.15),
            ("suspended", 0.05),
            ("blocked", 0.03),
            ("pending_verification", 0.02),
        ]

        # Tier distribution with probabilities
        tier_distribution = [("basic", 0.6), ("premium", 0.3), ("enterprise", 0.1)]

        self.stdout.write(f"Creating {count} dummy public users...")

        with transaction.atomic():
            for i in range(count):
                # Generate user data
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                full_name = f"{first_name} {last_name}"
                email = f"{first_name.lower()}.{last_name.lower()}{i+1}@example.com"
                phone_number = f"+1{random.randint(1000000000, 9999999999)}"

                # Select random location
                location = random.choice(cities_countries)

                # Generate address
                street_number = random.randint(100, 9999)
                street_name = random.choice(streets)
                address = f"{street_number} {street_name}, {location['city']}, {location['state']} {location['country']}"

                # Select status and tier based on probabilities
                status = random.choices(
                    [s[0] for s in status_distribution],
                    weights=[s[1] for s in status_distribution],
                )[0]

                tier = random.choices(
                    [t[0] for t in tier_distribution],
                    weights=[t[1] for t in tier_distribution],
                )[0]

                # Generate timestamps
                created_at = timezone.now() - timedelta(days=random.randint(1, 365))
                last_activity = (
                    created_at + timedelta(days=random.randint(1, 30))
                    if random.random() < 0.8
                    else None
                )

                # Plan dates
                plan_start_date = created_at.date() + timedelta(
                    days=random.randint(1, 30)
                )
                plan_end_date = plan_start_date + timedelta(
                    days=random.randint(30, 365)
                )

                # Create User first
                user = User.objects.create(
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    role="public_user",
                    country_code="+1",
                    phone_number=phone_number[2:],  # Remove +1 prefix
                    is_active=(status == "active"),
                    created_at=created_at,
                )

                # Create UserProfile
                profile = UserProfile.objects.create(
                    user=user,
                    date_of_birth=date(
                        1980 + random.randint(0, 40),
                        random.randint(1, 12),
                        random.randint(1, 28),
                    ),
                    gender=random.choice(["male", "female", "other"]),
                    address=address,
                    city=location["city"],
                    state=location["state"],
                    country=location["country"],
                    postal_code=f"{random.randint(10000, 99999)}",
                    emergency_contact_name=f"{random.choice(first_names)} {random.choice(last_names)}",
                    emergency_contact_phone=f"+1{random.randint(1000000000, 9999999999)}",
                    preferences={
                        "theme": random.choice(["light", "dark"]),
                        "notifications": random.choice([True, False]),
                        "language": random.choice(["en", "es", "fr", "de"]),
                    },
                )

                # Create Client (public user)
                client = Client.objects.create(
                    user=user,
                    client_type=Client.ClientType.DIRECT_USER,
                    reseller=None,  # Public users don't have resellers
                    full_name=full_name,
                    email=email,
                    phone_number=phone_number,
                    address=address,
                    city=location["city"],
                    country=location["country"],
                    status=status,
                    tier=tier,
                    last_activity=last_activity,
                    total_logins=random.randint(0, 50),
                    current_plan=(
                        random.choice(current_plans) if random.random() < 0.7 else None
                    ),
                    plan_start_date=plan_start_date if random.random() < 0.7 else None,
                    plan_end_date=plan_end_date if random.random() < 0.7 else None,
                    auto_renewal=random.choice([True, False]),
                    preferred_package=random.choice(preferred_packages),
                    preferred_network=random.choice(preferred_networks),
                    created_at=created_at,
                )

                # Create support tickets for some users
                if random.random() < 0.3:  # 30% of users have support tickets
                    num_tickets = random.randint(1, 3)
                    for j in range(num_tickets):
                        ticket_subjects = [
                            "SIM activation issue",
                            "Network connectivity problem",
                            "Billing inquiry",
                            "Data usage question",
                            "Travel plan activation",
                            "Account access issue",
                            "Payment method update",
                            "Plan upgrade request",
                        ]

                        ticket_descriptions = [
                            "I am having trouble activating my SIM card. Please help.",
                            "My network connection is not working properly.",
                            "I have a question about my recent bill.",
                            "How can I check my data usage?",
                            "I need help activating my travel plan.",
                            "I cannot access my account.",
                            "I want to update my payment method.",
                            "I would like to upgrade my current plan.",
                        ]

                        ticket_status = random.choice(
                            ["open", "in_progress", "resolved", "closed"]
                        )

                        SupportTicket.objects.create(
                            client=client,
                            subject=random.choice(ticket_subjects),
                            description=random.choice(ticket_descriptions),
                            status=ticket_status,
                            assigned_to=(
                                random.choice(admin_users)
                                if ticket_status in ["in_progress", "resolved"]
                                else None
                            ),
                            created_at=created_at
                            + timedelta(days=random.randint(1, 30)),
                            resolved_at=(
                                timezone.now()
                                if ticket_status in ["resolved", "closed"]
                                else None
                            ),
                        )

                if (i + 1) % 10 == 0:
                    self.stdout.write(f"Created {i + 1} public users...")

        self.stdout.write(
            self.style.SUCCESS(f"Successfully created {count} dummy public users!")
        )

        # Print summary
        total_public_users = Client.objects.filter(
            client_type=Client.ClientType.DIRECT_USER
        ).count()
        users_by_status = (
            Client.objects.filter(client_type=Client.ClientType.DIRECT_USER)
            .values("status")
            .annotate(count=models.Count("id"))
            .order_by("status")
        )

        users_by_tier = (
            Client.objects.filter(client_type=Client.ClientType.DIRECT_USER)
            .values("tier")
            .annotate(count=models.Count("id"))
            .order_by("tier")
        )

        users_by_country = (
            Client.objects.filter(client_type=Client.ClientType.DIRECT_USER)
            .values("country")
            .annotate(count=models.Count("id"))
            .order_by("-count")[:10]
        )

        self.stdout.write("\nPublic Users Summary:")
        self.stdout.write(f"Total Public Users: {total_public_users}")

        self.stdout.write("\nUsers by Status:")
        for status_count in users_by_status:
            self.stdout.write(f'{status_count["status"]}: {status_count["count"]}')

        self.stdout.write("\nUsers by Tier:")
        for tier_count in users_by_tier:
            self.stdout.write(f'{tier_count["tier"]}: {tier_count["count"]}')

        self.stdout.write("\nTop Countries:")
        for country_count in users_by_country:
            self.stdout.write(f'{country_count["country"]}: {country_count["count"]}')

        # Support tickets summary
        total_tickets = SupportTicket.objects.filter(
            client__client_type=Client.ClientType.DIRECT_USER
        ).count()
        tickets_by_status = (
            SupportTicket.objects.filter(
                client__client_type=Client.ClientType.DIRECT_USER
            )
            .values("status")
            .annotate(count=models.Count("id"))
            .order_by("status")
        )

        self.stdout.write(f"\nSupport Tickets: {total_tickets}")
        for ticket_status in tickets_by_status:
            self.stdout.write(f'{ticket_status["status"]}: {ticket_status["count"]}')

        if with_orders:
            self.stdout.write(
                '\n💡 Tip: Run "python manage.py create_dummy_orders" to create orders for these users!'
            )
