# Generated by Django 4.2.7 on 2025-08-12 07:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("payments", "0002_remove_paymentwebhook_gateway_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalpayment",
            name="invoice_file",
            field=models.TextField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="invoice_generated",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="invoice_generated_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="invoice_number",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="manual_approved_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="manual_approved_by",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="manual_payment_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="manual_payment_proof",
            field=models.TextField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="manual_rejection_reason",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="payment_type",
            field=models.CharField(
                choices=[
                    ("stripe", "Stripe"),
                    ("manual", "Manual Payment"),
                    ("bank_transfer", "Bank Transfer"),
                    ("cash", "Cash"),
                ],
                default="stripe",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="payment",
            name="invoice_file",
            field=models.FileField(blank=True, null=True, upload_to="invoices/"),
        ),
        migrations.AddField(
            model_name="payment",
            name="invoice_generated",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="payment",
            name="invoice_generated_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="invoice_number",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="manual_approved_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="manual_approved_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="approved_manual_payments",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="payment",
            name="manual_payment_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="manual_payment_proof",
            field=models.FileField(blank=True, null=True, upload_to="payment_proofs/"),
        ),
        migrations.AddField(
            model_name="payment",
            name="manual_rejection_reason",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="payment_type",
            field=models.CharField(
                choices=[
                    ("stripe", "Stripe"),
                    ("manual", "Manual Payment"),
                    ("bank_transfer", "Bank Transfer"),
                    ("cash", "Cash"),
                ],
                default="stripe",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="historicalpayment",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                    ("cancelled", "Cancelled"),
                    ("refunded", "Refunded"),
                    ("manual_approval", "Manual Approval Required"),
                    ("rejected", "Rejected"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="payment",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                    ("cancelled", "Cancelled"),
                    ("refunded", "Refunded"),
                    ("manual_approval", "Manual Approval Required"),
                    ("rejected", "Rejected"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
    ]
