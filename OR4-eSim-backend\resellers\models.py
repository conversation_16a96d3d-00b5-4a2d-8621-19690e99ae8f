from django.conf import settings
from django.db import models
from simple_history.models import HistoricalRecords


class ResellerActivationRequest(models.Model):
    """
    Model to handle reseller activation requests that require admin approval
    """

    class RequestStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        APPROVED = "approved", "Approved"
        REJECTED = "rejected", "Rejected"

    # User information
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="reseller_activation_request",
    )

    # Reseller information
    max_clients = models.PositiveIntegerField(default=100)
    max_sims = models.PositiveIntegerField(default=1000)
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, default=1000.00)

    # Request details
    status = models.CharField(
        max_length=20, choices=RequestStatus.choices, default=RequestStatus.PENDING
    )
    admin_notes = models.TextField(blank=True, null=True)

    # Admin approval
    approved_by = models.Foreign<PERSON>ey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="approved_reseller_requests",
    )
    approved_at = models.DateTimeField(null=True, blank=True)
    rejected_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "reseller_activation_requests"
        verbose_name = "Reseller Activation Request"
        verbose_name_plural = "Reseller Activation Requests"
        ordering = ["-created_at"]

    def __str__(self):
        return f"Reseller Request - {self.user.email} ({self.status})"

    @property
    def is_pending(self):
        return self.status == self.RequestStatus.PENDING

    @property
    def is_approved(self):
        return self.status == self.RequestStatus.APPROVED

    @property
    def is_rejected(self):
        return self.status == self.RequestStatus.REJECTED


class Reseller(models.Model):
    """
    Reseller model with account limits and management features
    """

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="reseller_profile",
    )

    # Account limits
    max_clients = models.PositiveIntegerField(default=100)
    max_sims = models.PositiveIntegerField(default=1000)
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, default=1000.00)
    current_credit = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Status and settings
    is_suspended = models.BooleanField(default=False)
    suspension_reason = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "resellers"
        verbose_name = "Reseller"
        verbose_name_plural = "Resellers"

    def __str__(self):
        return f"{self.user.email} - Reseller"

    @property
    def available_credit(self):
        return self.credit_limit - self.current_credit

    @property
    def total_revenue(self):
        from orders.models import Order

        return (
            Order.objects.filter(
                reseller=self, status__in=["completed", "delivered"]
            ).aggregate(total=models.Sum("total_amount"))["total"]
            or 0
        )

    @property
    def total_clients(self):
        return self.clients.count()

    @property
    def total_sims_used(self):
        from esim_management.models import ESIM

        return ESIM.objects.filter(client__reseller=self).count()
