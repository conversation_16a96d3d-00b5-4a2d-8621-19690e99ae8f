from django.db.models import Count, Q, Sum
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .models import (
    DeliveryTracking,
    Order,
    OrderItem,
    OrderNotification,
    OrderStatusHistory,
)
from .serializers import (
    DeliveryTrackingCreateSerializer,
    DeliveryTrackingSerializer,
    OrderItemSerializer,
    OrderNotificationSerializer,
    OrderSerializer,
    OrderStatusHistorySerializer,
)


class OrderViewSet(viewsets.ModelViewSet):
    """Order management API"""

    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter orders based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Order.objects.none()

        if self.request.user.is_admin:
            return Order.objects.all()
        elif self.request.user.is_reseller:
            return Order.objects.filter(reseller__user=self.request.user)
        elif self.request.user.is_client:
            return Order.objects.filter(client__user=self.request.user)
        elif self.request.user.is_public_user:
            return Order.objects.filter(client__user=self.request.user)
        return Order.objects.none()

    @action(detail=False, methods=["get"])
    def my_orders(self, request):
        """Get orders for current user"""
        orders = self.get_queryset()

        # Check if no orders found
        if not orders.exists():
            return Response(
                {"success": True, "message": "No orders found", "data": []},
                status=status.HTTP_204_NO_CONTENT,
            )

        serializer = self.get_serializer(orders, many=True)
        return Response(
            {
                "success": True,
                "message": "Orders retrieved successfully",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def mobile_app_orders(self, request):
        """Get orders placed via mobile app"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        queryset = self.get_queryset().filter(order_source="app")

        # Apply additional filters
        status_filter = request.GET.get("status")
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        city = request.GET.get("city")
        if city:
            queryset = queryset.filter(delivery_city__icontains=city)

        package = request.GET.get("package")
        if package:
            queryset = queryset.filter(product_name__icontains=package)

        date_from = request.GET.get("date_from")
        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)

        date_to = request.GET.get("date_to")
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        # Check if no orders found
        if not queryset.exists():
            return Response(
                {"success": True, "message": "No mobile app orders found", "data": []},
                status=status.HTTP_204_NO_CONTENT,
            )

        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {
                "success": True,
                "message": "Mobile app orders retrieved successfully",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def public_user_orders(self, request):
        """Get orders from public users (direct users)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        from clients.models import Client

        # Get orders from direct users (public users)
        public_user_ids = Client.objects.filter(
            client_type=Client.ClientType.DIRECT_USER
        ).values_list("user_id", flat=True)

        queryset = self.get_queryset().filter(client__user_id__in=public_user_ids)

        # Apply filters
        order_source = request.GET.get("order_source")
        if order_source:
            queryset = queryset.filter(order_source=order_source)

        status_filter = request.GET.get("status")
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        city = request.GET.get("city")
        if city:
            queryset = queryset.filter(delivery_city__icontains=city)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        # Check if no orders found
        if not queryset.exists():
            return Response(
                {"success": True, "message": "No public user orders found", "data": []},
                status=status.HTTP_204_NO_CONTENT,
            )

        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {
                "success": True,
                "message": "Public user orders retrieved successfully",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def analytics(self, request):
        """Get order analytics"""
        queryset = self.get_queryset()

        # Basic statistics
        total_orders = queryset.count()
        pending_orders = queryset.filter(status="pending").count()
        completed_orders = queryset.filter(status="completed").count()
        cancelled_orders = queryset.filter(status="cancelled").count()
        total_revenue = queryset.aggregate(total=Sum("total_amount"))["total"] or 0

        # Status distribution
        status_distribution = dict(
            queryset.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        analytics_data = {
            "total_orders": total_orders,
            "pending_orders": pending_orders,
            "completed_orders": completed_orders,
            "cancelled_orders": cancelled_orders,
            "total_revenue": float(total_revenue),
            "status_distribution": status_distribution,
        }

        return Response(
            {
                "success": True,
                "message": "Order analytics retrieved successfully",
                "data": analytics_data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def revenue_analytics(self, request):
        """Get order revenue analytics"""
        queryset = self.get_queryset()

        # Revenue statistics
        total_revenue = queryset.aggregate(total=Sum("total_amount"))["total"] or 0
        completed_revenue = (
            queryset.filter(status="completed").aggregate(total=Sum("total_amount"))[
                "total"
            ]
            or 0
        )
        pending_revenue = (
            queryset.filter(status="pending").aggregate(total=Sum("total_amount"))[
                "total"
            ]
            or 0
        )

        # Monthly revenue
        from datetime import timedelta

        current_month = timezone.now().month
        monthly_revenue = (
            queryset.filter(created_at__month=current_month).aggregate(
                total=Sum("total_amount")
            )["total"]
            or 0
        )

        revenue_data = {
            "total_revenue": float(total_revenue),
            "completed_revenue": float(completed_revenue),
            "pending_revenue": float(pending_revenue),
            "monthly_revenue": float(monthly_revenue),
            "average_order_value": (
                float(total_revenue / queryset.count()) if queryset.count() > 0 else 0
            ),
        }

        return Response(
            {
                "success": True,
                "message": "Revenue analytics retrieved successfully",
                "data": revenue_data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def status_analytics(self, request):
        """Get order status analytics"""
        queryset = self.get_queryset()

        # Status distribution
        status_distribution = dict(
            queryset.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        # Status percentages
        total_orders = queryset.count()
        status_percentages = {}
        for status_name, count in status_distribution.items():
            status_percentages[status_name] = (
                (count / total_orders * 100) if total_orders > 0 else 0
            )

        status_data = {
            "status_distribution": status_distribution,
            "status_percentages": status_percentages,
            "total_orders": total_orders,
        }

        return Response(
            {
                "success": True,
                "message": "Status analytics retrieved successfully",
                "data": status_data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get order statistics"""
        queryset = self.get_queryset()

        # Monthly statistics
        current_month = timezone.now().month
        monthly_orders = queryset.filter(created_at__month=current_month)

        stats = {
            "total_orders": queryset.count(),
            "pending_orders": queryset.filter(status="pending").count(),
            "completed_orders": queryset.filter(status="completed").count(),
            "cancelled_orders": queryset.filter(status="cancelled").count(),
            "monthly_orders": monthly_orders.count(),
            "total_revenue": queryset.aggregate(total=Sum("total_amount"))["total"]
            or 0,
            "monthly_revenue": monthly_orders.aggregate(total=Sum("total_amount"))[
                "total"
            ]
            or 0,
        }

        return Response(
            {
                "success": True,
                "message": "Order statistics retrieved successfully",
                "data": stats,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def mobile_app_statistics(self, request):
        """Get statistics for mobile app orders"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        from datetime import timedelta

        from django.utils import timezone

        # Get mobile app orders
        mobile_orders = self.get_queryset().filter(order_source="app")

        # Basic statistics
        total_mobile_orders = mobile_orders.count()
        total_mobile_revenue = (
            mobile_orders.filter(status__in=["completed", "delivered"]).aggregate(
                total=Sum("total_amount")
            )["total"]
            or 0
        )

        # Status distribution
        status_distribution = dict(
            mobile_orders.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        # City distribution
        city_distribution = dict(
            mobile_orders.values("delivery_city")
            .exclude(delivery_city__isnull=True)
            .exclude(delivery_city="")
            .annotate(count=Count("id"))
            .values_list("delivery_city", "count")
        )

        # Package distribution
        package_distribution = dict(
            mobile_orders.values("product_name")
            .annotate(count=Count("id"))
            .values_list("product_name", "count")
        )

        # Time-based statistics
        now = timezone.now()
        today = now.date()
        this_week = today - timedelta(days=7)
        this_month = today - timedelta(days=30)

        orders_today = mobile_orders.filter(created_at__date=today).count()
        orders_this_week = mobile_orders.filter(created_at__date__gte=this_week).count()
        orders_this_month = mobile_orders.filter(
            created_at__date__gte=this_month
        ).count()

        revenue_today = (
            mobile_orders.filter(
                created_at__date=today, status__in=["completed", "delivered"]
            ).aggregate(total=Sum("total_amount"))["total"]
            or 0
        )

        revenue_this_week = (
            mobile_orders.filter(
                created_at__date__gte=this_week, status__in=["completed", "delivered"]
            ).aggregate(total=Sum("total_amount"))["total"]
            or 0
        )

        revenue_this_month = (
            mobile_orders.filter(
                created_at__date__gte=this_month, status__in=["completed", "delivered"]
            ).aggregate(total=Sum("total_amount"))["total"]
            or 0
        )

        # Recent orders
        recent_orders = mobile_orders.order_by("-created_at")[:10]

        statistics = {
            "overview": {
                "total_mobile_orders": total_mobile_orders,
                "total_mobile_revenue": float(total_mobile_revenue),
                "average_order_value": (
                    float(total_mobile_revenue / total_mobile_orders)
                    if total_mobile_orders > 0
                    else 0
                ),
            },
            "time_based": {
                "orders_today": orders_today,
                "orders_this_week": orders_this_week,
                "orders_this_month": orders_this_month,
                "revenue_today": float(revenue_today),
                "revenue_this_week": float(revenue_this_week),
                "revenue_this_month": float(revenue_this_month),
            },
            "status_distribution": status_distribution,
            "geographic_distribution": {
                "city_distribution": city_distribution,
                "top_cities": sorted(
                    city_distribution.items(), key=lambda x: x[1], reverse=True
                )[:5],
            },
            "package_distribution": {
                "package_distribution": package_distribution,
                "top_packages": sorted(
                    package_distribution.items(), key=lambda x: x[1], reverse=True
                )[:5],
            },
            "recent_orders": self.get_serializer(recent_orders, many=True).data,
        }

        return Response(
            {
                "success": True,
                "message": "Mobile app statistics retrieved successfully",
                "data": statistics,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def sim_orders(self, request):
        """Get SIM orders with advanced filtering (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Get SIM orders
        queryset = self.get_queryset().filter(order_type="sim")

        # Apply filters
        # Status filter
        status_filter = request.GET.get("status")
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Order source filter
        order_source = request.GET.get("order_source")
        if order_source:
            queryset = queryset.filter(order_source=order_source)

        # City filter
        city = request.GET.get("city")
        if city:
            queryset = queryset.filter(delivery_city__icontains=city)

        # Country filter
        country = request.GET.get("country")
        if country:
            queryset = queryset.filter(delivery_country__icontains=country)

        # Date range filters
        date_from = request.GET.get("date_from")
        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)

        date_to = request.GET.get("date_to")
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        # Product filter
        product = request.GET.get("product")
        if product:
            queryset = queryset.filter(product_name__icontains=product)

        # Reseller filter
        reseller_id = request.GET.get("reseller_id")
        if reseller_id:
            queryset = queryset.filter(reseller_id=reseller_id)

        # Client filter
        client_id = request.GET.get("client_id")
        if client_id:
            queryset = queryset.filter(client_id=client_id)

        # Amount range filters
        min_amount = request.GET.get("min_amount")
        if min_amount:
            queryset = queryset.filter(total_amount__gte=min_amount)

        max_amount = request.GET.get("max_amount")
        if max_amount:
            queryset = queryset.filter(total_amount__lte=max_amount)

        # Sort by
        sort_by = request.GET.get("sort_by", "-created_at")
        if sort_by in [
            "created_at",
            "-created_at",
            "total_amount",
            "-total_amount",
            "status",
            "-status",
        ]:
            queryset = queryset.order_by(sort_by)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        # Check if no orders found
        if not queryset.exists():
            return Response(
                {"success": True, "message": "No SIM orders found", "data": []},
                status=status.HTTP_204_NO_CONTENT,
            )

        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {
                "success": True,
                "message": "SIM orders retrieved successfully",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def sim_order_statistics(self, request):
        """Get statistics for SIM orders (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        from datetime import timedelta

        # Get SIM orders
        sim_orders = self.get_queryset().filter(order_type="sim")

        # Basic statistics
        total_sim_orders = sim_orders.count()
        total_sim_revenue = (
            sim_orders.filter(
                status__in=["completed", "delivered", "activated"]
            ).aggregate(total=Sum("total_amount"))["total"]
            or 0
        )

        # Status distribution
        status_distribution = dict(
            sim_orders.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        # Source distribution
        source_distribution = dict(
            sim_orders.values("order_source")
            .annotate(count=Count("id"))
            .values_list("order_source", "count")
        )

        # City distribution
        city_distribution = dict(
            sim_orders.values("delivery_city")
            .exclude(delivery_city__isnull=True)
            .exclude(delivery_city="")
            .annotate(count=Count("id"))
            .values_list("delivery_city", "count")
        )

        # Product distribution
        product_distribution = dict(
            sim_orders.values("product_name")
            .annotate(count=Count("id"))
            .values_list("product_name", "count")
        )

        # Time-based statistics
        now = timezone.now()
        today = now.date()
        this_week = today - timedelta(days=7)
        this_month = today - timedelta(days=30)

        orders_today = sim_orders.filter(created_at__date=today).count()
        orders_this_week = sim_orders.filter(created_at__date__gte=this_week).count()
        orders_this_month = sim_orders.filter(created_at__date__gte=this_month).count()

        revenue_today = (
            sim_orders.filter(
                created_at__date=today,
                status__in=["completed", "delivered", "activated"],
            ).aggregate(total=Sum("total_amount"))["total"]
            or 0
        )

        revenue_this_week = (
            sim_orders.filter(
                created_at__date__gte=this_week,
                status__in=["completed", "delivered", "activated"],
            ).aggregate(total=Sum("total_amount"))["total"]
            or 0
        )

        revenue_this_month = (
            sim_orders.filter(
                created_at__date__gte=this_month,
                status__in=["completed", "delivered", "activated"],
            ).aggregate(total=Sum("total_amount"))["total"]
            or 0
        )

        # Delivery statistics
        pending_delivery = sim_orders.filter(status="confirmed").count()
        in_transit = sim_orders.filter(status="dispatched").count()
        delivered = sim_orders.filter(status="delivered").count()
        activated = sim_orders.filter(status="activated").count()

        statistics = {
            "overview": {
                "total_sim_orders": total_sim_orders,
                "total_sim_revenue": float(total_sim_revenue),
                "average_order_value": (
                    float(total_sim_revenue / total_sim_orders)
                    if total_sim_orders > 0
                    else 0
                ),
            },
            "delivery_status": {
                "pending_delivery": pending_delivery,
                "in_transit": in_transit,
                "delivered": delivered,
                "activated": activated,
            },
            "time_based": {
                "orders_today": orders_today,
                "orders_this_week": orders_this_week,
                "orders_this_month": orders_this_month,
                "revenue_today": float(revenue_today),
                "revenue_this_week": float(revenue_this_week),
                "revenue_this_month": float(revenue_this_month),
            },
            "status_distribution": status_distribution,
            "source_distribution": source_distribution,
            "geographic_distribution": {
                "city_distribution": city_distribution,
                "top_cities": sorted(
                    city_distribution.items(), key=lambda x: x[1], reverse=True
                )[:5],
            },
            "product_distribution": {
                "product_distribution": product_distribution,
                "top_products": sorted(
                    product_distribution.items(), key=lambda x: x[1], reverse=True
                )[:5],
            },
        }

        return Response(
            {
                "success": True,
                "message": "SIM order statistics retrieved successfully",
                "data": statistics,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def assign_delivery(self, request, pk=None):
        """Assign order for delivery (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            order = self.get_object()
            courier_name = request.data.get("courier_name")
            tracking_number = request.data.get("tracking_number")
            estimated_delivery = request.data.get("estimated_delivery")
            notes = request.data.get("notes", "")

            if not courier_name:
                return Response(
                    {"error": "Courier name is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Update order status to dispatched
            if order.status in ["confirmed", "processing"]:
                old_status = order.status
                order.status = "dispatched"
                order.dispatched_at = timezone.now()
                order.save()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    old_status=old_status,
                    new_status="dispatched",
                    changed_by=request.user,
                    notes=f"Assigned to {courier_name} for delivery. {notes}",
                )

                # Create or update delivery tracking
                tracking, created = DeliveryTracking.objects.get_or_create(order=order)
                tracking.courier_name = courier_name
                tracking.tracking_number = tracking_number
                tracking.status = "in_transit"
                if estimated_delivery:
                    tracking.estimated_delivery = estimated_delivery
                tracking.save()

                # Send notification if requested
                if request.data.get("send_notification", False):
                    self._send_order_notification(order, "dispatched", request.user)

                return Response(
                    {
                        "success": True,
                        "message": "Order assigned for delivery successfully",
                        "data": {
                            "order_id": order.id,
                            "order_number": order.order_number,
                            "courier_name": courier_name,
                            "tracking_number": tracking_number,
                            "estimated_delivery": estimated_delivery,
                            "assigned_by": request.user.email,
                            "assigned_at": timezone.now().isoformat(),
                        },
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "error": f"Order cannot be assigned for delivery in current status: {order.status}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to assign delivery: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def mark_delivered(self, request, pk=None):
        """Mark order as delivered (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            order = self.get_object()
            notes = request.data.get("notes", "")
            send_notification = request.data.get("send_notification", False)

            if order.status != "dispatched":
                return Response(
                    {
                        "error": f"Order cannot be marked as delivered in current status: {order.status}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Update order status
            old_status = order.status
            order.status = "delivered"
            order.delivered_at = timezone.now()
            order.save()

            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                old_status=old_status,
                new_status="delivered",
                changed_by=request.user,
                notes=notes,
            )

            # Update delivery tracking
            if hasattr(order, "delivery_tracking"):
                order.delivery_tracking.status = "delivered"
                order.delivery_tracking.actual_delivery = timezone.now()
                order.delivery_tracking.save()

            # Send notification if requested
            if send_notification:
                self._send_order_notification(order, "delivered", request.user)

            return Response(
                {
                    "success": True,
                    "message": "Order marked as delivered successfully",
                    "data": {
                        "order_id": order.id,
                        "order_number": order.order_number,
                        "old_status": old_status,
                        "new_status": "delivered",
                        "delivered_at": order.delivered_at.isoformat(),
                        "updated_by": request.user.email,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to mark as delivered: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def mark_activated(self, request, pk=None):
        """Mark order as activated (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            order = self.get_object()
            notes = request.data.get("notes", "")
            send_notification = request.data.get("send_notification", False)

            if order.status not in ["delivered", "completed"]:
                return Response(
                    {
                        "error": f"Order cannot be marked as activated in current status: {order.status}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Update order status
            old_status = order.status
            order.status = "activated"
            order.save()

            # Create status history
            OrderStatusHistory.objects.create(
                order=order,
                old_status=old_status,
                new_status="activated",
                changed_by=request.user,
                notes=notes,
            )

            # Send notification if requested
            if send_notification:
                self._send_order_notification(order, "activated", request.user)

            return Response(
                {
                    "success": True,
                    "message": "Order marked as activated successfully",
                    "data": {
                        "order_id": order.id,
                        "order_number": order.order_number,
                        "old_status": old_status,
                        "new_status": "activated",
                        "activated_at": timezone.now().isoformat(),
                        "updated_by": request.user.email,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to mark as activated: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def cancel_order(self, request, pk=None):
        """Cancel an order"""
        try:
            order = self.get_object()
            if order.status in ["pending", "processing"]:
                order.status = "cancelled"
                order.cancelled_at = timezone.now()
                order.save()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    old_status=order.status,
                    new_status="cancelled",
                    changed_by=request.user,
                    notes="Order cancelled by user",
                )

                return Response(
                    {
                        "success": True,
                        "message": "Order cancelled successfully",
                        "data": {
                            "order_id": order.id,
                            "order_number": order.order_number,
                            "status": order.status,
                            "cancelled_at": order.cancelled_at.isoformat(),
                        },
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "success": False,
                        "error": "Order cannot be cancelled in current status",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to cancel order: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def update_status(self, request, pk=None):
        """Update order status (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            order = self.get_object()
            new_status = request.data.get("status")
            notes = request.data.get("notes", "")
            courier_name = request.data.get("courier_name", "")
            tracking_number = request.data.get("tracking_number", "")

            if not new_status:
                return Response(
                    {"error": "Status is required"}, status=status.HTTP_400_BAD_REQUEST
                )

            # Validate status transition
            valid_transitions = {
                "pending": ["confirmed", "cancelled"],
                "confirmed": ["processing", "cancelled"],
                "processing": ["dispatched", "cancelled"],
                "dispatched": ["delivered", "cancelled"],
                "delivered": ["activated", "completed"],
                "activated": ["completed"],
                "completed": [],
                "cancelled": [],
                "refunded": [],
            }

            if (
                order.status in valid_transitions
                and new_status not in valid_transitions[order.status]
            ):
                return Response(
                    {
                        "error": f"Invalid status transition from {order.status} to {new_status}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Update order status
            old_status = order.status
            order.status = new_status

            # Update timestamps based on status
            if new_status == "confirmed":
                order.confirmed_at = timezone.now()
            elif new_status == "dispatched":
                order.dispatched_at = timezone.now()
            elif new_status == "delivered":
                order.delivered_at = timezone.now()
            elif new_status == "completed":
                order.completed_at = timezone.now()
            elif new_status == "cancelled":
                order.cancelled_at = timezone.now()

                order.save()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order,
                    old_status=old_status,
                    new_status=new_status,
                    changed_by=request.user,
                    notes=notes,
                )

            # Update delivery tracking if provided
            if courier_name or tracking_number:
                tracking, created = DeliveryTracking.objects.get_or_create(order=order)
                if courier_name:
                    tracking.courier_name = courier_name
                if tracking_number:
                    tracking.tracking_number = tracking_number
                tracking.status = (
                    "in_transit"
                    if new_status == "dispatched"
                    else "delivered" if new_status == "delivered" else "pending"
                )
                tracking.save()

            # Send notification (optional)
            if request.data.get("send_notification", False):
                self._send_order_notification(order, new_status, request.user)

            return Response(
                {
                    "success": True,
                    "message": "Order status updated successfully",
                    "data": {
                        "order_id": order.id,
                        "order_number": order.order_number,
                        "old_status": old_status,
                        "new_status": new_status,
                        "updated_by": request.user.email,
                        "updated_at": timezone.now().isoformat(),
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to update status: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def _send_order_notification(self, order, status, user):
        """Send notification for order status change"""
        try:
            notification_type_map = {
                "confirmed": "ORDER_CONFIRMED",
                "dispatched": "ORDER_DISPATCHED",
                "delivered": "ORDER_DELIVERED",
                "activated": "ORDER_ACTIVATED",
                "cancelled": "ORDER_CANCELLED",
            }

            if status in notification_type_map:
                notification_type = notification_type_map[status]
                recipient = (
                    order.client.email if order.client else order.client.phone_number
                )

                if recipient:
                    OrderNotification.objects.create(
                        order=order,
                        notification_type=notification_type,
                        notification_method="email" if "@" in recipient else "sms",
                        recipient=recipient,
                        message=f"Your order {order.order_number} has been {status}",
                        delivered=True,
                        delivered_at=timezone.now(),
                    )
        except Exception as e:
            # Log error but don't fail the status update
            print(f"Failed to send notification: {str(e)}")


class OrderItemViewSet(viewsets.ModelViewSet):
    """Order item management API"""

    queryset = OrderItem.objects.all()
    serializer_class = OrderItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter order items based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return OrderItem.objects.none()

        if self.request.user.is_admin:
            return OrderItem.objects.all()
        elif self.request.user.is_reseller:
            return OrderItem.objects.filter(order__reseller__user=self.request.user)
        elif self.request.user.is_client:
            return OrderItem.objects.filter(order__client__user=self.request.user)
        elif self.request.user.is_public_user:
            return OrderItem.objects.filter(order__client__user=self.request.user)
        return OrderItem.objects.none()


class OrderStatusHistoryViewSet(viewsets.ModelViewSet):
    """Order status history API"""

    queryset = OrderStatusHistory.objects.all()
    serializer_class = OrderStatusHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter status history based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return OrderStatusHistory.objects.none()

        if self.request.user.is_admin:
            return OrderStatusHistory.objects.all()
        elif self.request.user.is_reseller:
            return OrderStatusHistory.objects.filter(
                order__reseller__user=self.request.user
            )
        elif self.request.user.is_client:
            return OrderStatusHistory.objects.filter(
                order__client__user=self.request.user
            )
        elif self.request.user.is_public_user:
            return OrderStatusHistory.objects.filter(
                order__client__user=self.request.user
            )
        return OrderStatusHistory.objects.none()


class DeliveryTrackingViewSet(viewsets.ModelViewSet):
    """Delivery tracking API"""

    queryset = DeliveryTracking.objects.all()
    serializer_class = DeliveryTrackingSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Return appropriate serializer class"""
        if self.action == "create":
            return DeliveryTrackingCreateSerializer
        return DeliveryTrackingSerializer

    def get_queryset(self):
        """Filter delivery tracking based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return DeliveryTracking.objects.none()

        if self.request.user.is_admin:
            return DeliveryTracking.objects.all()
        elif self.request.user.is_reseller:
            return DeliveryTracking.objects.filter(
                order__reseller__user=self.request.user
            )
        elif self.request.user.is_client:
            return DeliveryTracking.objects.filter(
                order__client__user=self.request.user
            )
        elif self.request.user.is_public_user:
            return DeliveryTracking.objects.filter(
                order__client__user=self.request.user
            )
        return DeliveryTracking.objects.none()

    @action(detail=False, methods=["get"], url_path="by-order/(?P<order_id>[^/.]+)")
    def by_order(self, request, order_id=None):
        """Get delivery tracking by order ID"""
        try:
            from orders.models import Order

            order = Order.objects.get(id=order_id)

            # Check permissions
            if not self._has_order_access(order):
                return Response(
                    {"error": "Access denied"}, status=status.HTTP_403_FORBIDDEN
                )

            tracking = DeliveryTracking.objects.filter(order=order).first()

            if not tracking:
                return Response(
                    {"message": "No tracking information found for this order"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = self.get_serializer(tracking)
            return Response(
                {
                    "success": True,
                    "message": "Delivery tracking retrieved successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Order.DoesNotExist:
            return Response(
                {"error": "Order not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"Failed to retrieve tracking: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def _has_order_access(self, order):
        """Check if user has access to the order"""
        if self.request.user.is_admin:
            return True
        elif (
            self.request.user.is_reseller
            and order.reseller
            and order.reseller.user == self.request.user
        ):
            return True
        elif (
            self.request.user.is_client
            and order.client
            and order.client.user == self.request.user
        ):
            return True
        elif (
            self.request.user.is_public_user
            and order.client
            and order.client.user == self.request.user
        ):
            return True
        return False

    @action(detail=True, methods=["post"])
    def update_tracking(self, request, pk=None):
        """Update delivery tracking status"""
        try:
            tracking = self.get_object()
            status = request.data.get("status")
            location = request.data.get("location", "")
            notes = request.data.get("notes", "")

            if not status:
                return Response(
                    {"error": "Status is required"}, status=status.HTTP_400_BAD_REQUEST
                )

                tracking.status = status
                tracking.current_location = location
                tracking.updated_at = timezone.now()
                tracking.save()

            return Response(
                {
                    "success": True,
                    "message": "Tracking updated successfully",
                    "data": {
                        "tracking_id": tracking.id,
                        "order_number": tracking.order.order_number,
                        "status": tracking.status,
                        "current_location": tracking.current_location,
                        "updated_at": tracking.updated_at.isoformat(),
                    },
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to update tracking: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class OrderNotificationViewSet(viewsets.ModelViewSet):
    """Order notification API"""

    queryset = OrderNotification.objects.all()
    serializer_class = OrderNotificationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter notifications based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return OrderNotification.objects.none()

        if self.request.user.is_admin:
            return OrderNotification.objects.all()
        elif self.request.user.is_reseller:
            return OrderNotification.objects.filter(
                order__reseller__user=self.request.user
            )
        elif self.request.user.is_client:
            return OrderNotification.objects.filter(
                order__client__user=self.request.user
            )
        elif self.request.user.is_public_user:
            return OrderNotification.objects.filter(
                order__client__user=self.request.user
            )
        return OrderNotification.objects.none()

    @action(detail=False, methods=["get"], url_path="by-order/(?P<order_id>[^/.]+)")
    def by_order(self, request, order_id=None):
        """Get notifications by order ID"""
        try:
            from orders.models import Order

            order = Order.objects.get(id=order_id)

            # Check permissions
            if not self._has_order_access(order):
                return Response(
                    {"error": "Access denied"}, status=status.HTTP_403_FORBIDDEN
                )

            notifications = OrderNotification.objects.filter(order=order).order_by(
                "-sent_at"
            )

            serializer = self.get_serializer(notifications, many=True)
            return Response(
                {
                    "success": True,
                    "message": "Order notifications retrieved successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Order.DoesNotExist:
            return Response(
                {"error": "Order not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"Failed to retrieve notifications: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["post"], url_path="send/(?P<order_id>[^/.]+)")
    def send_notification(self, request, order_id=None):
        """Send notification for an order"""
        try:
            from orders.models import Order

            order = Order.objects.get(id=order_id)

            # Check permissions
            if not self._has_order_access(order):
                return Response(
                    {"error": "Access denied"}, status=status.HTTP_403_FORBIDDEN
                )

            notification_type = request.data.get("notification_type")
            subject = request.data.get("subject", "")
            message = request.data.get("message", "")

            if not notification_type:
                return Response(
                    {"error": "Notification type is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create notification
            notification = OrderNotification.objects.create(
                order=order,
                notification_type=notification_type,
                notification_method="email",  # Default to email
                recipient=order.client.email if order.client else "",
                message=message or f"Order {order.order_number} notification",
                delivered=True,
                delivered_at=timezone.now(),
            )

            serializer = self.get_serializer(notification)
            return Response(
                {
                    "success": True,
                    "message": "Notification sent successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Order.DoesNotExist:
            return Response(
                {"error": "Order not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"Failed to send notification: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def _has_order_access(self, order):
        """Check if user has access to the order"""
        if self.request.user.is_admin:
            return True
        elif (
            self.request.user.is_reseller
            and order.reseller
            and order.reseller.user == self.request.user
        ):
            return True
        elif (
            self.request.user.is_client
            and order.client
            and order.client.user == self.request.user
        ):
            return True
        elif (
            self.request.user.is_public_user
            and order.client
            and order.client.user == self.request.user
        ):
            return True
        return False
