import random
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.db import models, transaction
from django.utils import timezone

from accounts.models import User
from clients.models import Client
from orders.models import (
    DeliveryTracking,
    Order,
    OrderItem,
    OrderNotification,
    OrderStatusHistory,
)
from resellers.models import Reseller


class Command(BaseCommand):
    help = "Create dummy order data for testing"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=50,
            help="Number of orders to create (default: 50)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing orders before creating new ones",
        )

    def handle(self, *args, **options):
        count = options["count"]
        clear_existing = options["clear"]

        if clear_existing:
            self.stdout.write("Clearing existing orders...")
            Order.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Existing orders cleared"))

        # Get existing clients and resellers
        clients = list(Client.objects.all())
        resellers = list(Reseller.objects.all())
        users = list(User.objects.filter(role__in=["admin", "reseller"]))

        if not clients:
            self.stdout.write(
                self.style.ERROR("No clients found. Please create clients first.")
            )
            return

        if not resellers:
            self.stdout.write(
                self.style.ERROR("No resellers found. Please create resellers first.")
            )
            return

        if not users:
            self.stdout.write(
                self.style.ERROR("No users found. Please create users first.")
            )
            return

        # Product data
        products = [
            {
                "name": "Global eSIM Plan Basic",
                "description": "Basic global eSIM with 1GB data for 30 days",
                "price": Decimal("19.99"),
                "type": "esim",
            },
            {
                "name": "Global eSIM Plan Premium",
                "description": "Premium global eSIM with 5GB data for 30 days",
                "price": Decimal("49.99"),
                "type": "esim",
            },
            {
                "name": "Europe eSIM Plan",
                "description": "Europe-specific eSIM with 3GB data for 15 days",
                "price": Decimal("29.99"),
                "type": "esim",
            },
            {
                "name": "Asia eSIM Plan",
                "description": "Asia-specific eSIM with 2GB data for 10 days",
                "price": Decimal("24.99"),
                "type": "esim",
            },
            {
                "name": "Physical SIM Card Global",
                "description": "Physical SIM card with global coverage",
                "price": Decimal("15.99"),
                "type": "sim",
            },
            {
                "name": "Physical SIM Card Europe",
                "description": "Physical SIM card for Europe",
                "price": Decimal("12.99"),
                "type": "sim",
            },
            {
                "name": "Business eSIM Plan",
                "description": "Business eSIM with 10GB data and priority support",
                "price": Decimal("89.99"),
                "type": "esim",
            },
            {
                "name": "Student eSIM Plan",
                "description": "Student discount eSIM with 2GB data",
                "price": Decimal("14.99"),
                "type": "esim",
            },
        ]

        # Delivery cities and countries
        delivery_locations = [
            {"city": "New York", "country": "USA"},
            {"city": "Los Angeles", "country": "USA"},
            {"city": "Chicago", "country": "USA"},
            {"city": "London", "country": "UK"},
            {"city": "Paris", "country": "France"},
            {"city": "Berlin", "country": "Germany"},
            {"city": "Madrid", "country": "Spain"},
            {"city": "Rome", "country": "Italy"},
            {"city": "Amsterdam", "country": "Netherlands"},
            {"city": "Toronto", "country": "Canada"},
            {"city": "Vancouver", "country": "Canada"},
            {"city": "Sydney", "country": "Australia"},
            {"city": "Melbourne", "country": "Australia"},
            {"city": "Tokyo", "country": "Japan"},
            {"city": "Seoul", "country": "South Korea"},
            {"city": "Singapore", "country": "Singapore"},
            {"city": "Dubai", "country": "UAE"},
            {"city": "Mumbai", "country": "India"},
            {"city": "Delhi", "country": "India"},
            {"city": "São Paulo", "country": "Brazil"},
        ]

        # Courier companies
        couriers = [
            "FedEx",
            "UPS",
            "DHL",
            "USPS",
            "Royal Mail",
            "Canada Post",
            "Australia Post",
        ]

        # Status progression with probabilities
        status_progression = [
            ("pending", 0.1),
            ("confirmed", 0.15),
            ("processing", 0.2),
            ("dispatched", 0.25),
            ("delivered", 0.2),
            ("activated", 0.05),
            ("completed", 0.03),
            ("cancelled", 0.02),
        ]

        self.stdout.write(f"Creating {count} dummy orders...")

        with transaction.atomic():
            for i in range(count):
                # Select random client and reseller
                client = random.choice(clients)
                reseller = random.choice(resellers) if random.random() < 0.7 else None

                # Select random product
                product = random.choice(products)

                # Select random delivery location
                location = random.choice(delivery_locations)

                # Generate order number
                order_number = f"ORD-{timezone.now().strftime('%Y%m%d')}-{i+1:04d}"

                # Calculate pricing
                quantity = random.randint(1, 3)
                unit_price = product["price"]
                subtotal = unit_price * quantity
                tax_amount = subtotal * Decimal("0.08")  # 8% tax
                delivery_fee = (
                    Decimal("5.99") if product["type"] == "sim" else Decimal("0.00")
                )
                total_amount = subtotal + tax_amount + delivery_fee

                # Select order source
                order_source = random.choice(["app", "reseller", "admin"])

                # Select status based on probabilities
                status = random.choices(
                    [s[0] for s in status_progression],
                    weights=[s[1] for s in status_progression],
                )[0]

                # Generate timestamps based on status
                created_at = timezone.now() - timedelta(days=random.randint(1, 90))
                confirmed_at = None
                dispatched_at = None
                delivered_at = None
                completed_at = None
                cancelled_at = None

                if status in [
                    "confirmed",
                    "processing",
                    "dispatched",
                    "delivered",
                    "activated",
                    "completed",
                ]:
                    confirmed_at = created_at + timedelta(hours=random.randint(1, 24))

                if status in ["dispatched", "delivered", "activated", "completed"]:
                    dispatched_at = confirmed_at + timedelta(
                        hours=random.randint(2, 48)
                    )

                if status in ["delivered", "activated", "completed"]:
                    delivered_at = dispatched_at + timedelta(
                        hours=random.randint(1, 72)
                    )

                if status == "completed":
                    completed_at = delivered_at + timedelta(hours=random.randint(1, 24))

                if status == "cancelled":
                    cancelled_at = created_at + timedelta(hours=random.randint(1, 12))

                # Create order
                order = Order.objects.create(
                    order_number=order_number,
                    order_type=product["type"],
                    order_source=order_source,
                    status=status,
                    reseller=reseller,
                    client=client,
                    product_name=product["name"],
                    product_description=product["description"],
                    quantity=quantity,
                    unit_price=unit_price,
                    subtotal=subtotal,
                    tax_amount=tax_amount,
                    delivery_fee=delivery_fee,
                    total_amount=total_amount,
                    delivery_address=f"{random.randint(100, 9999)} {random.choice(['Main St', 'Oak Ave', 'Pine Rd', 'Elm St', 'Maple Dr'])}",
                    delivery_city=location["city"],
                    delivery_country=location["country"],
                    delivery_phone=f"+1{random.randint(1000000000, 9999999999)}",
                    created_at=created_at,
                    confirmed_at=confirmed_at,
                    dispatched_at=dispatched_at,
                    delivered_at=delivered_at,
                    completed_at=completed_at,
                    cancelled_at=cancelled_at,
                )

                # Create order item
                OrderItem.objects.create(
                    order=order,
                    product_name=product["name"],
                    product_description=product["description"],
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=subtotal,
                )

                # Create status history
                statuses = ["pending"]
                if confirmed_at:
                    statuses.append("confirmed")
                if dispatched_at:
                    statuses.append("dispatched")
                if delivered_at:
                    statuses.append("delivered")
                if completed_at:
                    statuses.append("completed")
                if cancelled_at:
                    statuses.append("cancelled")

                for j in range(len(statuses) - 1):
                    OrderStatusHistory.objects.create(
                        order=order,
                        old_status=statuses[j],
                        new_status=statuses[j + 1],
                        changed_by=random.choice(users),
                        notes=f"Status changed from {statuses[j]} to {statuses[j + 1]}",
                    )

                # Create delivery tracking for dispatched/delivered orders
                if status in ["dispatched", "delivered", "activated", "completed"]:
                    courier = random.choice(couriers)
                    tracking_number = (
                        f"{courier[:3].upper()}{random.randint(100000000, 999999999)}"
                    )

                    tracking_status = "in_transit"
                    if status in ["delivered", "activated", "completed"]:
                        tracking_status = "delivered"

                    DeliveryTracking.objects.create(
                        order=order,
                        tracking_number=tracking_number,
                        courier_name=courier,
                        status=tracking_status,
                        current_location=location["city"],
                        estimated_delivery=(
                            dispatched_at + timedelta(days=random.randint(1, 5))
                            if dispatched_at
                            else None
                        ),
                        actual_delivery=delivered_at if delivered_at else None,
                    )

                # Create notifications
                notification_types = {
                    "confirmed": "ORDER_CONFIRMED",
                    "dispatched": "ORDER_DISPATCHED",
                    "delivered": "ORDER_DELIVERED",
                    "activated": "ORDER_ACTIVATED",
                    "cancelled": "ORDER_CANCELLED",
                }

                if status in notification_types:
                    notification_type = notification_types[status]
                    recipient = client.email or client.phone_number

                    if recipient:
                        OrderNotification.objects.create(
                            order=order,
                            notification_type=notification_type,
                            notification_method="email" if "@" in recipient else "sms",
                            recipient=recipient,
                            message=f"Your order {order_number} has been {status}",
                            delivered=True,
                            delivered_at=timezone.now(),
                        )

                if (i + 1) % 10 == 0:
                    self.stdout.write(f"Created {i + 1} orders...")

        self.stdout.write(
            self.style.SUCCESS(f"Successfully created {count} dummy orders!")
        )

        # Print summary
        total_orders = Order.objects.count()
        orders_by_status = (
            Order.objects.values("status")
            .annotate(count=models.Count("id"))
            .order_by("status")
        )

        self.stdout.write("\nOrder Summary:")
        self.stdout.write(f"Total Orders: {total_orders}")
        for status_count in orders_by_status:
            self.stdout.write(f'{status_count["status"]}: {status_count["count"]}')

        # Orders by source
        orders_by_source = (
            Order.objects.values("order_source")
            .annotate(count=models.Count("id"))
            .order_by("order_source")
        )

        self.stdout.write("\nOrders by Source:")
        for source_count in orders_by_source:
            self.stdout.write(
                f'{source_count["order_source"]}: {source_count["count"]}'
            )

        # Orders by type
        orders_by_type = (
            Order.objects.values("order_type")
            .annotate(count=models.Count("id"))
            .order_by("order_type")
        )

        self.stdout.write("\nOrders by Type:")
        for type_count in orders_by_type:
            self.stdout.write(f'{type_count["order_type"]}: {type_count["count"]}')
