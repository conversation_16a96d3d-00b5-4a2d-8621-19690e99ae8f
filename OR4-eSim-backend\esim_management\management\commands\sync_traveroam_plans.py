import logging

from django.core.cache import cache
from django.core.management.base import BaseCommand
from django.utils import timezone

from esim_management.models import ESIMPlan
from esim_management.services import ESIMWorkflowService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Professional TraveRoam eSIM plans synchronization"

    def add_arguments(self, parser):
        parser.add_argument(
            "--country",
            type=str,
            help="Filter plans by country code (e.g., IN, PK, SG)",
        )
        parser.add_argument(
            "--region",
            type=str,
            help="Filter plans by region (e.g., Asia, Europe)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be updated without making changes",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force refresh even if plans exist",
        )
        parser.add_argument(
            "--clear-cache",
            action="store_true",
            help="Clear cache before syncing",
        )

    def handle(self, *args, **options):
        country = options.get("country")
        region = options.get("region")
        dry_run = options.get("dry_run")
        force = options.get("force")
        clear_cache = options.get("clear_cache")

        self.stdout.write(
            self.style.SUCCESS("🚀 Starting Professional TraveRoam Plan Sync...")
        )

        if clear_cache:
            cache.clear()
            self.stdout.write(self.style.WARNING("🗑️  Cache cleared"))

        try:
            # Get plans from TraveRoam API
            self.stdout.write("📡 Fetching plans from TraveRoam API...")
            traveroam_plans = ESIMWorkflowService.get_available_plans(
                country=country, region=region
            )

            if not traveroam_plans:
                self.stdout.write(
                    self.style.WARNING("❌ No plans found from TraveRoam API")
                )
                return

            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ Found {len(traveroam_plans)} plans from TraveRoam"
                )
            )

            created_count = 0
            updated_count = 0
            skipped_count = 0
            error_count = 0

            for plan_data in traveroam_plans:
                try:
                    plan_id = plan_data.get("id")
                    if not plan_id:
                        self.stdout.write(
                            self.style.WARNING(
                                f'⚠️  Skipping plan without ID: {plan_data.get("name", "Unknown")}'
                            )
                        )
                        skipped_count += 1
                        continue

                    # Check if plan exists
                    existing_plan = ESIMPlan.objects.filter(
                        traveroam_plan_id=plan_id
                    ).first()

                    # Prepare plan data
                    plan_info = {
                        "name": plan_data.get("name", ""),
                        "description": plan_data.get("description", ""),
                        "country": plan_data.get("country", ""),
                        "country_code": plan_data.get("country_code", ""),
                        "region": plan_data.get("region", ""),
                        "data_volume": plan_data.get("data_volume", ""),
                        "data_amount_mb": plan_data.get("data_amount_mb", 0),
                        "duration_days": plan_data.get("duration_days", 30),
                        "duration": plan_data.get("duration", ""),
                        "base_price": plan_data.get("price", 0),
                        "reseller_price": plan_data.get(
                            "price", 0
                        ),  # Same as base for now
                        "public_price": plan_data.get(
                            "price", 0
                        ),  # Same as base for now
                        "speed": plan_data.get("speed", []),
                        "unlimited": plan_data.get("unlimited", False),
                        "autostart": plan_data.get("autostart", True),
                        "image_url": plan_data.get("image_url", ""),
                        "billing_type": plan_data.get("billing_type", "FixedCost"),
                        "is_active": True,
                        "last_synced": timezone.now(),
                    }

                    if existing_plan:
                        if dry_run:
                            self.stdout.write(
                                f"🔄 Would update plan: {existing_plan.name} ({plan_id})"
                            )
                        else:
                            # Update existing plan
                            for field, value in plan_info.items():
                                setattr(existing_plan, field, value)
                            existing_plan.updated_at = timezone.now()
                            existing_plan.save()

                            self.stdout.write(
                                self.style.SUCCESS(
                                    f"✅ Updated plan: {existing_plan.name}"
                                )
                            )
                        updated_count += 1
                    else:
                        if dry_run:
                            self.stdout.write(
                                f'🆕 Would create plan: {plan_info["name"]} ({plan_id})'
                            )
                        else:
                            # Create new plan
                            plan = ESIMPlan.objects.create(
                                traveroam_plan_id=plan_id, **plan_info
                            )

                            self.stdout.write(
                                self.style.SUCCESS(f"✅ Created plan: {plan.name}")
                            )
                        created_count += 1

                except Exception as e:
                    error_count += 1
                    logger.error(
                        f"Error processing plan {plan_data.get('id', 'unknown')}: {str(e)}"
                    )
                    self.stdout.write(
                        self.style.ERROR(f"❌ Error processing plan: {str(e)}")
                    )
                    continue

            # Summary
            self.stdout.write("\n" + "=" * 60)
            self.stdout.write("📊 SYNC SUMMARY:")
            self.stdout.write("=" * 60)
            self.stdout.write(f"📡 Total plans from TraveRoam: {len(traveroam_plans)}")
            self.stdout.write(f"🆕 Created: {created_count}")
            self.stdout.write(f"🔄 Updated: {updated_count}")
            self.stdout.write(f"⚠️  Skipped: {skipped_count}")
            self.stdout.write(f"❌ Errors: {error_count}")

            if country:
                self.stdout.write(f"🌍 Filtered by country: {country}")
            if region:
                self.stdout.write(f"🌐 Filtered by region: {region}")

            if dry_run:
                self.stdout.write(
                    self.style.WARNING("🔍 DRY RUN - No changes were made")
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS("🎉 Plan sync completed successfully!")
                )

            # Cache statistics
            cache_stats = cache.get("traveroam_sync_stats", {})
            cache_stats["last_sync"] = timezone.now().isoformat()
            cache_stats["total_plans"] = len(traveroam_plans)
            cache_stats["created"] = created_count
            cache_stats["updated"] = updated_count
            cache.set("traveroam_sync_stats", cache_stats, 3600)

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error syncing plans: {str(e)}"))
            logger.error(f"Error syncing TraveRoam plans: {str(e)}")
            raise
