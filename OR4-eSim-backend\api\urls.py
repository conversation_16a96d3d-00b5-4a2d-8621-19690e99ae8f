from django.urls import include, path
from rest_framework.routers import DefaultRouter

# Import views from each app
from accounts.views import (
    UserProfileViewSet,
    UserViewSet,
    edit_profile_view,
    login_view,
    logout_view,
    password_change_view,
    password_reset_confirm_view,
    password_reset_request_view,
    refresh_token_view,
    signup_view,
    user_profile_view,
    verify_token_view,
)
from clients.views import ClientViewSet, ResellerClientViewSet, SupportTicketViewSet
from esim_management.reseller_views import (
    ResellerDashboardViewSet,
    ResellerESIMViewSet,
    ResellerPlanViewSet,
)
from esim_management.views import (
    ESIMDeliveryViewSet,
    ESIMPlanViewSet,
    ESIMUsageViewSet,
    ESIMViewSet,
)
from esim_management.views import ResellerClientViewSet as ESIMResellerClientViewSet
from esim_management.views import TraveRoamWebhookViewSet
from esim_management.webhook_views import (
    traveroam_webhook,
    webhook_simulate,
    webhook_status,
    webhook_test,
)
from orders.views import (
    DeliveryTrackingViewSet,
    OrderItemViewSet,
    OrderNotificationViewSet,
    OrderStatusHistoryViewSet,
    OrderViewSet,
)
from payments.views import PaymentViewSet, StripeWebhookViewSet
from reports.views import (
    AnalyticsEventViewSet,
    PerformanceMetricViewSet,
    ReportScheduleViewSet,
    ReportViewSet,
)
from resellers.views import ResellerActivationRequestViewSet, ResellerViewSet

from . import views

# Create routers for each app
accounts_router = DefaultRouter()
accounts_router.register(r"users", UserViewSet)
accounts_router.register(r"user-profiles", UserProfileViewSet)

resellers_router = DefaultRouter()
resellers_router.register(r"resellers", ResellerViewSet)
resellers_router.register(
    r"reseller-activation-requests", ResellerActivationRequestViewSet
)

clients_router = DefaultRouter()
clients_router.register(r"", ClientViewSet)
clients_router.register(
    r"reseller-clients", ResellerClientViewSet, basename="reseller-client"
)
clients_router.register(r"support-tickets", SupportTicketViewSet)

esim_router = DefaultRouter()
esim_router.register(r"esim-plans", ESIMPlanViewSet)
esim_router.register(r"esims", ESIMViewSet)
esim_router.register(r"esim-usage", ESIMUsageViewSet)
esim_router.register(r"traveroam-webhooks", TraveRoamWebhookViewSet)
esim_router.register(r"esim-deliveries", ESIMDeliveryViewSet)

# Reseller workflow routers
reseller_workflow_router = DefaultRouter()
reseller_workflow_router.register(
    r"clients", ESIMResellerClientViewSet, basename="esim-reseller-client"
)
reseller_workflow_router.register(
    r"esims", ResellerESIMViewSet, basename="esim-reseller-esim"
)
reseller_workflow_router.register(
    r"plans", ResellerPlanViewSet, basename="esim-reseller-plan"
)
reseller_workflow_router.register(
    r"dashboard", ResellerDashboardViewSet, basename="esim-reseller-dashboard"
)

orders_router = DefaultRouter()
orders_router.register(r"orders", OrderViewSet, basename="order")
orders_router.register(r"order-items", OrderItemViewSet, basename="orderitem")
orders_router.register(
    r"order-status-history", OrderStatusHistoryViewSet, basename="orderstatushistory"
)
orders_router.register(
    r"delivery-tracking", DeliveryTrackingViewSet, basename="deliverytracking"
)
orders_router.register(
    r"order-notifications", OrderNotificationViewSet, basename="ordernotification"
)

payments_router = DefaultRouter()
payments_router.register(r"payments", PaymentViewSet)
payments_router.register(r"stripe-webhooks", StripeWebhookViewSet)

reports_router = DefaultRouter()
reports_router.register(r"reports", ReportViewSet)
reports_router.register(r"analytics-events", AnalyticsEventViewSet)
reports_router.register(r"performance-metrics", PerformanceMetricViewSet)
reports_router.register(r"report-schedules", ReportScheduleViewSet)

urlpatterns = [
    # JWT Authentication endpoints
    path("auth/signup/", signup_view, name="signup"),
    path("auth/login/", login_view, name="login"),
    path("auth/logout/", logout_view, name="logout"),
    path("auth/refresh/", refresh_token_view, name="refresh-token"),
    path("auth/verify/", verify_token_view, name="verify_token"),
    # Password management endpoints
    path(
        "auth/password-reset-request/",
        password_reset_request_view,
        name="password-reset-request",
    ),
    path(
        "auth/password-reset-confirm/",
        password_reset_confirm_view,
        name="password-reset-confirm",
    ),
    path("auth/password-change/", password_change_view, name="password-change"),
    path("auth/profile/", user_profile_view, name="user-profile"),
    path("auth/edit-profile/<str:email>/", edit_profile_view, name="edit-profile"),
    # Image upload endpoints
    path(
        "upload/profile-image/", views.upload_profile_image, name="upload-profile-image"
    ),
    path(
        "upload/document-image/",
        views.upload_document_image,
        name="upload-document-image",
    ),
    path(
        "upload/delete-profile-image/",
        views.delete_profile_image,
        name="delete-profile-image",
    ),
    # Dashboard endpoints
    path("dashboard/", views.dashboard_view, name="dashboard"),
    path("reports/dashboard/", views.dashboard_view, name="reports-dashboard"),
    path(
        "reports/dashboard/test/",
        views.test_admin_dashboard,
        name="reports-dashboard-test",
    ),
    # App-specific API routers
    path("accounts/", include(accounts_router.urls)),
    path("resellers/", include(resellers_router.urls)),
    path("clients/", include(clients_router.urls)),
    path("esim/", include(esim_router.urls)),
    path("esim/reseller/", include(reseller_workflow_router.urls)),
    path("esim/webhooks/traveroam/", traveroam_webhook, name="traveroam-webhook"),
    path("esim/webhooks/test/", webhook_test, name="webhook-test"),
    path("esim/webhooks/status/", webhook_status, name="webhook-status"),
    path("esim/webhooks/simulate/", webhook_simulate, name="webhook-simulate"),
    # TraveRoam API endpoints
    path("traveroam/", include("esim_management.traveroam_urls")),
    # Stripe Payment endpoints
    path("stripe/", include("payments.stripe_urls")),
    path("orders/", include(orders_router.urls)),
    path("payments/", include(payments_router.urls)),
    path("reports/", include(reports_router.urls)),
]
