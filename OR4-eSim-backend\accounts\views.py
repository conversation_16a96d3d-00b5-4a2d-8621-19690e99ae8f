import logging

from django.contrib.auth import authenticate, get_user_model
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken

from api.firebase_storage import firebase_storage

from .models import User, UserProfile
from .serializers import (
    EditProfileSerializer,
    LoginSerializer,
    PasswordChangeSerializer,
    SignupSerializer,
    UserProfileSerializer,
    UserSerializer,
    UserUpdateSerializer,
)

User = get_user_model()


@api_view(["POST"])
@permission_classes([AllowAny])
def signup_view(request):
    """User signup with optional profile image"""
    try:
        # Handle both JSON and multipart form data
        if request.content_type.startswith("multipart/form-data"):
            # Handle image upload with signup
            serializer = SignupSerializer(data=request.data)
            if serializer.is_valid():
                user = serializer.save()

                # Handle reseller activation request
                activation_request = None
                if user.role == "reseller":
                    from resellers.models import ResellerActivationRequest

                    activation_request = ResellerActivationRequest.objects.create(
                        user=user,
                        max_clients=request.data.get("max_clients", 100),
                        max_sims=request.data.get("max_sims", 1000),
                        credit_limit=request.data.get("credit_limit", 1000.00),
                    )

                # Handle profile image upload if provided
                profile_image_url = None
                if "profile_image" in request.FILES:
                    image_file = request.FILES["profile_image"]

                    # Validate file type
                    allowed_types = [
                        "image/jpeg",
                        "image/jpg",
                        "image/png",
                        "image/gif",
                    ]
                    if image_file.content_type not in allowed_types:
                        return Response(
                            {
                                "success": False,
                                "message": "Invalid file type. Only JPEG, PNG, and GIF are allowed",
                                "status_code": 400,
                                "errors": {"profile_image": ["Invalid file type"]},
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                    # Validate file size (max 5MB)
                    if image_file.size > 5 * 1024 * 1024:
                        return Response(
                            {
                                "success": False,
                                "message": "File size too large. Maximum size is 5MB",
                                "status_code": 400,
                                "errors": {"profile_image": ["File too large"]},
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                    # Upload to Firebase Storage
                    try:
                        user_full_name = f"{user.first_name} {user.last_name}".strip()
                        public_url, _ = firebase_storage.upload_profile_image(
                            image_file, user.id, user_full_name
                        )

                        # Create user profile with image URL
                        user_profile = UserProfile.objects.create(
                            user=user, profile_image_url=public_url
                        )
                        profile_image_url = public_url
                    except Exception as firebase_error:
                        # If Firebase upload fails, still create user but without image
                        logger = logging.getLogger(__name__)
                        logger.error(f"Firebase upload failed: {str(firebase_error)}")

                        # Create user profile without image
                        UserProfile.objects.create(user=user)

                        response_data = {
                            "user_id": user.id,
                            "profile_image_url": None,
                            "warning": "Image upload failed. Please check Firebase configuration.",
                        }

                        if activation_request:
                            response_data["activation_request_id"] = (
                                activation_request.id
                            )
                            response_data["message"] = (
                                "User registered successfully, but image upload failed. Reseller activation request created and pending admin approval."
                            )
                        else:
                            response_data["message"] = (
                                "User registered successfully, but image upload failed. Please try uploading image later."
                            )

                        return Response(
                            {
                                "success": True,
                                "message": response_data["message"],
                                "status_code": 200,
                                "data": response_data,
                            },
                            status=status.HTTP_200_OK,
                        )

                response_data = {
                    "user_id": user.id,
                    "profile_image_url": profile_image_url,
                }

                if activation_request:
                    response_data["activation_request_id"] = activation_request.id
                    response_data["message"] = (
                        "User registered successfully. Reseller activation request created and pending admin approval."
                    )
                else:
                    response_data["message"] = "User registered successfully"

                return Response(
                    {
                        "success": True,
                        "message": response_data["message"],
                        "status_code": 200,
                        "data": response_data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "success": False,
                        "message": "Signup failed",
                        "status_code": 400,
                        "errors": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            # Handle JSON-only signup (no image)
            serializer = SignupSerializer(data=request.data)
            if serializer.is_valid():
                user = serializer.save()

                # Handle reseller activation request
                activation_request = None
                if user.role == "reseller":
                    from resellers.models import ResellerActivationRequest

                    activation_request = ResellerActivationRequest.objects.create(
                        user=user,
                        max_clients=request.data.get("max_clients", 100),
                        max_sims=request.data.get("max_sims", 1000),
                        credit_limit=request.data.get("credit_limit", 1000.00),
                    )

                response_data = {"user_id": user.id}

                if activation_request:
                    response_data["activation_request_id"] = activation_request.id
                    response_data["message"] = (
                        "User registered successfully. Reseller activation request created and pending admin approval."
                    )
                else:
                    response_data["message"] = "User registered successfully"

                return Response(
                    {
                        "success": True,
                        "message": response_data["message"],
                        "status_code": 200,
                        "data": response_data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "success": False,
                        "message": "Signup failed",
                        "status_code": 400,
                        "errors": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Signup error: {str(e)}", exc_info=True)
        return Response(
            {
                "success": False,
                "message": "Signup failed",
                "status_code": 500,
                "errors": {"detail": "Internal server error"},
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def login_view(request):
    """Simple user login"""
    serializer = LoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data["user"]

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        refresh_token = str(refresh)

        # Get complete user profile information
        try:
            user_profile = UserProfile.objects.get(user=user)
            profile_data = {
                "profile_image_url": user_profile.profile_image_url,
                "date_of_birth": (
                    user_profile.date_of_birth.isoformat()
                    if user_profile.date_of_birth
                    else None
                ),
                "gender": user_profile.gender,
                "address": user_profile.address,
                "city": user_profile.city,
                "state": user_profile.state,
                "country": user_profile.country,
                "postal_code": user_profile.postal_code,
                "emergency_contact_name": user_profile.emergency_contact_name,
                "emergency_contact_phone": user_profile.emergency_contact_phone,
                "preferences": user_profile.preferences,
            }
        except UserProfile.DoesNotExist:
            profile_data = {
                "profile_image_url": None,
                "date_of_birth": None,
                "gender": None,
                "address": None,
                "city": None,
                "state": None,
                "country": None,
                "postal_code": None,
                "emergency_contact_name": None,
                "emergency_contact_phone": None,
                "preferences": {},
            }

        return Response(
            {
                "success": True,
                "message": "Login successful",
                "status_code": 200,
                "data": {
                    "user": {
                        "id": user.id,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "role": user.role,
                        "phone_number": user.phone_number,
                        "is_active": user.is_active,
                        "created_at": user.created_at.isoformat(),
                        "last_login": (
                            user.last_login.isoformat() if user.last_login else None
                        ),
                        "profile": profile_data,
                    },
                    "tokens": {"access": access_token, "refresh": refresh_token},
                },
            },
            status=status.HTTP_200_OK,
        )
    else:
        # Check if user exists
        email = request.data.get("email")
        if email:
            try:
                user = User.objects.get(email=email)
                if not user.is_active:
                    return Response(
                        {
                            "success": False,
                            "message": "Account is disabled",
                            "status_code": 403,
                            "error_code": "ACCOUNT_DISABLED",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )
                else:
                    return Response(
                        {
                            "success": False,
                            "message": "Invalid password",
                            "status_code": 401,
                            "error_code": "INVALID_PASSWORD",
                        },
                        status=status.HTTP_401_UNAUTHORIZED,
                    )
            except User.DoesNotExist:
                return Response(
                    {
                        "success": False,
                        "message": "User not found",
                        "status_code": 404,
                        "error_code": "USER_NOT_FOUND",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )
        else:
            return Response(
                {
                    "success": False,
                    "message": "Email is required",
                    "status_code": 400,
                    "error_code": "EMAIL_REQUIRED",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["POST"])
@permission_classes([AllowAny])
def logout_view(request):
    """Proper logout with token blacklisting and session cleanup"""
    # Get refresh token from request body
    refresh_token = request.data.get("refresh_token")

    # Get access token from Authorization header
    auth_header = request.headers.get("Authorization")
    access_token = None
    if auth_header and auth_header.startswith("Bearer "):
        access_token = auth_header.split(" ")[1]

    # Blacklist refresh token if provided
    if refresh_token:
        try:
            token = RefreshToken(refresh_token)
            token.blacklist()
            refresh_blacklisted = True
        except (TokenError, InvalidToken):
            refresh_blacklisted = False
    else:
        refresh_blacklisted = False

    # Blacklist access token if provided
    if access_token:
        try:
            token = AccessToken(access_token)
            # Note: Access tokens are typically not blacklisted as they expire quickly
            # But we can track them if needed
            access_blacklisted = True
        except (TokenError, InvalidToken):
            access_blacklisted = False
    else:
        access_blacklisted = False

    # Get user info if authenticated
    user_info = None
    if request.user.is_authenticated:
        user_info = {
            "id": request.user.id,
            "email": request.user.email,
            "role": request.user.role,
        }

    # Prepare response based on what was blacklisted
    if refresh_blacklisted or access_blacklisted:
        message = "Logout successful"
        if refresh_blacklisted and access_blacklisted:
            message = "Logout successful - All tokens blacklisted"
        elif refresh_blacklisted:
            message = "Logout successful - Refresh token blacklisted"
        elif access_blacklisted:
            message = "Logout successful - Access token blacklisted"

        return Response(
            {
                "success": True,
                "message": message,
                "status_code": 200,
                "data": {
                    "user_logged_out": user_info,
                    "refresh_token_blacklisted": refresh_blacklisted,
                    "access_token_blacklisted": access_blacklisted,
                    "logout_time": timezone.now().isoformat(),
                },
            }
        )
    else:
        return Response(
            {
                "success": True,
                "message": "Logout successful - No tokens provided to blacklist",
                "status_code": 200,
                "data": {
                    "user_logged_out": user_info,
                    "refresh_token_blacklisted": False,
                    "access_token_blacklisted": False,
                    "logout_time": timezone.now().isoformat(),
                },
            }
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def refresh_token_view(request):
    """Simple token refresh"""
    refresh_token = request.data.get("refresh_token")

    if not refresh_token:
        return Response(
            {
                "success": False,
                "message": "Refresh token required",
                "status_code": 400,
                "error_code": "REFRESH_TOKEN_REQUIRED",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        token = RefreshToken(refresh_token)
        access_token = str(token.access_token)

        return Response(
            {
                "success": True,
                "message": "Token refreshed",
                "status_code": 200,
                "data": {"access": access_token, "refresh": str(token)},
            }
        )
    except (TokenError, InvalidToken):
        return Response(
            {
                "success": False,
                "message": "Invalid refresh token",
                "status_code": 401,
                "error_code": "INVALID_REFRESH_TOKEN",
            },
            status=status.HTTP_401_UNAUTHORIZED,
        )


@api_view(["GET"])
@permission_classes([AllowAny])
def verify_token_view(request):
    """Simple token verification"""
    # Get token from Authorization header
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return Response(
            {
                "success": False,
                "message": "Authorization header required",
                "status_code": 401,
                "error_code": "AUTHORIZATION_HEADER_REQUIRED",
            },
            status=status.HTTP_401_UNAUTHORIZED,
        )

    token = auth_header.split(" ")[1]

    try:
        # Verify token
        access_token = AccessToken(token)
        user_id = access_token.payload.get("user_id")
        user = User.objects.get(id=user_id)

        return Response(
            {
                "success": True,
                "message": "Token verified",
                "status_code": 200,
                "data": {
                    "user": {
                        "id": user.id,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "role": user.role,
                    }
                },
            }
        )
    except (TokenError, InvalidToken, User.DoesNotExist):
        return Response(
            {
                "success": False,
                "message": "Invalid token",
                "status_code": 401,
                "error_code": "INVALID_TOKEN",
            },
            status=status.HTTP_401_UNAUTHORIZED,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def password_reset_request_view(request):
    """Step 1: Check if user exists in database"""
    email = request.data.get("email")

    if not email:
        return Response(
            {
                "success": False,
                "message": "Email is required",
                "status_code": 400,
                "error_code": "EMAIL_REQUIRED",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        user = User.objects.get(email=email)
        if not user.is_active:
            return Response(
                {
                    "success": False,
                    "message": "Account is disabled",
                    "status_code": 403,
                    "error_code": "ACCOUNT_DISABLED",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # User exists and is active
        return Response(
            {
                "success": True,
                "message": "User found. You can proceed with password reset.",
                "status_code": 200,
                "data": {"email": user.email, "user_id": user.id},
            },
            status=status.HTTP_200_OK,
        )

    except User.DoesNotExist:
        return Response(
            {
                "success": False,
                "message": "User not found with this email",
                "status_code": 404,
                "error_code": "USER_NOT_FOUND",
            },
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def password_reset_confirm_view(request):
    """Step 2: Update password if user exists and passwords match"""
    email = request.data.get("email")
    new_password = request.data.get("new_password")
    confirm_password = request.data.get("confirm_password")

    # Check if all required fields are provided
    if not email:
        return Response(
            {
                "success": False,
                "message": "Email is required",
                "status_code": 400,
                "error_code": "EMAIL_REQUIRED",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    if not new_password:
        return Response(
            {
                "success": False,
                "message": "New password is required",
                "status_code": 400,
                "error_code": "NEW_PASSWORD_REQUIRED",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    if not confirm_password:
        return Response(
            {
                "success": False,
                "message": "Confirm password is required",
                "status_code": 400,
                "error_code": "CONFIRM_PASSWORD_REQUIRED",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    # Check if passwords match
    if new_password != confirm_password:
        return Response(
            {
                "success": False,
                "message": "Passwords do not match",
                "status_code": 400,
                "error_code": "PASSWORDS_DONT_MATCH",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    # Check password length
    if len(new_password) < 8:
        return Response(
            {
                "success": False,
                "message": "Password must be at least 8 characters long",
                "status_code": 400,
                "error_code": "PASSWORD_TOO_SHORT",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    # Check if user exists
    try:
        user = User.objects.get(email=email)
        if not user.is_active:
            return Response(
                {
                    "success": False,
                    "message": "Account is disabled",
                    "status_code": 403,
                    "error_code": "ACCOUNT_DISABLED",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Update user password
        user.set_password(new_password)
        user.save()

        return Response(
            {
                "success": True,
                "message": "Password updated successfully",
                "status_code": 200,
                "data": {"email": user.email, "user_id": user.id},
            },
            status=status.HTTP_200_OK,
        )

    except User.DoesNotExist:
        return Response(
            {
                "success": False,
                "message": "User not found with this email",
                "status_code": 404,
                "error_code": "USER_NOT_FOUND",
            },
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["POST"])
def password_change_view(request):
    """Simple password change"""
    if not request.user.is_authenticated:
        return Response(
            {
                "success": False,
                "message": "Authentication required",
                "status_code": 401,
                "error_code": "AUTHENTICATION_REQUIRED",
            },
            status=status.HTTP_401_UNAUTHORIZED,
        )

    serializer = PasswordChangeSerializer(
        data=request.data, context={"request": request}
    )
    if serializer.is_valid():
        new_password = serializer.validated_data["new_password"]
        request.user.set_password(new_password)
        request.user.save()

        return Response(
            {
                "success": True,
                "message": "Password changed successfully",
                "status_code": 200,
            }
        )
    else:
        return Response(
            {
                "success": False,
                "message": "Invalid data",
                "status_code": 400,
                "errors": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["GET"])
def user_profile_view(request):
    """Simple user profile"""
    if not request.user.is_authenticated:
        return Response(
            {
                "success": False,
                "message": "Authentication required",
                "status_code": 401,
                "error_code": "AUTHENTICATION_REQUIRED",
            },
            status=status.HTTP_401_UNAUTHORIZED,
        )

    # Get complete user profile information
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        profile_data = {
            "profile_image_url": user_profile.profile_image_url,
            "date_of_birth": (
                user_profile.date_of_birth.isoformat()
                if user_profile.date_of_birth
                else None
            ),
            "gender": user_profile.gender,
            "address": user_profile.address,
            "city": user_profile.city,
            "state": user_profile.state,
            "country": user_profile.country,
            "postal_code": user_profile.postal_code,
            "emergency_contact_name": user_profile.emergency_contact_name,
            "emergency_contact_phone": user_profile.emergency_contact_phone,
            "preferences": user_profile.preferences,
        }
    except UserProfile.DoesNotExist:
        profile_data = {
            "profile_image_url": None,
            "date_of_birth": None,
            "gender": None,
            "address": None,
            "city": None,
            "state": None,
            "country": None,
            "postal_code": None,
            "emergency_contact_name": None,
            "emergency_contact_phone": None,
            "preferences": {},
        }

    return Response(
        {
            "success": True,
            "message": "Profile retrieved",
            "status_code": 200,
            "data": {
                "user": {
                    "id": request.user.id,
                    "email": request.user.email,
                    "first_name": request.user.first_name,
                    "last_name": request.user.last_name,
                    "role": request.user.role,
                    "phone_number": request.user.phone_number,
                    "is_active": request.user.is_active,
                    "created_at": request.user.created_at.isoformat(),
                    "last_login": (
                        request.user.last_login.isoformat()
                        if request.user.last_login
                        else None
                    ),
                    "profile": profile_data,
                }
            },
        }
    )


@api_view(["PUT", "PATCH"])
def edit_profile_view(request, email):
    """Edit user profile with partial updates based on email"""
    if not request.user.is_authenticated:
        return Response(
            {
                "success": False,
                "message": "Authentication required",
                "status_code": 401,
                "error_code": "AUTHENTICATION_REQUIRED",
            },
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        # Fetch user by email
        try:
            user_to_update = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "message": "User not found",
                    "status_code": 404,
                    "error_code": "USER_NOT_FOUND",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

        # Check permissions - only allow users to edit their own profile or admin to edit any profile
        if not (request.user.email == email or request.user.role == "admin"):
            return Response(
                {
                    "success": False,
                    "message": "Permission denied. You can only edit your own profile.",
                    "status_code": 403,
                    "error_code": "PERMISSION_DENIED",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Handle both JSON and multipart form data
        if request.content_type.startswith("multipart/form-data"):
            # Handle image upload with profile update
            serializer = EditProfileSerializer(
                instance=user_to_update,
                data=request.data,
                context={"request": request},
                partial=True,
            )
        else:
            # Handle JSON-only profile update (no image)
            serializer = EditProfileSerializer(
                instance=user_to_update,
                data=request.data,
                context={"request": request},
                partial=True,
            )

        if serializer.is_valid():
            # Update user and profile
            updated_user = serializer.save()

            # Get complete updated user profile information
            try:
                user_profile = UserProfile.objects.get(user=updated_user)
                profile_data = {
                    "profile_image_url": user_profile.profile_image_url,
                    "date_of_birth": (
                        user_profile.date_of_birth.isoformat()
                        if user_profile.date_of_birth
                        else None
                    ),
                    "gender": user_profile.gender,
                    "address": user_profile.address,
                    "city": user_profile.city,
                    "state": user_profile.state,
                    "country": user_profile.country,
                    "postal_code": user_profile.postal_code,
                    "emergency_contact_name": user_profile.emergency_contact_name,
                    "emergency_contact_phone": user_profile.emergency_contact_phone,
                    "preferences": user_profile.preferences,
                }
            except UserProfile.DoesNotExist:
                profile_data = {
                    "profile_image_url": None,
                    "date_of_birth": None,
                    "gender": None,
                    "address": None,
                    "city": None,
                    "state": None,
                    "country": None,
                    "postal_code": None,
                    "emergency_contact_name": None,
                    "emergency_contact_phone": None,
                    "preferences": {},
                }

            return Response(
                {
                    "success": True,
                    "message": "Profile updated successfully",
                    "status_code": 200,
                    "data": {
                        "user": {
                            "id": updated_user.id,
                            "email": updated_user.email,
                            "first_name": updated_user.first_name,
                            "last_name": updated_user.last_name,
                            "role": updated_user.role,
                            "phone_number": updated_user.phone_number,
                            "is_active": updated_user.is_active,
                            "created_at": updated_user.created_at.isoformat(),
                            "last_login": (
                                updated_user.last_login.isoformat()
                                if updated_user.last_login
                                else None
                            ),
                            "profile": profile_data,
                        }
                    },
                }
            )
        else:
            return Response(
                {
                    "success": False,
                    "message": "Profile update failed",
                    "status_code": 400,
                    "errors": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Profile update error: {str(e)}", exc_info=True)
        return Response(
            {
                "success": False,
                "message": "Profile update failed",
                "status_code": 500,
                "errors": {"detail": "Internal server error"},
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


class UserViewSet(viewsets.ModelViewSet):
    """Simple user management"""

    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
                return User.objects.all()
            elif (
                hasattr(self.request.user, "is_reseller")
                and self.request.user.is_reseller
            ):
                return User.objects.filter(role="client")
        return User.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return SignupSerializer
        elif self.action in ["update", "partial_update"]:
            return UserUpdateSerializer
        return UserSerializer


class UserProfileViewSet(viewsets.ModelViewSet):
    """Simple user profile management"""

    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
                return UserProfile.objects.all()
            elif (
                hasattr(self.request.user, "is_reseller")
                and self.request.user.is_reseller
            ):
                return UserProfile.objects.filter(user__role="client")
            else:
                return UserProfile.objects.filter(user=self.request.user)
        return UserProfile.objects.none()
