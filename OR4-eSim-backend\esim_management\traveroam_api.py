# -*- coding: utf-8 -*-
"""
Professional TraveRoam API Client for eSIM Management
Complete integration with all required endpoints for reseller workflow
"""

import logging
from typing import Any, Dict, List, Optional

import requests
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


class TraveRoamAPIError(Exception):
    """Custom exception for TraveRoam API errors"""

    pass


class TraveRoamAPIClient:
    """Professional TraveRoam API client for complete eSIM management"""

    def __init__(self):
        """Initialize API client with environment variables"""
        # Load from environment variables
        from django.conf import settings

        self.base_url = getattr(settings, "TRAVEROAM_API_BASE_URL")
        self.api_key = getattr(settings, "TRAVEROAM_API_KEY")
        self.client_secret = getattr(settings, "TRAVEROAM_SECRET_KEY")

        # Load endpoints from settings
        self.catalogue_endpoint = getattr(settings, "TRAVEROAM_CATALOGUE_ENDPOINT")
        self.bundle_endpoint = getattr(settings, "TRAVEROAM_BUNDLE_ENDPOINT")
        self.networks_endpoint = getattr(settings, "TRAVEROAM_NETWORKS_ENDPOINT")
        self.organization_endpoint = getattr(
            settings, "TRAVEROAM_ORGANIZATION_ENDPOINT"
        )
        self.process_orders_endpoint = getattr(
            settings, "TRAVEROAM_PROCESS_ORDERS_ENDPOINT"
        )
        self.orders_endpoint = getattr(settings, "TRAVEROAM_ORDERS_ENDPOINT")
        self.esim_update_endpoint = getattr(settings, "TRAVEROAM_ESIM_UPDATE_ENDPOINT")
        self.esim_details_endpoint = getattr(
            settings, "TRAVEROAM_ESIM_DETAILS_ENDPOINT"
        )
        self.esim_refresh_endpoint = getattr(
            settings, "TRAVEROAM_ESIM_REFRESH_ENDPOINT"
        )
        self.send_sms_endpoint = getattr(settings, "TRAVEROAM_SEND_SMS_ENDPOINT")
        self.esim_bundles_endpoint = getattr(
            settings, "TRAVEROAM_ESIM_BUNDLES_ENDPOINT"
        )
        self.bundle_status_endpoint = getattr(
            settings, "TRAVEROAM_BUNDLE_STATUS_ENDPOINT"
        )
        self.esim_location_endpoint = getattr(
            settings, "TRAVEROAM_ESIM_LOCATION_ENDPOINT"
        )
        self.revoke_bundle_endpoint = getattr(
            settings, "TRAVEROAM_REVOKE_BUNDLE_ENDPOINT"
        )
        self.all_esims_endpoint = getattr(settings, "TRAVEROAM_ALL_ESIMS_ENDPOINT")
        self.esim_assignments_endpoint = getattr(
            settings, "TRAVEROAM_ESIM_ASSIGNMENTS_ENDPOINT"
        )

        # Initialize session
        self.session = requests.Session()
        self.session.headers.update(
            {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "x-api-key": self.api_key,
                "clientSecret": self.client_secret,
            }
        )

        logger.info(f"TraveRoam API Client initialized for {self.base_url}")

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        return {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "x-api-key": self.api_key,
            "clientSecret": self.client_secret,
        }

    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        cache_key: Optional[str] = None,
        cache_timeout: int = 300,
    ) -> Dict:
        """Make API request to TraveRoam with caching support"""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()

        # Check cache for GET requests
        if method.upper() == "GET" and cache_key:
            cached_response = cache.get(cache_key)
            if cached_response:
                logger.info(f"Returning cached response for {cache_key}")
                return cached_response

        try:
            if method.upper() == "GET":
                response = self.session.get(url, headers=headers, timeout=30)
            elif method.upper() == "POST":
                response = self.session.post(
                    url, headers=headers, json=data, timeout=30
                )
            elif method.upper() == "PATCH":
                response = self.session.patch(
                    url, headers=headers, json=data, timeout=30
                )
            else:
                raise TraveRoamAPIError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            result = response.json()

            # Cache GET responses
            if method.upper() == "GET" and cache_key:
                cache.set(cache_key, result, cache_timeout)
                logger.info(f"Cached response for {cache_key}")

            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"TraveRoam API request failed: {str(e)}")
            raise TraveRoamAPIError(f"API request failed: {str(e)}")

    # Core API Methods for Reseller Workflow

    def get_catalogue(
        self, countries: Optional[str] = None, region: Optional[str] = None
    ) -> Dict:
        """Get available eSIM plans from catalogue - Step 2 of workflow"""
        data = {}
        if countries:
            data["countries"] = countries
        if region:
            data["region"] = region

        cache_key = f"traveroam_catalogue_{countries}_{region}"
        return self._make_request("POST", self.catalogue_endpoint, data, cache_key, 600)

    def get_networks(
        self,
        countries: Optional[str] = None,
        isos: Optional[str] = None,
        returnall: bool = False,
    ) -> List[Dict]:
        """Get available networks"""
        data = {"returnall": str(returnall).lower()}
        if countries:
            data["countries"] = countries
        if isos:
            data["isos"] = isos

        cache_key = f"traveroam_networks_{countries}_{isos}_{returnall}"
        return self._make_request("POST", self.networks_endpoint, data, cache_key, 1800)

    def get_organization_details(self) -> Dict:
        """Get organization details"""
        cache_key = "traveroam_organization"
        return self._make_request(
            "GET", self.organization_endpoint, cache_key=cache_key, cache_timeout=3600
        )

    def process_order(
        self, bundle_name: str, order_type: str = "REGION", iccid: Optional[str] = None
    ) -> Dict:
        """Process order to provision eSIM - Step 2 of workflow"""
        data = {"item": bundle_name, "type": order_type}
        if iccid:
            data["iccid"] = iccid

        logger.info(f"Processing order for bundle: {bundle_name}")
        return self._make_request("POST", self.process_orders_endpoint, data)

    def get_esim_assignments(self, reference: str) -> Dict:
        """Get eSIM assignments/QR codes by order reference - Step 3 of workflow"""
        data = {"reference": reference}
        logger.info(f"Getting eSIM assignments for reference: {reference}")
        return self._make_request("POST", self.esim_assignments_endpoint, data)

    def check_bundle_status(self, iccid: str, bundle_name: str) -> Dict:
        """Check if a bundle is activated/active for an ICCID"""
        data = {"iccid": iccid, "bundlename": bundle_name}
        cache_key = f"traveroam_bundle_status_{iccid}_{bundle_name}"
        return self._make_request(
            "POST", self.bundle_status_endpoint, data, cache_key, 300
        )

    def check_esim_status(self, iccid: str) -> Dict:
        """Check eSIM status and active bundles"""
        data = {"iccid": iccid}
        cache_key = f"traveroam_esim_status_{iccid}"
        return self._make_request(
            "POST", self.esim_details_endpoint, data, cache_key, 300
        )

    def get_all_esims(self, phone_number: str = None) -> Dict:
        """Get all eSIMs for a phone number or all eSIMs"""
        data = {}
        if phone_number:
            data["phone"] = phone_number
        cache_key = f"traveroam_all_esims_{phone_number or 'all'}"
        return self._make_request("POST", self.all_esims_endpoint, data, cache_key, 600)

    def validate_bundle_assignment(self, phone_number: str, bundle_name: str) -> Dict:
        """Validate if a phone number already has an active bundle"""
        try:
            # Get all eSIMs for the phone number
            all_esims = self.get_all_esims(phone_number)

            if not all_esims or not isinstance(all_esims, dict):
                return {"valid": True, "message": "No existing eSIMs found"}

            # Check if any eSIM has the same bundle active
            esims = all_esims.get("esims", [])
            for esim in esims:
                if isinstance(esim, dict):
                    iccid = esim.get("iccid")
                    if iccid:
                        # Check bundle status for this ICCID
                        bundle_status = self.check_bundle_status(iccid, bundle_name)
                        if bundle_status and bundle_status.get("status") == "active":
                            return {
                                "valid": False,
                                "message": f"Phone number {phone_number} already has an active {bundle_name} bundle",
                                "iccid": iccid,
                                "bundle_status": bundle_status,
                            }

            return {
                "valid": True,
                "message": "No active bundle found for this phone number",
            }

        except TraveRoamAPIError as e:
            logger.error(f"Error validating bundle assignment: {str(e)}")
            return {
                "valid": True,
                "message": "Validation failed, proceeding with assignment",
            }

    def check_duplicate_assignment(self, phone_number: str, bundle_name: str) -> Dict:
        """Check for duplicate bundle assignments for a phone number"""
        try:
            # Get all eSIMs for the phone number
            all_esims = self.get_all_esims(phone_number)

            if not all_esims:
                return {"duplicate": False, "message": "No existing eSIMs found"}

            # Check for duplicate bundle assignments
            esims = all_esims.get("esims", [])
            duplicate_count = 0

            for esim in esims:
                if isinstance(esim, dict):
                    esim_bundle = esim.get("bundle_name") or esim.get("name")
                    if esim_bundle == bundle_name:
                        duplicate_count += 1

            if duplicate_count > 0:
                return {
                    "duplicate": True,
                    "message": f"Found {duplicate_count} existing assignment(s) for {bundle_name}",
                    "count": duplicate_count,
                }

            return {"duplicate": False, "message": "No duplicate assignments found"}

        except TraveRoamAPIError as e:
            logger.error(f"Error checking duplicate assignment: {str(e)}")
            return {
                "duplicate": False,
                "message": "Check failed, proceeding with assignment",
            }

    def get_orders(self) -> List[Dict]:
        """Get all orders"""
        cache_key = "traveroam_orders"
        return self._make_request(
            "POST", self.orders_endpoint, cache_key=cache_key, cache_timeout=300
        )

    def get_all_esims(self) -> List[Dict]:
        """Get all eSIMs"""
        cache_key = "traveroam_all_esims"
        return self._make_request(
            "GET", self.all_esims_endpoint, cache_key=cache_key, cache_timeout=300
        )

    # eSIM Management Methods

    def update_esim(self, iccid: str, customerref: str) -> Dict:
        """Update eSIM details"""
        data = {"iccid": iccid, "customerref": customerref}
        return self._make_request("POST", self.esim_update_endpoint, data)

    def get_esim_details(self, iccid: str) -> Dict:
        """Get eSIM details"""
        data = {"iccid": iccid}
        cache_key = f"traveroam_esim_details_{iccid}"
        return self._make_request(
            "POST", self.esim_details_endpoint, data, cache_key, 300
        )

    def refresh_esim(self, iccid: str) -> Dict:
        """Refresh eSIM (disconnect from network)"""
        data = {"iccid": iccid}
        return self._make_request("POST", self.esim_refresh_endpoint, data)

    def get_esim_bundles(self, iccid: str) -> List[Dict]:
        """Get eSIM bundles and usage data"""
        data = {"iccid": iccid}
        cache_key = f"traveroam_esim_bundles_{iccid}"
        return self._make_request(
            "POST", self.esim_bundles_endpoint, data, cache_key, 300
        )

    def get_bundle_status(self, iccid: str, bundle_name: str) -> Dict:
        """Get specific bundle status"""
        data = {"iccid": iccid, "name": bundle_name}
        cache_key = f"traveroam_bundle_status_{iccid}_{bundle_name}"
        return self._make_request(
            "POST", self.bundle_status_endpoint, data, cache_key, 300
        )

    def get_esim_location(self, iccid: str) -> Dict:
        """Get eSIM location"""
        data = {"iccid": iccid}
        cache_key = f"traveroam_esim_location_{iccid}"
        return self._make_request(
            "POST", self.esim_location_endpoint, data, cache_key, 300
        )

    def revoke_bundle(self, iccid: str, bundle_name: str, assignment_id: str) -> Dict:
        """Revoke specific bundle assignment"""
        data = {"iccid": iccid, "name": bundle_name, "assignmentid": assignment_id}
        return self._make_request("POST", self.revoke_bundle_endpoint, data)

    def get_bundle_details(self, bundle_name: str) -> Dict:
        """Get bundle details"""
        data = {"bundlename": bundle_name}
        cache_key = f"traveroam_bundle_details_{bundle_name}"
        return self._make_request("POST", self.bundle_endpoint, data, cache_key, 1800)

    def send_sms(self, phone_number: str, message: str) -> Dict:
        """Send SMS notification"""
        data = {"phone": phone_number, "message": message}
        return self._make_request("POST", self.send_sms_endpoint, data)

    # Utility Methods

    def get_available_plans(
        self, country: Optional[str] = None, region: Optional[str] = None
    ) -> List[Dict]:
        """Get available plans with proper formatting for frontend"""
        try:
            catalogue_response = self.get_catalogue(countries=country, region=region)

            # Handle different response formats
            if isinstance(catalogue_response, dict) and "bundles" in catalogue_response:
                bundles = catalogue_response["bundles"]
            elif isinstance(catalogue_response, list):
                bundles = catalogue_response
            else:
                logger.warning(
                    f"Unexpected catalogue response format: {type(catalogue_response)}"
                )
                return []

            # Format bundles for frontend
            formatted_plans = []
            for bundle in bundles:
                if isinstance(bundle, dict):
                    plan = {
                        "id": bundle.get("name", ""),
                        "name": bundle.get("description", bundle.get("name", "")),
                        "country": (
                            bundle.get("countries", [{}])[0].get("name", "Unknown")
                            if bundle.get("countries")
                            else "Unknown"
                        ),
                        "country_code": (
                            bundle.get("countries", [{}])[0].get("iso", "")
                            if bundle.get("countries")
                            else ""
                        ),
                        "region": (
                            bundle.get("countries", [{}])[0].get("region", "Unknown")
                            if bundle.get("countries")
                            else "Unknown"
                        ),
                        "data_volume": (
                            f"{bundle.get('dataAmount', 0)}MB"
                            if bundle.get("dataAmount")
                            else "Unknown"
                        ),
                        "data_amount_mb": bundle.get("dataAmount", 0),
                        "duration_days": bundle.get("duration", 0),
                        "duration": f"{bundle.get('duration', 0)} days",
                        "price": float(bundle.get("price", 0)),
                        "speed": bundle.get("speed", []),
                        "unlimited": bundle.get("unlimited", False),
                        "autostart": bundle.get("autostart", True),
                        "image_url": bundle.get("imageUrl", ""),
                        "billing_type": bundle.get("billingType", "FixedCost"),
                    }
                    formatted_plans.append(plan)

            logger.info(f"Formatted {len(formatted_plans)} plans for frontend")
            return formatted_plans

        except TraveRoamAPIError as e:
            logger.error(f"Failed to get available plans: {str(e)}")
            return []

    def provision_esim(self, bundle_name: str, user_data: Dict) -> Dict:
        """Complete eSIM provisioning workflow"""
        try:
            # Step 1: Process order
            logger.info(f"Processing order for bundle: {bundle_name}")
            order_response = self.process_order(
                bundle_name=bundle_name, order_type="REGION"
            )

            if not order_response:
                raise TraveRoamAPIError("Failed to process order")

            # Step 2: Extract order reference
            order_reference = (
                order_response.get("reference")
                or order_response.get("order_id")
                or order_response.get("id")
            )
            if not order_reference:
                raise TraveRoamAPIError("No order reference received")

            # Step 3: Get eSIM assignments
            logger.info(f"Getting eSIM assignments for reference: {order_reference}")
            assignments_response = self.get_esim_assignments(order_reference)

            if not assignments_response:
                raise TraveRoamAPIError("Failed to get eSIM assignments")

            # Step 4: Extract eSIM details
            esim_details = self._extract_esim_details(assignments_response)

            return {
                "success": True,
                "order_reference": order_reference,
                "bundle_name": bundle_name,
                "esim_details": esim_details,
                "order_response": order_response,
                "assignments_response": assignments_response,
            }

        except TraveRoamAPIError as e:
            logger.error(f"eSIM provisioning failed: {str(e)}")
            return {"success": False, "error": str(e)}

    def _extract_esim_details(self, assignments_response: Any) -> Dict:
        """Extract eSIM details from assignments response"""
        esim_details = {
            "iccid": None,
            "qr_code": None,
            "activation_code": None,
            "smdp_address": None,
            "matching_id": None,
        }

        if isinstance(assignments_response, dict):
            esim_details.update(
                {
                    "iccid": assignments_response.get("iccid"),
                    "qr_code": assignments_response.get("qr_code"),
                    "activation_code": assignments_response.get("activation_code"),
                    "smdp_address": assignments_response.get("smdp_address"),
                    "matching_id": assignments_response.get("matching_id"),
                }
            )
        elif isinstance(assignments_response, list) and assignments_response:
            first_assignment = assignments_response[0]
            esim_details.update(
                {
                    "iccid": first_assignment.get("iccid"),
                    "qr_code": first_assignment.get("qr_code"),
                    "activation_code": first_assignment.get("activation_code"),
                    "smdp_address": first_assignment.get("smdp_address"),
                    "matching_id": first_assignment.get("matching_id"),
                }
            )

        return esim_details

    def get_esim_status(self, iccid: str) -> Dict:
        """Get comprehensive eSIM status"""
        try:
            # Get eSIM details
            esim_details = self.get_esim_details(iccid)

            # Get bundle information
            bundles = self.get_esim_bundles(iccid)

            # Get location
            location = self.get_esim_location(iccid)

            return {
                "success": True,
                "esim_details": esim_details,
                "bundles": bundles,
                "location": location,
                "status_summary": self._summarize_status(esim_details, bundles),
            }

        except TraveRoamAPIError as e:
            logger.error(f"Failed to get eSIM status: {str(e)}")
            return {"success": False, "error": str(e)}

    def _summarize_status(self, esim_details: Dict, bundles: List[Dict]) -> Dict:
        """Summarize eSIM status for frontend"""
        active_bundles = [
            b
            for b in bundles
            if isinstance(b, dict) and b.get("status") in ["Active", "Queued"]
        ]

        return {
            "has_active_bundles": len(active_bundles) > 0,
            "active_bundle_count": len(active_bundles),
            "total_bundle_count": len(bundles),
            "status": "active" if active_bundles else "inactive",
            "last_updated": esim_details.get("last_updated"),
        }


# Global instance
traveroam_client = TraveRoamAPIClient()
