from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.validators import RegexValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from simple_history.models import HistoricalRecords


class UserManager(BaseUserManager):
    def create_user(self, email, first_name, last_name, password=None, **extra_fields):
        """Create and save a regular user"""
        if not email:
            raise ValueError("The Email field must be set")
        email = self.normalize_email(email)
        user = self.model(
            email=email, first_name=first_name, last_name=last_name, **extra_fields
        )
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(
        self, email, first_name, last_name, password=None, **extra_fields
    ):
        """Create and save a superuser"""
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)
        extra_fields.setdefault("role", "admin")

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self.create_user(email, first_name, last_name, password, **extra_fields)


class User(AbstractUser):
    """
    Custom User model with email-based authentication and role-based access
    """

    ROLE_CHOICES = [
        ("admin", "Admin"),
        ("reseller", "Reseller"),
        ("client", "Client"),
        ("public_user", "Public User"),
    ]

    # Remove username field completely
    username = None

    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=30, blank=True)
    last_name = models.CharField(max_length=30, blank=True)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default="public_user")
    country_code = models.CharField(
        max_length=5,
        default="+1",
        help_text="Country code (e.g., +1, +44, +91)",
    )
    phone_number = models.CharField(
        max_length=15,
        validators=[
            RegexValidator(
                regex=r"^\d{9,15}$",
                message="Phone number must be 9-15 digits without country code.",
            )
        ],
        blank=True,
        null=True,
        help_text="Phone number without country code",
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["first_name", "last_name"]

    objects = UserManager()

    class Meta:
        db_table = "accounts_user"
        verbose_name = "User"
        verbose_name_plural = "Users"

    def __str__(self):
        return f"{self.email} ({self.get_role_display()})"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    @property
    def is_admin(self):
        return self.role == "admin"

    @property
    def is_reseller(self):
        return self.role == "reseller"

    @property
    def is_client(self):
        return self.role == "client"

    @property
    def full_phone_number(self):
        """Get full phone number with country code"""
        if self.phone_number:
            return f"{self.country_code}{self.phone_number}"
        return None

    @property
    def is_public_user(self):
        return self.role == "public_user"


class UserProfile(models.Model):
    """
    Extended user profile information
    """

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    profile_image_url = models.URLField(
        max_length=500,
        blank=True,
        null=True,
        help_text="Firebase Storage URL for profile image",
    )
    date_of_birth = models.DateField(blank=True, null=True)
    gender = models.CharField(
        max_length=10,
        choices=[("male", "Male"), ("female", "Female"), ("other", "Other")],
        blank=True,
    )
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = models.CharField(max_length=15, blank=True)
    preferences = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "accounts_user_profile"
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"

    def __str__(self):
        return f"Profile for {self.user.email}"

    def get_full_address(self):
        """Get formatted full address"""
        parts = [self.address, self.city, self.state, self.country, self.postal_code]
        return ", ".join(filter(None, parts))


class PasswordResetToken(models.Model):
    """
    Password reset token model
    """

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="password_reset_tokens"
    )
    token = models.CharField(max_length=255, unique=True)
    is_used = models.BooleanField(default=False)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "accounts_password_reset_token"
        verbose_name = "Password Reset Token"
        verbose_name_plural = "Password Reset Tokens"

    def __str__(self):
        return f"Reset token for {self.user.email}"

    def is_expired(self):
        """Check if token is expired"""
        from django.utils import timezone

        return timezone.now() > self.expires_at
