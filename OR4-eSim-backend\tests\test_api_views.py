import pytest
from django.urls import reverse
from rest_framework import status

from accounts.models import User, UserProfile
from clients.models import Client
from esim_management.models import ESIM, ESIMPlan
from orders.models import Order
from payments.models import Payment
from resellers.models import Reseller


class TestImageUploadViews:
    """Test cases for image upload views"""

    @pytest.mark.django_db
    def test_upload_profile_image_success(
        self, authenticated_client, test_image, mock_firebase_storage
    ):
        """Test successful profile image upload"""
        client, user = authenticated_client

        url = reverse("upload-profile-image")
        data = {"image": test_image}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "image_url" in response.data["data"]

    @pytest.mark.django_db
    def test_upload_profile_image_no_file(self, authenticated_client):
        """Test profile image upload without file"""
        client, user = authenticated_client

        url = reverse("upload-profile-image")
        data = {}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False

    @pytest.mark.django_db
    def test_upload_profile_image_invalid_type(
        self, authenticated_client, mock_firebase_storage
    ):
        """Test profile image upload with invalid file type"""
        from django.core.files.uploadedfile import SimpleUploadedFile

        client, user = authenticated_client

        invalid_file = SimpleUploadedFile(
            name="test.txt", content=b"invalid content", content_type="text/plain"
        )

        url = reverse("upload-profile-image")
        data = {"image": invalid_file}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid file type" in response.data["message"]

    @pytest.mark.django_db
    def test_upload_document_image_success(
        self, authenticated_client, test_image, mock_firebase_storage
    ):
        """Test successful document image upload"""
        client, user = authenticated_client

        url = reverse("upload-document-image")
        data = {"image": test_image, "document_type": "passport"}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "image_url" in response.data["data"]
        assert response.data["data"]["document_type"] == "passport"

    @pytest.mark.django_db
    def test_upload_document_image_missing_type(self, authenticated_client, test_image):
        """Test document image upload without document type"""
        client, user = authenticated_client

        url = reverse("upload-document-image")
        data = {"image": test_image}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False

    @pytest.mark.django_db
    def test_upload_document_image_unauthorized(self, api_client, test_image):
        """Test document image upload without authentication"""
        url = reverse("upload-document-image")
        data = {"image": test_image, "document_type": "passport"}

        response = api_client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.django_db
    def test_delete_profile_image_success(self, authenticated_client):
        """Test successful profile image deletion"""
        client, user = authenticated_client

        url = reverse("delete-profile-image")

        response = client.delete(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
