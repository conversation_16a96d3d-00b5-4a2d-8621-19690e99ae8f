import csv
import json

from django.db.models import Count, Q, Sum
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import Payment, StripeWebhook
from .serializers import (
    InvoiceGenerateSerializer,
    ManualPaymentApprovalSerializer,
    PaymentCreateSerializer,
    PaymentDashboardSerializer,
    PaymentFilter,
    PaymentSerializer,
    PaymentStatisticsSerializer,
    PaymentUpdateSerializer,
    RefundCreateSerializer,
    StripeWebhookSerializer,
)


class PaymentPagination(PageNumberPagination):
    """Pagination for payments"""

    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 100


class PaymentViewSet(viewsets.ModelViewSet):
    """Payment management API with admin controls"""

    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PaymentPagination
    filterset_class = PaymentFilter

    def get_queryset(self):
        """Filter payments based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Payment.objects.none()

        if not self.request.user.is_authenticated:
            return Payment.objects.none()

        queryset = Payment.objects.select_related(
            "order",
            "order__client",
            "order__reseller__user",
            "manual_approved_by",
            "refund_approved_by",
        )

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return queryset
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return queryset.filter(order__reseller__user=self.request.user)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return queryset.filter(order__client__user=self.request.user)
        return Payment.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return PaymentCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return PaymentUpdateSerializer
        return PaymentSerializer

    @action(detail=False, methods=["get"])
    def test(self, request):
        """Test endpoint to debug issues"""
        return Response({"message": "Test endpoint working", "status": "success"})

    @action(detail=False, methods=["get"])
    def dashboard(self, request):
        """Get payment dashboard data for admin"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                create_error_response(
                    "Access denied. Admin privileges required.",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
                status=status.HTTP_403_FORBIDDEN,
            )

        queryset = self.get_queryset()

        # Basic statistics
        total_payments = queryset.count()
        total_amount = queryset.aggregate(total=Sum("amount"))["total"] or 0
        completed_payments = queryset.filter(
            status=Payment.PaymentStatus.COMPLETED
        ).count()
        pending_payments = queryset.filter(status=Payment.PaymentStatus.PENDING).count()
        failed_payments = queryset.filter(status=Payment.PaymentStatus.FAILED).count()
        refunded_payments = queryset.filter(
            status=Payment.PaymentStatus.REFUNDED
        ).count()
        manual_approval_payments = queryset.filter(
            status=Payment.PaymentStatus.MANUAL_APPROVAL
        ).count()

        # Monthly statistics
        current_month = timezone.now().month
        monthly_payments = queryset.filter(created_at__month=current_month)
        monthly_amount = monthly_payments.aggregate(total=Sum("amount"))["total"] or 0

        # Payments by type
        payments_by_type = dict(
            queryset.values("payment_type")
            .annotate(count=Count("id"))
            .values_list("payment_type", "count")
        )

        # Payments by status
        payments_by_status = dict(
            queryset.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        # Top resellers by payment volume
        top_resellers = (
            queryset.values("order__reseller__user__email")
            .annotate(total_amount=Sum("amount"), payment_count=Count("id"))
            .filter(order__reseller__isnull=False)
            .order_by("-total_amount")[:5]
        )

        # Recent payments
        recent_payments = queryset.order_by("-created_at")[:10]

        dashboard_data = {
            "statistics": {
                "total_payments": total_payments,
                "total_amount": total_amount,
                "completed_payments": completed_payments,
                "pending_payments": pending_payments,
                "failed_payments": failed_payments,
                "refunded_payments": refunded_payments,
                "manual_approval_payments": manual_approval_payments,
                "monthly_payments": monthly_payments.count(),
                "monthly_amount": monthly_amount,
            },
            "payments_by_type": payments_by_type,
            "payments_by_status": payments_by_status,
            "top_resellers": list(top_resellers),
            "recent_payments": PaymentSerializer(recent_payments, many=True).data,
        }

        return Response(
            create_success_response(dashboard_data, "Payment dashboard data retrieved")
        )

    @action(detail=False, methods=["get"])
    def manual_approvals(self, request):
        """Get payments requiring manual approval (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                create_error_response(
                    "Access denied. Admin privileges required.",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
                status=status.HTTP_403_FORBIDDEN,
            )

        payments = (
            self.get_queryset()
            .filter(status=Payment.PaymentStatus.MANUAL_APPROVAL)
            .order_by("-created_at")
        )

        # Apply pagination
        page = self.paginate_queryset(payments)
        if page is not None:
            serializer = PaymentSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PaymentSerializer(payments, many=True)
        return Response(
            create_success_response(
                serializer.data, "Manual approval payments retrieved"
            )
        )

    @action(detail=True, methods=["post"])
    def approve_manual_payment(self, request, pk=None):
        """Approve or reject a manual payment (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                create_error_response(
                    "Access denied. Admin privileges required.",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
                status=status.HTTP_403_FORBIDDEN,
            )

        payment = self.get_object()

        if payment.status != Payment.PaymentStatus.MANUAL_APPROVAL:
            return Response(
                create_error_response(
                    "Payment does not require manual approval",
                    status_code=status.HTTP_400_BAD_REQUEST,
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = ManualPaymentApprovalSerializer(data=request.data)
        if serializer.is_valid():
            approved = serializer.validated_data.get("approved")
            notes = serializer.validated_data.get("notes", "")
            rejection_reason = serializer.validated_data.get("rejection_reason", "")

            if approved:
                payment.approve_manual_payment(request.user, notes)
                return Response(
                    create_success_response(None, "Payment approved successfully")
                )
            else:
                payment.reject_manual_payment(request.user, rejection_reason)
                return Response(
                    create_success_response(None, "Payment rejected successfully")
                )
        else:
            return Response(
                create_error_response(
                    "Invalid approval data",
                    errors=serializer.errors,
                    status_code=status.HTTP_400_BAD_REQUEST,
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def generate_invoice(self, request, pk=None):
        """Generate invoice for payment (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                create_error_response(
                    "Access denied. Admin privileges required.",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
                status=status.HTTP_403_FORBIDDEN,
            )

        payment = self.get_object()
        serializer = InvoiceGenerateSerializer(data=request.data)

        if serializer.is_valid():
            # Generate invoice number
            invoice_number = payment.generate_invoice_number()

            # Mark as generated
            payment.invoice_generated = True
            payment.invoice_generated_at = timezone.now()
            payment.save()

            # TODO: Generate actual PDF invoice file
            # For now, return invoice data
            invoice_data = {
                "invoice_number": invoice_number,
                "payment_id": payment.id,
                "transaction_id": payment.transaction_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "customer_name": payment.customer_name,
                "reseller_name": payment.reseller_name,
                "order_number": payment.order.order_number,
                "created_at": payment.created_at,
                "include_tax": serializer.validated_data.get("include_tax", True),
                "tax_rate": serializer.validated_data.get("tax_rate", 0.00),
                "notes": serializer.validated_data.get("notes", ""),
            }

            return Response(
                create_success_response(invoice_data, "Invoice generated successfully")
            )
        else:
            return Response(
                create_error_response(
                    "Invalid invoice data",
                    errors=serializer.errors,
                    status_code=status.HTTP_400_BAD_REQUEST,
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def export_payments(self, request):
        """Export payments data (CSV format) - Admin only"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                create_error_response(
                    "Access denied. Admin privileges required.",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
                status=status.HTTP_403_FORBIDDEN,
            )

        queryset = self.get_queryset()

        # Apply filters
        queryset = self.filterset_class(request.GET, queryset=queryset).qs

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = 'attachment; filename="payments_export.csv"'

        writer = csv.writer(response)
        writer.writerow(
            [
                "ID",
                "Transaction ID",
                "Order Number",
                "Customer Name",
                "Reseller",
                "Amount",
                "Currency",
                "Payment Type",
                "Payment Method",
                "Status",
                "Created Date",
                "Completed Date",
                "Invoice Number",
                "Refund Amount",
            ]
        )

        for payment in queryset:
            writer.writerow(
                [
                    payment.id,
                    payment.transaction_id or "",
                    payment.order.order_number,
                    payment.customer_name,
                    payment.reseller_name,
                    payment.amount,
                    payment.currency,
                    payment.get_payment_type_display(),
                    payment.payment_method,
                    payment.get_status_display(),
                    payment.created_at.strftime("%Y-%m-%d %H:%M"),
                    (
                        payment.completed_at.strftime("%Y-%m-%d %H:%M")
                        if payment.completed_at
                        else ""
                    ),
                    payment.invoice_number or "",
                    payment.refund_amount,
                ]
            )

        return response

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get comprehensive payment statistics - Admin only"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                create_error_response(
                    "Access denied. Admin privileges required.",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
                status=status.HTTP_403_FORBIDDEN,
            )

        queryset = self.get_queryset()

        # Monthly statistics
        current_month = timezone.now().month
        monthly_payments = queryset.filter(created_at__month=current_month)

        stats = {
            "total_payments": queryset.count(),
            "total_amount": queryset.aggregate(total=Sum("amount"))["total"] or 0,
            "monthly_payments": monthly_payments.count(),
            "monthly_amount": monthly_payments.aggregate(total=Sum("amount"))["total"]
            or 0,
            "completed_payments": queryset.filter(
                status=Payment.PaymentStatus.COMPLETED
            ).count(),
            "pending_payments": queryset.filter(
                status=Payment.PaymentStatus.PENDING
            ).count(),
            "failed_payments": queryset.filter(
                status=Payment.PaymentStatus.FAILED
            ).count(),
            "refunded_payments": queryset.filter(
                status=Payment.PaymentStatus.REFUNDED
            ).count(),
            "manual_approval_payments": queryset.filter(
                status=Payment.PaymentStatus.MANUAL_APPROVAL
            ).count(),
            "payments_by_type": dict(
                queryset.values("payment_type")
                .annotate(count=Count("id"))
                .values_list("payment_type", "count")
            ),
            "payments_by_status": dict(
                queryset.values("status")
                .annotate(count=Count("id"))
                .values_list("status", "count")
            ),
            "top_resellers": list(
                queryset.values("order__reseller__user__email")
                .annotate(total_amount=Sum("amount"), payment_count=Count("id"))
                .filter(order__reseller__isnull=False)
                .order_by("-total_amount")[:5]
            ),
            "recent_payments": PaymentSerializer(
                queryset.order_by("-created_at")[:10], many=True
            ).data,
        }

        return Response(create_success_response(stats, "Payment statistics retrieved"))

    @action(detail=True, methods=["post"])
    def create_refund(self, request, pk=None):
        """Create a refund for a payment (Admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                create_error_response(
                    "Access denied. Admin privileges required.",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
                status=status.HTTP_403_FORBIDDEN,
            )

        payment = self.get_object()

        if payment.status == Payment.PaymentStatus.REFUNDED:
            return Response(
                create_error_response(
                    "Payment already refunded", status_code=status.HTTP_400_BAD_REQUEST
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = RefundCreateSerializer(payment, data=request.data, partial=True)
        if serializer.is_valid():
            # Set payment status to refunded
            payment.status = Payment.PaymentStatus.REFUNDED
            payment.refund_approved_by = request.user
            payment.refund_approved_at = timezone.now()
            payment.save()

            return Response(
                create_success_response(None, "Refund created successfully")
            )
        else:
            return Response(
                create_error_response(
                    "Invalid refund data",
                    errors=serializer.errors,
                    status_code=status.HTTP_400_BAD_REQUEST,
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["post"])
    def create_manual(self, request):
        """Create a manual payment"""
        serializer = PaymentCreateSerializer(data=request.data)
        if serializer.is_valid():
            payment = serializer.save(payment_type="manual", status="manual_approval")
            return Response(
                {
                    "success": True,
                    "message": "Manual payment created successfully",
                    "data": PaymentSerializer(payment).data,
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {
                    "success": False,
                    "message": "Invalid payment data",
                    "errors": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def approve_manual(self, request, pk=None):
        """Approve a manual payment (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        payment = self.get_object()
        if payment.status != "manual_approval":
            return Response(
                {"error": "Payment does not require manual approval"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        payment.approve_manual_payment(
            request.user, request.data.get("approval_notes", "")
        )
        return Response(
            {
                "success": True,
                "message": "Payment approved successfully",
                "data": PaymentSerializer(payment).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def reject_manual(self, request, pk=None):
        """Reject a manual payment (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        payment = self.get_object()
        if payment.status != "manual_approval":
            return Response(
                {"error": "Payment does not require manual approval"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        payment.reject_manual_payment(
            request.user, request.data.get("rejection_reason", "")
        )
        return Response(
            {
                "success": True,
                "message": "Payment rejected successfully",
                "data": PaymentSerializer(payment).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def refund_list(self, request):
        """Get list of refunded payments"""
        queryset = self.get_queryset().filter(status="refunded")

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PaymentSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PaymentSerializer(queryset, many=True)
        return Response(
            {
                "success": True,
                "message": "Refunded payments retrieved successfully",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def my_payments(self, request):
        """Get payments for current user"""
        payments = self.get_queryset().order_by("-created_at")

        # Apply pagination
        page = self.paginate_queryset(payments)
        if page is not None:
            serializer = PaymentSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PaymentSerializer(payments, many=True)
        return Response(create_success_response(serializer.data, "Payments retrieved"))

    @action(detail=False, methods=["get"])
    def analytics(self, request):
        """Get payment analytics"""
        queryset = self.get_queryset()

        # Basic statistics
        total_payments = queryset.count()
        total_revenue = queryset.aggregate(total=Sum("amount"))["total"] or 0
        completed_payments = queryset.filter(
            status=Payment.PaymentStatus.COMPLETED
        ).count()
        pending_payments = queryset.filter(status=Payment.PaymentStatus.PENDING).count()
        failed_payments = queryset.filter(status=Payment.PaymentStatus.FAILED).count()

        # Status distribution
        status_distribution = dict(
            queryset.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        analytics_data = {
            "total_payments": total_payments,
            "total_revenue": float(total_revenue),
            "completed_payments": completed_payments,
            "pending_payments": pending_payments,
            "failed_payments": failed_payments,
            "status_distribution": status_distribution,
        }

        return Response(
            {
                "success": True,
                "message": "Payment analytics retrieved successfully",
                "data": analytics_data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def revenue_analytics(self, request):
        """Get payment revenue analytics"""
        queryset = self.get_queryset()

        # Revenue statistics
        total_revenue = queryset.aggregate(total=Sum("amount"))["total"] or 0
        completed_revenue = (
            queryset.filter(status=Payment.PaymentStatus.COMPLETED).aggregate(
                total=Sum("amount")
            )["total"]
            or 0
        )
        pending_revenue = (
            queryset.filter(status=Payment.PaymentStatus.PENDING).aggregate(
                total=Sum("amount")
            )["total"]
            or 0
        )

        # Monthly revenue
        from datetime import timedelta

        current_month = timezone.now().month
        monthly_revenue = (
            queryset.filter(created_at__month=current_month).aggregate(
                total=Sum("amount")
            )["total"]
            or 0
        )

        revenue_data = {
            "total_revenue": float(total_revenue),
            "completed_revenue": float(completed_revenue),
            "pending_revenue": float(pending_revenue),
            "monthly_revenue": float(monthly_revenue),
            "average_payment_value": (
                float(total_revenue / queryset.count()) if queryset.count() > 0 else 0
            ),
        }

        return Response(
            {
                "success": True,
                "message": "Revenue analytics retrieved successfully",
                "data": revenue_data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def method_analytics(self, request):
        """Get payment method analytics"""
        queryset = self.get_queryset()

        # Method distribution
        method_distribution = dict(
            queryset.values("payment_method")
            .annotate(count=Count("id"))
            .values_list("payment_method", "count")
        )

        # Method percentages
        total_payments = queryset.count()
        method_percentages = {}
        for method, count in method_distribution.items():
            method_percentages[method] = (
                (count / total_payments * 100) if total_payments > 0 else 0
            )

        method_data = {
            "method_distribution": method_distribution,
            "method_percentages": method_percentages,
            "total_payments": total_payments,
        }

        return Response(
            {
                "success": True,
                "message": "Payment method analytics retrieved successfully",
                "data": method_data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def status_analytics(self, request):
        """Get payment status analytics"""
        queryset = self.get_queryset()

        # Status distribution
        status_distribution = dict(
            queryset.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        # Status percentages
        total_payments = queryset.count()
        status_percentages = {}
        for status_name, count in status_distribution.items():
            status_percentages[status_name] = (
                (count / total_payments * 100) if total_payments > 0 else 0
            )

        status_data = {
            "status_distribution": status_distribution,
            "status_percentages": status_percentages,
            "total_payments": total_payments,
        }

        return Response(
            {
                "success": True,
                "message": "Payment status analytics retrieved successfully",
                "data": status_data,
            },
            status=status.HTTP_200_OK,
        )


class StripeWebhookViewSet(viewsets.ModelViewSet):
    """Stripe webhook management API"""

    queryset = StripeWebhook.objects.all()
    serializer_class = StripeWebhookSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter webhooks based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return StripeWebhook.objects.none()

        if not self.request.user.is_authenticated:
            return StripeWebhook.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return StripeWebhook.objects.all()
        else:
            return StripeWebhook.objects.none()
