@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%; /* white */
    --foreground: 222.2 84% 4.9%; /* slate-900 */
    --card: 0 0% 100%; /* white */
    --card-foreground: 222.2 84% 4.9%; /* slate-900 */
    --popover: 0 0% 100%; /* white */
    --popover-foreground: 222.2 84% 4.9%; /* slate-900 */
    --primary: 221.2 83.2% 53.3%; /* blue-500 */
    --primary-foreground: 210 40% 98%; /* white */
    --secondary: 210 40% 96.1%; /* slate-100 */
    --secondary-foreground: 222.2 84% 4.9%; /* slate-900 */
    --muted: 210 40% 98%; /* slate-50 */
    --muted-foreground: 215.4 16.3% 46.9%; /* slate-500 */
    --accent: 210 40% 96.1%; /* slate-100 */
    --accent-foreground: 222.2 84% 4.9%; /* slate-900 */
    --destructive: 0 84.2% 60.2%; /* red-500 */
    --destructive-foreground: 210 40% 98%; /* white */
    --success: 142.1 76.2% 36.3%; /* green-500 */
    --success-foreground: 355.7 100% 97.3%; /* white */
    --warning: 32.1 94.6% 43.7%; /* amber-500 */
    --warning-foreground: 210 40% 98%; /* white */
    --border: 214.3 31.8% 91.4%; /* slate-200 */
    --input: 214.3 31.8% 91.4%; /* slate-200 */
    --ring: 221.2 83.2% 53.3%; /* blue-500 */
  }

  .dark {
    /* Dark theme colors */
    --background: 222.2 84% 4.9%; /* slate-950 */
    --foreground: 210 40% 98%; /* slate-50 */
    --card: 222.2 84% 4.9%; /* slate-900 */
    --card-foreground: 210 40% 98%; /* slate-50 */
    --popover: 222.2 84% 4.9%; /* slate-900 */
    --popover-foreground: 210 40% 98%; /* slate-50 */
    --primary: 221.2 83.2% 53.3%; /* blue-500 */
    --primary-foreground: 210 40% 98%; /* white */
    --secondary: 217.2 32.6% 17.5%; /* slate-800 */
    --secondary-foreground: 210 40% 98%; /* slate-50 */
    --muted: 217.2 32.6% 17.5%; /* slate-900 */
    --muted-foreground: 215 20.2% 65.1%; /* slate-400 */
    --accent: 217.2 32.6% 17.5%; /* slate-800 */
    --accent-foreground: 210 40% 98%; /* slate-50 */
    --destructive: 0 62.8% 30.6%; /* red-600 */
    --destructive-foreground: 210 40% 98%; /* white */
    --success: 142.1 70.6% 45.3%; /* green-600 */
    --success-foreground: 355.7 100% 97.3%; /* white */
    --warning: 32.1 94.6% 43.7%; /* amber-600 */
    --warning-foreground: 210 40% 98%; /* white */
    --border: 217.2 32.6% 17.5%; /* slate-800 */
    --input: 217.2 32.6% 17.5%; /* slate-800 */
    --ring: 221.2 83.2% 53.3%; /* blue-500 */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground transition-colors duration-300;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Custom scrollbar for signup form */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #475569;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #64748b;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-border;
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
  }

  .btn-success {
    @apply bg-success text-success-foreground hover:bg-success/90;
  }

  .btn-warning {
    @apply bg-warning text-warning-foreground hover:bg-warning/90;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  /* Card Components */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-soft dark:shadow-dark-soft transition-all duration-200;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Input Components */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  .textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  .select {
    @apply flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }

  .badge-secondary {
    @apply border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-destructive {
    @apply border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }

  .badge-success {
    @apply border-transparent bg-success text-success-foreground hover:bg-success/80;
  }

  .badge-warning {
    @apply border-transparent bg-warning text-warning-foreground hover:bg-warning/80;
  }

  .badge-outline {
    @apply text-foreground border-border;
  }

  /* Table Components */
  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b border-border;
  }

  .table-body {
    @apply [&_tr:last-child]:border-0;
  }

  .table-footer {
    @apply border-t border-border bg-muted/50 font-medium;
  }

  .table-row {
    @apply border-b border-border transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted;
  }

  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0;
  }

  .table-cell {
    @apply p-4 align-middle [&:has([role=checkbox])]:pr-0;
  }

  /* Navigation Components */
  .nav-link {
    @apply flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200 hover:bg-accent hover:text-accent-foreground;
  }

  .nav-link-active {
    @apply bg-accent text-accent-foreground;
  }

  /* Utility Classes */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply backdrop-blur-sm bg-background/80 border border-border/50;
  }

  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg;
  }

  .fade-in {
    @apply animate-fade-in;
  }

  .slide-in {
    @apply animate-slide-in;
  }
}
