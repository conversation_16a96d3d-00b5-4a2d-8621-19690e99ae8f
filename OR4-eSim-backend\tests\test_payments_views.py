from unittest.mock import MagicMock, patch

import pytest
from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from accounts.models import User
from clients.models import Client
from orders.models import Order
from payments.models import Payment, StripeWebhook
from payments.stripe_service import StripePaymentError
from resellers.models import Reseller


class TestPaymentViews:
    """Test cases for payment views"""

    @pytest.mark.django_db
    def test_payment_list(self, admin_client):
        """Test payment list view"""
        client, user = admin_client
        url = reverse("payment-list")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_payment_create(self, reseller_client, payment_factory):
        """Test payment creation"""
        api_client, user, reseller = reseller_client
        url = reverse("payment-list")

        data = {
            "amount": "100.00",
            "currency": "USD",
            "payment_method": "stripe",
            "payment_type": "stripe",
            "status": "pending",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["amount"] == "100.00"

    @pytest.mark.django_db
    def test_payment_detail(self, admin_client, payment_factory):
        """Test payment detail view"""
        client, user = admin_client
        test_payment = payment_factory()

        url = reverse("payment-detail", kwargs={"pk": test_payment.pk})
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["id"] == test_payment.pk

    @pytest.mark.django_db
    def test_payment_update(self, admin_client, payment_factory):
        """Test payment update"""
        api_client, user = admin_client
        test_payment = payment_factory()

        url = reverse("payment-detail", kwargs={"pk": test_payment.pk})
        data = {"status": "completed"}

        response = api_client.patch(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["status"] == "completed"

    @pytest.mark.django_db
    def test_payment_filter_by_status(self, admin_client, payment_factory):
        """Test payment filtering by status"""
        client, user = admin_client
        payment_factory(status="completed")
        payment_factory(status="pending")

        url = reverse("payment-list")
        response = client.get(url, {"status": "completed"})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 1

    @pytest.mark.django_db
    def test_payment_analytics(self, admin_client, payment_factory):
        """Test payment analytics"""
        client, user = admin_client
        payment_factory(amount="100.00", status="completed")
        payment_factory(amount="50.00", status="completed")

        url = reverse("payment-analytics")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "total_revenue" in response.data["data"]


class TestPaymentAnalyticsViews:
    """Test cases for payment analytics views"""

    @pytest.mark.django_db
    def test_payment_revenue_analytics(self, admin_client, payment_factory):
        """Test payment revenue analytics"""
        client, user = admin_client
        payment_factory(amount="100.00", status="completed")
        payment_factory(amount="50.00", status="completed")

        url = reverse("payment-revenue-analytics")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "total_revenue" in response.data["data"]

    @pytest.mark.django_db
    def test_payment_method_analytics(self, admin_client, payment_factory):
        """Test payment method analytics"""
        client, user = admin_client
        payment_factory(payment_method="stripe")
        payment_factory(payment_method="manual")

        url = reverse("payment-method-analytics")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "method_distribution" in response.data["data"]

    @pytest.mark.django_db
    def test_payment_status_analytics(self, admin_client, payment_factory):
        """Test payment status analytics"""
        client, user = admin_client
        payment_factory(status="completed")
        payment_factory(status="pending")

        url = reverse("payment-status-analytics")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "status_distribution" in response.data["data"]


class TestPaymentRefundViews:
    """Test cases for payment refund views"""

    @pytest.mark.django_db
    def test_create_refund(self, admin_client, payment_factory):
        """Test payment refund creation"""
        api_client, admin_user = admin_client
        test_payment = payment_factory(status="completed")

        url = reverse("payment-create-refund", kwargs={"pk": test_payment.pk})
        data = {"refund_amount": "50.00", "refund_reason": "Customer request"}

        with patch(
            "payments.stripe_service.StripeService.refund_payment"
        ) as mock_refund:
            mock_refund.return_value = {"success": True, "refund_id": "re_test_123"}

            response = api_client.post(url, data)

            assert response.status_code == status.HTTP_200_OK
            assert response.data["success"] is True

    @pytest.mark.django_db
    def test_refund_list(self, admin_client, payment_factory):
        """Test refund list"""
        client, user = admin_client
        payment_factory(refund_amount="10.00")

        url = reverse("payment-refund-list")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data


class TestManualPaymentViews:
    """Test cases for manual payment views"""

    @pytest.mark.django_db
    def test_create_manual_payment(self, reseller_client):
        """Test manual payment creation"""
        api_client, user, reseller = reseller_client
        url = reverse("payment-create-manual")

        data = {
            "amount": "100.00",
            "currency": "USD",
            "payment_method": "bank_transfer",
            "manual_payment_notes": "Test manual payment",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["success"] is True

    @pytest.mark.django_db
    def test_approve_manual_payment(self, admin_client, payment_factory):
        """Test manual payment approval"""
        api_client, admin_user = admin_client
        test_payment = payment_factory(payment_type="manual", status="manual_approval")

        url = reverse("payment-approve-manual", kwargs={"pk": test_payment.pk})
        data = {"approval_notes": "Approved"}

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

    @pytest.mark.django_db
    def test_reject_manual_payment(self, admin_client, payment_factory):
        """Test manual payment rejection"""
        api_client, admin_user = admin_client
        test_payment = payment_factory(payment_type="manual", status="manual_approval")

        url = reverse("payment-reject-manual", kwargs={"pk": test_payment.pk})
        data = {"rejection_reason": "Invalid proof"}

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True


class TestStripeWebhookViews:
    """Test cases for Stripe webhook views"""

    @pytest.mark.django_db
    def test_stripe_webhook_list(self, admin_client, payment_factory):
        """Test Stripe webhook list"""
        client, user = admin_client
        test_payment = payment_factory()
        webhook = StripeWebhook.objects.create(
            payment=test_payment,
            event_type="payment_intent.succeeded",
            stripe_event_id="evt_test_123",
            event_data={"test": "data"},
            stripe_created_at=timezone.now(),
        )

        url = reverse("stripewebhook-list")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_stripe_webhook_detail(self, admin_client, payment_factory):
        """Test Stripe webhook detail"""
        client, admin_user = admin_client
        test_payment = payment_factory()
        webhook = StripeWebhook.objects.create(
            payment=test_payment,
            event_type="payment_intent.succeeded",
            stripe_event_id="evt_test_123",
            event_data={"test": "data"},
            stripe_created_at=timezone.now(),
        )

        url = reverse("stripewebhook-detail", kwargs={"pk": webhook.pk})
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["event_type"] == "payment_intent.succeeded"
