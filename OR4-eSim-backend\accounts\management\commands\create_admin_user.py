from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.core.management.base import BaseCommand

User = get_user_model()


class Command(BaseCommand):
    help = "Create default admin user"

    def handle(self, *args, **options):
        try:
            # Check if admin user already exists
            if User.objects.filter(role="admin").exists():
                self.stdout.write(self.style.WARNING("Admin user already exists"))
                return

            # Create admin user
            admin_user = User.objects.create_user(
                email="<EMAIL>",
                first_name="Admin",
                last_name="User",
                role="admin",
                is_staff=True,
                is_superuser=True,
                is_active=True,
            )
            admin_user.set_password("admin123")
            admin_user.save()

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully created admin user: {admin_user.email}"
                )
            )
            self.stdout.write(self.style.SUCCESS("Default password: admin123"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating admin user: {e}"))
