from django.conf import settings
from django.core.mail import send_mail
from django.db import models
from django.db.models import Count, Q, Sum
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import Reseller, ResellerActivationRequest
from .serializers import (
    ResellerActivationRequestCreateSerializer,
    ResellerActivationRequestSerializer,
    ResellerCreateSerializer,
    ResellerDashboardSerializer,
    ResellerFrontendSerializer,
    ResellerSerializer,
    ResellerUpdateSerializer,
)


class ResellerActivationRequestViewSet(viewsets.ModelViewSet):
    """Reseller activation request management API"""

    queryset = ResellerActivationRequest.objects.all()
    serializer_class = ResellerActivationRequestSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter requests based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return ResellerActivationRequest.objects.none()

        if not self.request.user.is_authenticated:
            return ResellerActivationRequest.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return ResellerActivationRequest.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return ResellerActivationRequest.objects.filter(user=self.request.user)
        return ResellerActivationRequest.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ResellerActivationRequestCreateSerializer
        return ResellerActivationRequestSerializer

    def list(self, request, *args, **kwargs):
        """Get all activation requests (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return create_error_response(
                "Access denied. Admin only.", status_code=status.HTTP_403_FORBIDDEN
            )

        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(
            create_success_response(
                data=serializer.data, message="Activation requests retrieved"
            ),
            status=status.HTTP_200_OK,
        )

    def retrieve(self, request, *args, **kwargs):
        """Get specific activation request"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(
            create_success_response(
                data=serializer.data, message="Activation request retrieved"
            ),
            status=status.HTTP_200_OK,
        )

    def update(self, request, *args, **kwargs):
        """Update activation request (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                create_error_response(message="Access denied. Admin only."),
                status=status.HTTP_403_FORBIDDEN,
            )

        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                create_success_response(
                    data=serializer.data, message="Activation request updated"
                ),
                status=status.HTTP_200_OK,
            )
        return Response(
            create_error_response(message="Update failed", errors=serializer.errors),
            status=status.HTTP_400_BAD_REQUEST,
        )

    def destroy(self, request, *args, **kwargs):
        """Delete activation request (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                create_error_response(message="Access denied. Admin only."),
                status=status.HTTP_403_FORBIDDEN,
            )

        instance = self.get_object()
        instance.delete()
        return Response(
            create_success_response(message="Activation request deleted"),
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def my_request(self, request):
        """Get current user's activation request"""
        try:
            activation_request = ResellerActivationRequest.objects.get(
                user=request.user
            )
            serializer = self.get_serializer(activation_request)
            return Response(
                create_success_response(
                    data=serializer.data, message="Request retrieved"
                ),
                status=status.HTTP_200_OK,
            )
        except ResellerActivationRequest.DoesNotExist:
            return Response(
                create_error_response(message="No activation request found"),
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=True, methods=["post"])
    def approve_request(self, request, pk=None):
        """Approve a reseller activation request (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            activation_request = self.get_object()

            if activation_request.status != "pending":
                return Response(
                    {"detail": "Request cannot be approved"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create reseller profile only when approved
            reseller, created = Reseller.objects.get_or_create(
                user=activation_request.user,
                defaults={
                    "max_clients": activation_request.max_clients,
                    "max_sims": activation_request.max_sims,
                    "credit_limit": activation_request.credit_limit,
                },
            )

            # Update activation request
            activation_request.status = "approved"
            activation_request.approved_by = request.user
            activation_request.approved_at = timezone.now()
            activation_request.save()

            # Send approval email
            try:
                send_mail(
                    subject="Reseller Account Approved - eSIM Management System",
                    message=f"""
Dear {activation_request.user.first_name},

Your reseller account has been approved by the administrator.

Your account details:
- Max Clients: {activation_request.max_clients}
- Max SIMs: {activation_request.max_sims}
- Credit Limit: ${activation_request.credit_limit}

You can now log in to your reseller dashboard and start managing clients.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[activation_request.user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Email failed to send: {str(e)}")

            return Response(
                {
                    "success": True,
                    "message": "Reseller activation request approved successfully",
                    "data": {
                        "request_id": activation_request.id,
                        "user_id": activation_request.user.id,
                        "user_email": activation_request.user.email,
                        "status": activation_request.status,
                        "approved_at": activation_request.approved_at,
                        "approved_by": request.user.email,
                    },
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to approve request: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def reject_request(self, request, pk=None):
        """Reject a reseller activation request (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            activation_request = self.get_object()

            if activation_request.status != "pending":
                return Response(
                    {"detail": "Request cannot be rejected"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            admin_notes = request.data.get("admin_notes", "")
            activation_request.status = "rejected"
            activation_request.admin_notes = admin_notes
            activation_request.rejected_at = timezone.now()
            activation_request.save()

            # Send rejection email
            try:
                send_mail(
                    subject="Reseller Account Application Status - eSIM Management System",
                    message=f"""
Dear {activation_request.user.first_name},

Your reseller account application has been reviewed by the administrator.

Status: REJECTED

{f"Admin Notes: {admin_notes}" if admin_notes else ""}

If you have any questions, please contact our support team.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[activation_request.user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Email failed to send: {str(e)}")

            return Response(
                {
                    "success": True,
                    "message": "Reseller activation request rejected",
                    "data": {
                        "request_id": activation_request.id,
                        "user_id": activation_request.user.id,
                        "user_email": activation_request.user.email,
                        "status": activation_request.status,
                        "admin_notes": activation_request.admin_notes,
                        "rejected_at": activation_request.rejected_at,
                        "rejected_by": request.user.email,
                    },
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to reject request: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def pending_requests(self, request):
        """Get all pending requests (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        pending_requests = ResellerActivationRequest.objects.filter(status="pending")
        serializer = self.get_serializer(pending_requests, many=True)
        return Response(
            {
                "success": True,
                "message": "Pending requests retrieved",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class ResellerViewSet(viewsets.ModelViewSet):
    """Reseller management API"""

    queryset = Reseller.objects.all()
    serializer_class = ResellerSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter resellers based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Reseller.objects.none()

        if not self.request.user.is_authenticated:
            return Reseller.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return Reseller.objects.all().order_by("-created_at")
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return Reseller.objects.filter(user=self.request.user).order_by(
                "-created_at"
            )
        return Reseller.objects.none()

    def get_serializer_class(self):
        if self.action in ["update", "partial_update"]:
            return ResellerUpdateSerializer
        return ResellerSerializer

    def create(self, request, *args, **kwargs):
        """Create a new reseller account with new user"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            # Get required data for new user
            email = request.data.get("email")
            first_name = request.data.get("first_name")
            last_name = request.data.get("last_name")
            password = request.data.get("password")
            country_code = request.data.get("country_code", "+1")
            phone_number = request.data.get("phone_number", "")

            # Get reseller configuration
            max_clients = request.data.get("max_clients", 100)
            max_sims = request.data.get("max_sims", 1000)
            credit_limit = request.data.get("credit_limit", 1000.00)

            # Validate required fields
            if not email or not first_name or not last_name or not password:
                return Response(
                    {
                        "detail": "email, first_name, last_name, and password are required"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if email already exists
            from django.contrib.auth import get_user_model

            User = get_user_model()

            if User.objects.filter(email=email).exists():
                return Response(
                    {"detail": "User with this email already exists"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create new user with reseller role
            user = User.objects.create_user(
                email=email,
                first_name=first_name,
                last_name=last_name,
                password=password,
                role="reseller",
                country_code=country_code,
                phone_number=phone_number,
            )

            # Create reseller account
            reseller = Reseller.objects.create(
                user=user,
                max_clients=max_clients,
                max_sims=max_sims,
                credit_limit=credit_limit,
                current_credit=credit_limit,  # Set current credit equal to limit
                is_suspended=False,
            )

            # Send welcome email
            try:
                send_mail(
                    subject="Welcome to eSIM Management System - Reseller Account Created",
                    message=f"""
Dear {user.first_name} {user.last_name},

Your reseller account has been successfully created by the administrator.

Your account details:
- Email: {user.email}
- Max Clients: {max_clients}
- Max SIMs: {max_sims}
- Credit Limit: ${credit_limit}
- Current Credit: ${credit_limit}

You can now log in to your reseller dashboard using your email and password.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Email failed to send: {str(e)}")

            serializer = self.get_serializer(reseller)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"detail": f"Failed to create reseller: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def retrieve(self, request, *args, **kwargs):
        """Get detailed reseller information for frontend"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            instance = self.get_object()
            serializer = ResellerFrontendSerializer(instance)
            return Response(
                {
                    "success": True,
                    "message": "Reseller details retrieved successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to get reseller details: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def update(self, request, *args, **kwargs):
        """Update reseller and user information (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"detail": "Validation failed", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            return Response(
                {"detail": f"Failed to update reseller: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def my_profile(self, request):
        """Get current reseller's profile"""
        try:
            reseller = Reseller.objects.get(user=request.user)
            serializer = self.get_serializer(reseller)
            return Response(
                {
                    "success": True,
                    "message": "Profile retrieved",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Reseller.DoesNotExist:
            return Response(
                {"detail": "Profile not found"}, status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get reseller statistics"""
        queryset = self.get_queryset()

        # Monthly statistics
        current_month = timezone.now().month
        monthly_orders = queryset.filter(
            orders__created_at__month=current_month
        ).distinct()

        stats = {
            "total_clients": queryset.aggregate(total=Sum("clients__count"))["total"]
            or 0,
            "total_orders": queryset.aggregate(total=Sum("orders__count"))["total"]
            or 0,
            "monthly_orders": monthly_orders.count(),
            "total_revenue": queryset.aggregate(total=Sum("total_revenue"))["total"]
            or 0,
        }

        return Response(
            {
                "success": True,
                "message": "Reseller statistics retrieved",
                "data": stats,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def suspend_reseller(self, request, pk=None):
        """Suspend a reseller (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            reseller = self.get_object()
            reason = request.data.get("reason", "")

            reseller.is_suspended = True
            reseller.suspension_reason = reason
            reseller.save()

            return Response(
                {"success": True, "message": "Reseller suspended successfully"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to suspend reseller: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def activate_reseller(self, request, pk=None):
        """Activate a suspended reseller (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            reseller = self.get_object()

            reseller.is_suspended = False
            reseller.suspension_reason = ""
            reseller.save()

            return Response(
                {"success": True, "message": "Reseller activated successfully"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to activate reseller: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def change_status(self, request, pk=None):
        """Change reseller status (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return Response(
                {"detail": "Access denied. Admin only."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            reseller = self.get_object()
            new_status = request.data.get("status")
            reason = request.data.get("reason", "")

            if not new_status:
                return Response(
                    {"detail": "Status parameter is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if new_status == "suspended":
                reseller.is_suspended = True
                reseller.suspension_reason = reason
            elif new_status == "active":
                reseller.is_suspended = False
                reseller.suspension_reason = ""
            else:
                return Response(
                    {"detail": "Invalid status. Use 'active' or 'suspended'"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            reseller.save()

            # Send email notification to reseller
            try:
                status_text = "suspended" if new_status == "suspended" else "activated"
                send_mail(
                    subject=f"Reseller Account {status_text.title()} - eSIM Management System",
                    message=f"""
Dear {reseller.user.first_name},

Your reseller account has been {status_text} by the administrator.

{f"Reason: {reason}" if reason else ""}

If you have any questions, please contact our support team.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[reseller.user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Email failed to send: {str(e)}")

            return Response(
                {
                    "success": True,
                    "message": f"Reseller status changed to {new_status} successfully",
                    "data": {
                        "id": reseller.id,
                        "is_suspended": reseller.is_suspended,
                        "suspension_reason": reseller.suspension_reason,
                    },
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"Failed to change reseller status: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["post"])
    def create_reseller(self, request):
        """Create a new reseller account with new user (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return create_error_response(
                "Access denied. Admin only.", status_code=status.HTTP_403_FORBIDDEN
            )

        try:
            # Get required data for new user
            email = request.data.get("email")
            first_name = request.data.get("first_name")
            last_name = request.data.get("last_name")
            password = request.data.get("password")
            country_code = request.data.get("country_code", "+1")
            phone_number = request.data.get("phone_number", "")

            # Get reseller configuration
            max_clients = request.data.get("max_clients", 100)
            max_sims = request.data.get("max_sims", 1000)
            credit_limit = request.data.get("credit_limit", 1000.00)

            # Validate required fields
            if not email or not first_name or not last_name or not password:
                return create_error_response(
                    "email, first_name, last_name, and password are required",
                    status_code=status.HTTP_400_BAD_REQUEST,
                )

            # Check if email already exists
            from django.contrib.auth import get_user_model

            User = get_user_model()

            if User.objects.filter(email=email).exists():
                return create_error_response(
                    "User with this email already exists",
                    status_code=status.HTTP_400_BAD_REQUEST,
                )

            # Create new user with reseller role
            user = User.objects.create_user(
                email=email,
                first_name=first_name,
                last_name=last_name,
                password=password,
                role="reseller",
                country_code=country_code,
                phone_number=phone_number,
            )

            # Create reseller account
            reseller = Reseller.objects.create(
                user=user,
                max_clients=max_clients,
                max_sims=max_sims,
                credit_limit=credit_limit,
                current_credit=credit_limit,  # Set current credit equal to limit
                is_suspended=False,
            )

            # Send welcome email
            try:
                send_mail(
                    subject="Welcome to eSIM Management System - Reseller Account Created",
                    message=f"""
Dear {user.first_name} {user.last_name},

Your reseller account has been successfully created by the administrator.

Your account details:
- Email: {user.email}
- Max Clients: {max_clients}
- Max SIMs: {max_sims}
- Credit Limit: ${credit_limit}
- Current Credit: ${credit_limit}

You can now log in to your reseller dashboard using your email and password.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[user.email],
                    fail_silently=True,
                )
            except Exception as e:
                print(f"Email failed to send: {str(e)}")

            serializer = self.get_serializer(reseller)
            return Response(
                create_success_response(
                    data=serializer.data,
                    message="Reseller account created successfully",
                ),
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                create_error_response(message=f"Failed to create reseller: {str(e)}"),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def frontend_data(self, request):
        """Get comprehensive reseller data for frontend with pagination and summary"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return create_error_response(
                "Access denied. Admin only.", status_code=status.HTTP_403_FORBIDDEN
            )

        try:
            # Simple test response
            return Response(
                create_success_response(
                    data={"message": "Frontend data endpoint working"},
                    message="Test response",
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(message=f"Failed to get reseller data: {str(e)}"),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def available_users(self, request):
        """Get list of users available to be made resellers (admin only)"""
        if not hasattr(request.user, "is_admin") or not request.user.is_admin:
            return create_error_response(
                "Access denied. Admin only.", status_code=status.HTTP_403_FORBIDDEN
            )

        try:
            from django.contrib.auth import get_user_model

            User = get_user_model()

            # Get users who are not already resellers
            existing_reseller_user_ids = Reseller.objects.values_list(
                "user_id", flat=True
            )
            available_users = User.objects.exclude(id__in=existing_reseller_user_ids)

            # Return basic user info
            users_data = []
            for user in available_users:
                users_data.append(
                    {
                        "id": user.id,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "date_joined": user.date_joined,
                    }
                )

            return Response(
                create_success_response(
                    data=users_data, message="Available users retrieved"
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(
                    message=f"Failed to get available users: {str(e)}"
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )
