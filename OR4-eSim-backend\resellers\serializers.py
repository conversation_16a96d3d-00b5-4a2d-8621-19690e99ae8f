from django.db import models
from rest_framework import serializers

from .models import Reseller, ResellerActivationRequest


class ResellerActivationRequestSerializer(serializers.ModelSerializer):
    """Reseller activation request serializer"""

    user = serializers.SerializerMethodField()

    class Meta:
        model = ResellerActivationRequest
        fields = [
            "id",
            "user",
            "max_clients",
            "max_sims",
            "credit_limit",
            "status",
            "admin_notes",
            "approved_by",
            "approved_at",
            "rejected_at",
            "created_at",
            "updated_at",
            "is_pending",
            "is_approved",
            "is_rejected",
        ]
        read_only_fields = [
            "id",
            "status",
            "admin_notes",
            "approved_by",
            "approved_at",
            "rejected_at",
            "created_at",
            "updated_at",
            "is_pending",
            "is_approved",
            "is_rejected",
        ]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data


class ResellerActivationRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating reseller activation requests"""

    user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = ResellerActivationRequest
        fields = ["user_id", "max_clients", "max_sims", "credit_limit"]

    def validate_user_id(self, value):
        from accounts.models import User

        try:
            user = User.objects.get(id=value, role="reseller")
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or not a reseller")
        return value

    def create(self, validated_data):
        user_id = validated_data.pop("user_id")
        from accounts.models import User

        user = User.objects.get(id=user_id)
        validated_data["user"] = user
        return super().create(validated_data)


class ResellerSerializer(serializers.ModelSerializer):
    """Reseller serializer"""

    user = serializers.SerializerMethodField()
    total_clients = serializers.SerializerMethodField()
    total_orders = serializers.SerializerMethodField()

    class Meta:
        model = Reseller
        fields = [
            "id",
            "user",
            "max_clients",
            "max_sims",
            "credit_limit",
            "current_credit",
            "is_suspended",
            "created_at",
            "updated_at",
            "total_clients",
            "total_orders",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data

    def get_total_clients(self, obj):
        return obj.clients.count()

    def get_total_orders(self, obj):
        return obj.orders.count()


class ResellerCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating resellers"""

    user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Reseller
        fields = ["user_id", "max_clients", "max_sims", "credit_limit"]

    def validate_user_id(self, value):
        from accounts.models import User

        try:
            user = User.objects.get(id=value, role="reseller")
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or not a reseller")
        return value

    def create(self, validated_data):
        user_id = validated_data.pop("user_id")
        from accounts.models import User

        user = User.objects.get(id=user_id)
        validated_data["user"] = user
        return super().create(validated_data)


class ResellerUpdateSerializer(serializers.ModelSerializer):
    """Comprehensive serializer for updating reseller and user data"""

    # User fields that can be updated
    email = serializers.CharField(required=False)
    first_name = serializers.CharField(required=False)
    last_name = serializers.CharField(required=False)
    country_code = serializers.CharField(required=False)
    phone_number = serializers.CharField(required=False)

    # Reseller fields
    max_clients = serializers.IntegerField(required=False)
    max_sims = serializers.IntegerField(required=False)
    credit_limit = serializers.DecimalField(
        max_digits=10, decimal_places=2, required=False
    )

    class Meta:
        model = Reseller
        fields = [
            "email",
            "first_name",
            "last_name",
            "country_code",
            "phone_number",
            "max_clients",
            "max_sims",
            "credit_limit",
        ]

    def validate_email(self, value):
        """Validate email uniqueness"""
        if value:
            from accounts.models import User

            user = self.instance.user
            if User.objects.filter(email=value).exclude(id=user.id).exists():
                raise serializers.ValidationError("User with this email already exists")
        return value

    def validate_phone_number(self, value):
        """Validate phone number format"""
        if value and not value.isdigit():
            raise serializers.ValidationError("Phone number must contain only digits")
        return value

    def update(self, instance, validated_data):
        """Update both user and reseller data"""
        user_data = {}
        reseller_data = {}

        # Separate user and reseller data
        for field, value in validated_data.items():
            if field in [
                "email",
                "first_name",
                "last_name",
                "country_code",
                "phone_number",
            ]:
                user_data[field] = value
            else:
                reseller_data[field] = value

        # Update user data
        if user_data:
            user = instance.user
            for field, value in user_data.items():
                setattr(user, field, value)
            user.save()

        # Update reseller data
        for field, value in reseller_data.items():
            setattr(instance, field, value)
        instance.save()

        return instance


class ResellerDashboardSerializer(serializers.Serializer):
    """Reseller dashboard serializer"""

    reseller = ResellerSerializer()
    stats = serializers.DictField()


class ResellerFrontendSerializer(serializers.ModelSerializer):
    """Basic reseller serializer for frontend"""

    # Core user information
    id = serializers.IntegerField(source="user.id")
    email = serializers.CharField(source="user.email")
    firstName = serializers.CharField(source="user.first_name")
    lastName = serializers.CharField(source="user.last_name")
    name = serializers.SerializerMethodField()

    # Phone information
    phone = serializers.SerializerMethodField()
    phone_country_code = serializers.CharField(source="user.country_code")
    phone_number = serializers.CharField(source="user.phone_number")

    # Status and dates
    status = serializers.SerializerMethodField()
    joinDate = serializers.DateTimeField(source="user.created_at")
    lastLogin = serializers.DateTimeField(source="user.last_login")

    # Business configuration
    simLimit = serializers.IntegerField(source="max_sims")
    creditLimit = serializers.DecimalField(
        source="credit_limit", max_digits=10, decimal_places=2
    )
    currentCredit = serializers.DecimalField(
        source="current_credit", max_digits=10, decimal_places=2
    )
    maxClients = serializers.IntegerField(source="max_clients")
    notes = serializers.CharField(source="suspension_reason", default="")

    # Performance metrics (simplified)
    totalSales = serializers.SerializerMethodField()
    totalOrders = serializers.SerializerMethodField()
    activeClients = serializers.SerializerMethodField()
    activeSims = serializers.SerializerMethodField()
    monthlyRevenue = serializers.SerializerMethodField()
    commissionRate = serializers.SerializerMethodField()
    totalCommission = serializers.SerializerMethodField()

    # Account activity
    isVerified = serializers.SerializerMethodField()
    isApproved = serializers.SerializerMethodField()
    approvalDate = serializers.SerializerMethodField()
    approvedBy = serializers.SerializerMethodField()
    blockReason = serializers.SerializerMethodField()
    suspensionReason = serializers.CharField(source="suspension_reason", default="")

    # System fields
    createdAt = serializers.DateTimeField(source="created_at")
    updatedAt = serializers.DateTimeField(source="updated_at")

    class Meta:
        model = Reseller
        fields = [
            "id",
            "email",
            "firstName",
            "lastName",
            "name",
            "phone",
            "phone_country_code",
            "phone_number",
            "status",
            "joinDate",
            "lastLogin",
            "simLimit",
            "creditLimit",
            "currentCredit",
            "maxClients",
            "notes",
            "totalSales",
            "totalOrders",
            "activeClients",
            "activeSims",
            "monthlyRevenue",
            "commissionRate",
            "totalCommission",
            "isVerified",
            "isApproved",
            "approvalDate",
            "approvedBy",
            "blockReason",
            "suspensionReason",
            "createdAt",
            "updatedAt",
        ]

    def get_name(self, obj):
        """Get full name"""
        return f"{obj.user.first_name} {obj.user.last_name}".strip()

    def get_phone(self, obj):
        """Get full phone number with country code"""
        if obj.user.phone_number:
            return f"{obj.user.country_code}{obj.user.phone_number}"
        return None

    def get_status(self, obj):
        """Get reseller status"""
        if obj.is_suspended:
            return "suspended"
        return "active"

    def get_totalSales(self, obj):
        return 0

    def get_totalOrders(self, obj):
        return 0

    def get_activeClients(self, obj):
        return 0

    def get_activeSims(self, obj):
        return 0

    def get_monthlyRevenue(self, obj):
        return 0.0

    def get_commissionRate(self, obj):
        return 15.0

    def get_totalCommission(self, obj):
        return 0.0

    def get_isVerified(self, obj):
        return obj.user.is_active

    def get_isApproved(self, obj):
        return not obj.is_suspended

    def get_approvalDate(self, obj):
        return obj.created_at

    def get_approvedBy(self, obj):
        return "<EMAIL>"

    def get_blockReason(self, obj):
        if obj.is_suspended:
            return obj.suspension_reason or "Account suspended"
        return None
