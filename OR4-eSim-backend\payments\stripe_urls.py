from django.urls import path

from .stripe_views import (
    StripeBundleCheckoutAPIView,
    StripeBundlePurchaseListAPIView,
    StripePaymentIntentAPIView,
    StripePaymentStatusAPIView,
    stripe_webhook,
)

app_name = "stripe_payments"

urlpatterns = [
    # Stripe Checkout
    path(
        "checkout/bundle/",
        StripeBundleCheckoutAPIView.as_view(),
        name="bundle_checkout",
    ),
    path(
        "create-checkout-session/",
        StripeBundleCheckoutAPIView.as_view(),
        name="create_checkout_session",
    ),
    path(
        "create-bundle-purchase/",
        StripeBundleCheckoutAPIView.as_view(),
        name="create_bundle_purchase",
    ),
    # Payment Intent
    path(
        "payment-intent/create/",
        StripePaymentIntentAPIView.as_view(),
        name="create_payment_intent",
    ),
    path(
        "create-payment-intent/",
        StripePaymentIntentAPIView.as_view(),
        name="create_payment_intent_alt",
    ),
    path(
        "payment-intent/<str:payment_intent_id>/status/",
        StripePaymentStatusAPIView.as_view(),
        name="payment_status",
    ),
    path(
        "retrieve-checkout-session/",
        StripePaymentStatusAPIView.as_view(),
        name="retrieve_checkout_session",
    ),
    # Bundle Purchases
    path(
        "purchases/", StripeBundlePurchaseListAPIView.as_view(), name="bundle_purchases"
    ),
    path(
        "list-bundle-purchases/",
        StripeBundlePurchaseListAPIView.as_view(),
        name="list_bundle_purchases",
    ),
    path(
        "bundle-purchase/<int:pk>/",
        StripeBundlePurchaseListAPIView.as_view(),
        name="bundle_purchase_detail",
    ),
    # Webhook
    path("webhook/", stripe_webhook, name="stripe_webhook"),
]
