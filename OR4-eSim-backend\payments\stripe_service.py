# -*- coding: utf-8 -*-
"""
Professional Stripe Payment Service for TraveRoam Bundle Purchases
Component-based architecture for easy customization and maintenance
"""

import logging
from decimal import Decimal
from typing import Any, Dict, List, Optional

import stripe
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)


class StripePaymentError(Exception):
    """Custom exception for Stripe payment errors"""

    pass


class StripeService:
    """
    Professional Stripe service for handling TraveRoam bundle payments
    Component-based design for easy customization
    """

    def __init__(self):
        """Initialize Stripe service with environment configuration"""
        stripe.api_key = settings.STRIPE_SECRET_KEY
        self.public_key = settings.STRIPE_PUBLIC_KEY
        self.webhook_secret = settings.STRIPE_WEBHOOK_SECRET
        self.live_mode = settings.STRIPE_LIVE_MODE
        self.currency = settings.STRIPE_CURRENCY
        self.success_url = settings.STRIPE_SUCCESS_URL
        self.cancel_url = settings.STRIPE_CANCEL_URL

        logger.info(f"Stripe service initialized - Live mode: {self.live_mode}")

    def create_payment_intent(
        self,
        amount: Decimal,
        currency: str = None,
        metadata: Dict = None,
        customer_id: str = None,
    ) -> Dict:
        """
        Create a Stripe Payment Intent for bundle purchase

        Args:
            amount: Payment amount in smallest currency unit (cents for USD)
            currency: Currency code (defaults to settings.STRIPE_CURRENCY)
            metadata: Additional metadata to attach to payment
            customer_id: Stripe customer ID if available

        Returns:
            Dict containing payment intent details
        """
        try:
            currency = currency or self.currency

            # Convert amount to cents if needed
            if isinstance(amount, Decimal):
                amount_cents = int(amount * 100)
            else:
                amount_cents = int(float(amount) * 100)

            payment_intent_data = {
                "amount": amount_cents,
                "currency": currency.lower(),
                "automatic_payment_methods": {
                    "enabled": True,
                },
                "metadata": metadata or {},
            }

            if customer_id:
                payment_intent_data["customer"] = customer_id

            payment_intent = stripe.PaymentIntent.create(**payment_intent_data)

            logger.info(f"Created payment intent: {payment_intent.id} for ${amount}")

            return {
                "success": True,
                "payment_intent_id": payment_intent.id,
                "client_secret": payment_intent.client_secret,
                "amount": amount,
                "currency": currency,
                "status": payment_intent.status,
                "metadata": payment_intent.metadata,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating payment intent: {str(e)}")
            raise StripePaymentError(f"Failed to create payment intent: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating payment intent: {str(e)}")
            raise StripePaymentError(f"Payment processing error: {str(e)}")

    def create_checkout_session(
        self,
        line_items: List[Dict],
        metadata: Dict = None,
        customer_email: str = None,
        success_url: str = None,
        cancel_url: str = None,
    ) -> Dict:
        """
        Create a Stripe Checkout Session for bundle purchase

        Args:
            line_items: List of items to purchase
            metadata: Additional metadata
            customer_email: Customer email for prefilling
            success_url: Custom success URL
            cancel_url: Custom cancel URL

        Returns:
            Dict containing checkout session details
        """
        try:
            session_data = {
                "payment_method_types": ["card"],
                "line_items": line_items,
                "mode": "payment",
                "success_url": success_url or self.success_url,
                "cancel_url": cancel_url or self.cancel_url,
                "metadata": metadata or {},
            }

            if customer_email:
                session_data["customer_email"] = customer_email

            # Add automatic tax calculation if enabled
            if (
                hasattr(settings, "STRIPE_AUTOMATIC_TAX")
                and settings.STRIPE_AUTOMATIC_TAX
            ):
                session_data["automatic_tax"] = {"enabled": True}

            session = stripe.checkout.Session.create(**session_data)

            logger.info(f"Created checkout session: {session.id}")

            return {
                "success": True,
                "session_id": session.id,
                "checkout_url": session.url,
                "payment_status": session.payment_status,
                "metadata": session.metadata,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating checkout session: {str(e)}")
            raise StripePaymentError(f"Failed to create checkout session: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating checkout session: {str(e)}")
            raise StripePaymentError(f"Checkout processing error: {str(e)}")

    def create_bundle_checkout_session(
        self, bundle_data: Dict, client_data: Dict, reseller_data: Dict = None
    ) -> Dict:
        """
        Create checkout session specifically for TraveRoam bundle purchase

        Args:
            bundle_data: Bundle information (name, price, description)
            client_data: Client information
            reseller_data: Reseller information (optional)

        Returns:
            Dict containing checkout session details
        """
        try:
            # Prepare line items for the bundle
            line_items = [
                {
                    "price_data": {
                        "currency": self.currency.lower(),
                        "product_data": {
                            "name": bundle_data.get("name", "eSIM Bundle"),
                            "description": bundle_data.get(
                                "description", "TraveRoam eSIM Bundle"
                            ),
                            "metadata": {
                                "bundle_name": bundle_data.get("bundle_name"),
                                "country": bundle_data.get("country", ""),
                                "data_volume": bundle_data.get("data_volume", ""),
                                "validity_days": str(
                                    bundle_data.get("validity_days", 0)
                                ),
                            },
                        },
                        "unit_amount": int(
                            float(bundle_data.get("price", 0)) * 100
                        ),  # Convert to cents
                    },
                    "quantity": 1,
                }
            ]

            # Prepare metadata
            metadata = {
                "bundle_name": bundle_data.get("bundle_name"),
                "client_id": str(client_data.get("id")),
                "client_email": client_data.get("email"),
                "client_phone": client_data.get("phone_number"),
                "purchase_type": "traveroam_bundle",
                "timestamp": timezone.now().isoformat(),
            }

            if reseller_data:
                metadata.update(
                    {
                        "reseller_id": str(reseller_data.get("id")),
                        "reseller_name": reseller_data.get("company_name", ""),
                    }
                )

            # Create checkout session
            return self.create_checkout_session(
                line_items=line_items,
                metadata=metadata,
                customer_email=client_data.get("email"),
                success_url=f"{self.success_url}?session_id={{CHECKOUT_SESSION_ID}}",
                cancel_url=f"{self.cancel_url}?bundle={bundle_data.get('bundle_name')}",
            )

        except Exception as e:
            logger.error(f"Error creating bundle checkout session: {str(e)}")
            raise StripePaymentError(f"Bundle checkout error: {str(e)}")

    def create_bundle_purchase_session(
        self, bundle_data: Dict, client_data: Dict, reseller_data: Dict = None
    ) -> Dict:
        """
        Create checkout session specifically for TraveRoam bundle purchase
        Alias for create_bundle_checkout_session for backward compatibility
        """
        return self.create_bundle_checkout_session(
            bundle_data, client_data, reseller_data
        )

    def create_refund(
        self, payment_intent_id: str, amount: Decimal = None, reason: str = None
    ) -> Dict:
        """
        Create a refund for a payment
        Alias for refund_payment for backward compatibility
        """
        return self.refund_payment(payment_intent_id, amount, reason)

    def retrieve_payment_intent(self, payment_intent_id: str) -> Dict:
        """Retrieve payment intent details"""
        try:
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)

            return {
                "success": True,
                "payment_intent_id": payment_intent.id,
                "status": payment_intent.status,
                "amount": payment_intent.amount / 100,  # Convert from cents
                "currency": payment_intent.currency.upper(),
                "metadata": payment_intent.metadata,
                "created": payment_intent.created,
                "client_secret": payment_intent.client_secret,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error retrieving payment intent: {str(e)}")
            raise StripePaymentError(f"Failed to retrieve payment: {str(e)}")

    def retrieve_checkout_session(self, session_id: str) -> Dict:
        """Retrieve checkout session details"""
        try:
            session = stripe.checkout.Session.retrieve(session_id)

            return {
                "success": True,
                "session_id": session.id,
                "payment_status": session.payment_status,
                "payment_intent_id": session.payment_intent,
                "customer_email": (
                    session.customer_details.email if session.customer_details else None
                ),
                "amount_total": (
                    session.amount_total / 100 if session.amount_total else 0
                ),
                "currency": (
                    session.currency.upper() if session.currency else self.currency
                ),
                "metadata": session.metadata,
                "created": session.created,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error retrieving checkout session: {str(e)}")
            raise StripePaymentError(f"Failed to retrieve checkout session: {str(e)}")

    def create_customer(
        self, email: str, name: str = None, phone: str = None, metadata: Dict = None
    ) -> Dict:
        """Create a Stripe customer"""
        try:
            customer_data = {"email": email, "metadata": metadata or {}}

            if name:
                customer_data["name"] = name
            if phone:
                customer_data["phone"] = phone

            customer = stripe.Customer.create(**customer_data)

            logger.info(f"Created Stripe customer: {customer.id} for {email}")

            return {
                "success": True,
                "customer_id": customer.id,
                "email": customer.email,
                "name": customer.name,
                "phone": customer.phone,
                "metadata": customer.metadata,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating customer: {str(e)}")
            raise StripePaymentError(f"Failed to create customer: {str(e)}")

    def refund_payment(
        self, payment_intent_id: str, amount: Decimal = None, reason: str = None
    ) -> Dict:
        """Refund a payment"""
        try:
            refund_data = {"payment_intent": payment_intent_id}

            if amount:
                refund_data["amount"] = int(float(amount) * 100)  # Convert to cents

            if reason:
                refund_data["reason"] = reason

            refund = stripe.Refund.create(**refund_data)

            logger.info(f"Created refund: {refund.id} for payment {payment_intent_id}")

            return {
                "success": True,
                "refund_id": refund.id,
                "amount": refund.amount / 100,
                "currency": refund.currency.upper(),
                "status": refund.status,
                "reason": refund.reason,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating refund: {str(e)}")
            raise StripePaymentError(f"Failed to create refund: {str(e)}")

    def handle_webhook_event(self, event_data: Dict) -> bool:
        """
        Handle webhook event processing
        This is a placeholder method for backward compatibility
        """
        try:
            event_type = event_data.get("type")
            logger.info(f"Processing webhook event: {event_type}")

            # This method would typically process the webhook event
            # For now, we'll just return True to indicate success
            return True

        except Exception as e:
            logger.error(f"Error handling webhook event: {str(e)}")
            return False

    def verify_webhook_signature(self, payload: bytes, signature: str) -> bool:
        """Verify Stripe webhook signature"""
        try:
            stripe.Webhook.construct_event(payload, signature, self.webhook_secret)
            return True
        except stripe.error.SignatureVerificationError:
            logger.warning("Invalid Stripe webhook signature")
            return False
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {str(e)}")
            return False

    def construct_webhook_event(self, payload: bytes, signature: str) -> Dict:
        """Construct and validate webhook event"""
        try:
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            return {
                "success": True,
                "event": event,
                "event_type": event["type"],
                "event_id": event["id"],
                "data": event["data"]["object"],
            }
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid webhook signature: {str(e)}")
            raise StripePaymentError(f"Invalid webhook signature: {str(e)}")
        except Exception as e:
            logger.error(f"Error constructing webhook event: {str(e)}")
            raise StripePaymentError(f"Webhook processing error: {str(e)}")

    def get_payment_methods(self, customer_id: str) -> List[Dict]:
        """Get customer's saved payment methods"""
        try:
            payment_methods = stripe.PaymentMethod.list(
                customer=customer_id, type="card"
            )

            return [
                {
                    "id": pm.id,
                    "type": pm.type,
                    "card": (
                        {
                            "brand": pm.card.brand,
                            "last4": pm.card.last4,
                            "exp_month": pm.card.exp_month,
                            "exp_year": pm.card.exp_year,
                        }
                        if pm.card
                        else None
                    ),
                }
                for pm in payment_methods.data
            ]

        except stripe.error.StripeError as e:
            logger.error(f"Error retrieving payment methods: {str(e)}")
            return []

    def calculate_bundle_price(
        self, bundle_data: Dict, reseller_markup: Decimal = None
    ) -> Decimal:
        """Calculate final bundle price with optional reseller markup"""
        try:
            base_price = Decimal(str(bundle_data.get("price", 0)))

            if reseller_markup:
                markup_amount = base_price * (reseller_markup / 100)
                final_price = base_price + markup_amount
            else:
                final_price = base_price

            # Round to 2 decimal places
            return final_price.quantize(Decimal("0.01"))

        except Exception as e:
            logger.error(f"Error calculating bundle price: {str(e)}")
            return Decimal("0.00")

    def get_supported_currencies(self) -> List[str]:
        """Get list of supported currencies"""
        # Common currencies supported by Stripe
        return [
            "USD",
            "EUR",
            "GBP",
            "CAD",
            "AUD",
            "JPY",
            "CHF",
            "SEK",
            "NOK",
            "DKK",
            "PLN",
            "CZK",
            "HUF",
            "BGN",
            "RON",
            "HRK",
            "INR",
            "SGD",
            "HKD",
            "MYR",
            "THB",
            "PHP",
            "IDR",
            "KRW",
            "TWD",
            "NZD",
            "MXN",
            "BRL",
            "ARS",
            "CLP",
        ]

    def format_amount_for_display(self, amount: Decimal, currency: str = None) -> str:
        """Format amount for display with currency symbol"""
        currency = currency or self.currency

        currency_symbols = {
            "USD": "$",
            "EUR": "€",
            "GBP": "£",
            "JPY": "¥",
            "INR": "₹",
            "CAD": "C$",
            "AUD": "A$",
            "CHF": "CHF",
            "SEK": "kr",
            "NOK": "kr",
        }

        symbol = currency_symbols.get(currency.upper(), currency.upper() + " ")

        if currency.upper() in ["JPY", "KRW"]:  # Zero decimal currencies
            return f"{symbol}{int(amount)}"
        else:
            return f"{symbol}{amount:.2f}"


# Global Stripe service instance
stripe_service = StripeService()
