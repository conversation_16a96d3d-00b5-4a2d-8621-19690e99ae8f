idea for the pie Graph
You are given a task to integrate an existing React component in the codebase

The codebase should support:
- shadcn project structure  
- Tailwind CSS
- Typescript

If it doesn't, provide instructions on how to setup project via shadcn CLI, install Tail<PERSON> or Typescript.

Determine the default path for components and styles. 
If default path for components is not /components/ui, provide instructions on why it's important to create this folder
Copy-paste this component to /components/ui folder:
```tsx
pie-chart.tsx

import React, { useState } from 'react';
import { Pie, ProvidedProps, PieArcDatum } from '@visx/shape';
import { scaleOrdinal } from '@visx/scale';
import { Group } from '@visx/group';
import { GradientPinkBlue } from '@visx/gradient';
import { letterFrequency, browserUsage, LetterFrequency } from '@visx/mock-data';
import { animated, useTransition, interpolate } from '@react-spring/web';

type BrowserNames = keyof (typeof browserUsage)[0];

interface BrowserUsage {
  label: BrowserNames;
  usage: number;
}

const letters: LetterFrequency[] = letterFrequency.slice(0, 4);
const browserNames = Object.keys(browserUsage[0]).filter((k) => k !== 'date') as BrowserNames[];
const browsers: BrowserUsage[] = browserNames.map((name) => ({
  label: name,
  usage: Number(browserUsage[0][name]),
}));

const usage = (d: BrowserUsage) => d.usage;
const frequency = (d: LetterFrequency) => d.frequency;

const getBrowserColor = scaleOrdinal({
  domain: browserNames,
  range: [
    'rgba(255,255,255,0.7)',
    'rgba(255,255,255,0.6)',
    'rgba(255,255,255,0.5)',
    'rgba(255,255,255,0.4)',
    'rgba(255,255,255,0.3)',
    'rgba(255,255,255,0.2)',
    'rgba(255,255,255,0.1)',
  ],
});
const getLetterFrequencyColor = scaleOrdinal({
  domain: letters.map((l) => l.letter),
  range: ['rgba(93,30,91,1)', 'rgba(93,30,91,0.8)', 'rgba(93,30,91,0.6)', 'rgba(93,30,91,0.4)'],
});

const defaultMargin = { top: 20, right: 20, bottom: 20, left: 20 };

export type PieChartProps = {
  width: number;
  height: number;
  margin?: typeof defaultMargin;
  animate?: boolean;
};

export const PieChart = ({
  width,
  height,
  margin = defaultMargin,
  animate = true,
}: PieChartProps) => {
  const [selectedBrowser, setSelectedBrowser] = useState<string | null>(null);
  const [selectedAlphabetLetter, setSelectedAlphabetLetter] = useState<string | null>(null);

  if (width < 10) return null;

  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;
  const radius = Math.min(innerWidth, innerHeight) / 2;
  const centerY = innerHeight / 2;
  const centerX = innerWidth / 2;
  const donutThickness = 50;

  return (
    <svg width={width} height={height}>
      <GradientPinkBlue id="visx-pie-gradient" />
      <rect rx={14} width={width} height={height} fill="url('#visx-pie-gradient')" />
      <Group top={centerY + margin.top} left={centerX + margin.left}>
        <Pie
          data={
            selectedBrowser ? browsers.filter(({ label }) => label === selectedBrowser) : browsers
          }
          pieValue={usage}
          outerRadius={radius}
          innerRadius={radius - donutThickness}
          cornerRadius={3}
          padAngle={0.005}
        >
          {(pie) => (
            <AnimatedPie<BrowserUsage>
              {...pie}
              animate={animate}
              getKey={(arc) => arc.data.label}
              onClickDatum={({ data: { label } }) =>
                animate &&
                setSelectedBrowser(selectedBrowser && selectedBrowser === label ? null : label)
              }
              getColor={(arc) => getBrowserColor(arc.data.label)}
            />
          )}
        </Pie>
        <Pie
          data={
            selectedAlphabetLetter
              ? letters.filter(({ letter }) => letter === selectedAlphabetLetter)
              : letters
          }
          pieValue={frequency}
          pieSortValues={() => -1}
          outerRadius={radius - donutThickness * 1.3}
        >
          {(pie) => (
            <AnimatedPie<LetterFrequency>
              {...pie}
              animate={animate}
              getKey={({ data: { letter } }) => letter}
              onClickDatum={({ data: { letter } }) =>
                animate &&
                setSelectedAlphabetLetter(
                  selectedAlphabetLetter && selectedAlphabetLetter === letter ? null : letter,
                )
              }
              getColor={({ data: { letter } }) => getLetterFrequencyColor(letter)}
            />
          )}
        </Pie>
      </Group>
      {animate && (
        <text
          textAnchor="end"
          x={width - 16}
          y={height - 16}
          fill="white"
          fontSize={11}
          fontWeight={300}
          pointerEvents="none"
        >
          Click segments to update
        </text>
      )}
    </svg>
  );
};

type AnimatedStyles = { startAngle: number; endAngle: number; opacity: number };

const fromLeaveTransition = ({ endAngle }: PieArcDatum<any>) => ({
  startAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
  endAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
  opacity: 0,
});
const enterUpdateTransition = ({ startAngle, endAngle }: PieArcDatum<any>) => ({
  startAngle,
  endAngle,
  opacity: 1,
});

type AnimatedPieProps<Datum> = ProvidedProps<Datum> & {
  animate?: boolean;
  getKey: (d: PieArcDatum<Datum>) => string;
  getColor: (d: PieArcDatum<Datum>) => string;
  onClickDatum: (d: PieArcDatum<Datum>) => void;
  delay?: number;
};

function AnimatedPie<Datum>({
  animate,
  arcs,
  path,
  getKey,
  getColor,
  onClickDatum,
}: AnimatedPieProps<Datum>) {
  const transitions = useTransition<PieArcDatum<Datum>, AnimatedStyles>(arcs, {
    from: animate ? fromLeaveTransition : enterUpdateTransition,
    enter: enterUpdateTransition,
    update: enterUpdateTransition,
    leave: animate ? fromLeaveTransition : enterUpdateTransition,
    keys: getKey,
  });
  return transitions((props, arc, { key }) => {
    const [centroidX, centroidY] = path.centroid(arc);
    const hasSpaceForLabel = arc.endAngle - arc.startAngle >= 0.1;

    return (
      <g key={key}>
        <animated.path
          d={interpolate([props.startAngle, props.endAngle], (startAngle, endAngle) =>
            path({
              ...arc,
              startAngle,
              endAngle,
            }),
          )}
          fill={getColor(arc)}
          onClick={() => onClickDatum(arc)}
          onTouchStart={() => onClickDatum(arc)}
        />
        {hasSpaceForLabel && (
          <animated.g style={{ opacity: props.opacity }}>
            <text
              fill="white"
              x={centroidX}
              y={centroidY}
              dy=".33em"
              fontSize={9}
              textAnchor="middle"
              pointerEvents="none"
            >
              {getKey(arc)}
            </text>
          </animated.g>
        )}
      </g>
    );
  });
}

demo.tsx
import { PieChart } from "@/components/ui/pie-chart";

const DemoPieChart = () => {
  const width = 600;
  const height = 400;

  return (
    <div className="flex w-full h-screen justify-center items-center bg-gray-100">
      <PieChart width={width} height={height} animate={true} />
    </div>
  );
};

export { DemoPieChart };
```

Install NPM dependencies:
```bash
@visx/group, @visx/scale, @visx/shape, @visx/gradient, @visx/mock-data, @react-spring/web
```

Implementation Guidelines
 1. Analyze the component structure and identify all required dependencies
 2. Review the component's argumens and state
 3. Identify any required context providers or hooks and install them
 4. Questions to Ask
 - What data/props will be passed to this component?
 - Are there any specific state management requirements?
 - Are there any required assets (images, icons, etc.)?
 - What is the expected responsive behavior?
 - What is the best place to use this component in the app?

Steps to integrate
 0. Copy paste all the code above in the correct directories
 1. Install external dependencies
 2. Fill image assets with Unsplash stock images you know exist
 3. Use lucide-react icons for svgs or logos if component requires them

graph idea

You are given a task to integrate an existing React component in the codebase

The codebase should support:
- shadcn project structure  
- Tailwind CSS
- Typescript

If it doesn't, provide instructions on how to setup project via shadcn CLI, install Tailwind or Typescript.

Determine the default path for components and styles. 
If default path for components is not /components/ui, provide instructions on why it's important to create this folder
Copy-paste this component to /components/ui folder:
```tsx
line-graph-statistics.tsx
import React, { useState, useEffect } from 'react';

const CleanWireframeAnalytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('Last 30 days');
  const [hoveredPoint, setHoveredPoint] = useState(null);
  const [animationPhase, setAnimationPhase] = useState(0);
  const [chartVisible, setChartVisible] = useState(false);

  // Sample data for different periods
  const data = {
    'Last 3 months': {
      dates: ['Jun 1', 'Jun 3', 'Jun 5', 'Jun 7', 'Jun 9', 'Jun 12', 'Jun 15', 'Jun 18', 'Jun 21', 'Jun 24', 'Jun 27', 'Jun 30'],
      mobile: [290, 270, 310, 280, 260, 350, 320, 340, 400, 370, 420, 480],
      desktop: [200, 180, 220, 255, 230, 280, 260, 270, 300, 285, 310, 320],
      peak: 480,
      average: 315,
      growth: '+15%'
    },
    'Last 30 days': {
      dates: ['Jun 1', 'Jun 3', 'Jun 5', 'Jun 7', 'Jun 9', 'Jun 12', 'Jun 15', 'Jun 18', 'Jun 21', 'Jun 24', 'Jun 27', 'Jun 30'],
      mobile: [290, 270, 310, 280, 260, 350, 320, 340, 400, 370, 420, 480],
      desktop: [200, 180, 220, 255, 230, 280, 260, 270, 300, 285, 310, 320],
      peak: 480,
      average: 315,
      growth: '+12%'
    },
    'Last 7 days': {
      dates: ['Jun 24', 'Jun 25', 'Jun 26', 'Jun 27', 'Jun 28', 'Jun 29', 'Jun 30'],
      mobile: [370, 420, 380, 450, 480, 520, 550],
      desktop: [285, 310, 295, 340, 320, 365, 380],
      peak: 550,
      average: 458,
      growth: '+18%'
    }
  };

  const currentData = data[selectedPeriod];
  const maxValue = Math.max(...currentData.mobile, ...currentData.desktop) * 1.1;

  // Generate path for smooth curves
  const generateSmoothPath = (values, height = 300, isArea = false) => {
    const width = 800;
    const padding = 60;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    const points = values.map((value, index) => ({
      x: padding + (index / (values.length - 1)) * chartWidth,
      y: padding + (1 - value / maxValue) * chartHeight
    }));

    if (points.length < 2) return '';

    let path = `M ${points[0].x},${points[0].y}`;
    
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const next = points[i + 1];
      
      const cp1x = prev.x + (curr.x - prev.x) * 0.5;
      const cp1y = prev.y;
      const cp2x = curr.x - (next ? (next.x - curr.x) * 0.3 : 0);
      const cp2y = curr.y;
      
      path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${curr.x},${curr.y}`;
    }
    
    if (isArea) {
      path += ` L ${points[points.length - 1].x},${height - padding} L ${padding},${height - padding} Z`;
    }
    
    return path;
  };

  useEffect(() => {
    setChartVisible(false);
    setAnimationPhase(0);
    
    const timers = [
      setTimeout(() => setAnimationPhase(1), 100),
      setTimeout(() => setAnimationPhase(2), 400),
      setTimeout(() => setAnimationPhase(3), 800),
      setTimeout(() => setChartVisible(true), 1200)
    ];
    
    return () => timers.forEach(clearTimeout);
  }, [selectedPeriod]);

  const periods = [
    { label: 'Last 3 months', size: '2.32 KB', color: 'bg-green-500' },
    { label: 'Last 30 days', size: '1.45 KB', color: 'bg-blue-500' },
    { label: 'Last 7 days', size: '0.89 KB', color: 'bg-orange-500' }
  ];

  const metrics = [
    { label: 'Peak', value: currentData.peak, color: 'border-blue-500', size: '0.25 KB' },
    { label: 'Average', value: currentData.average, color: 'border-orange-500', size: '0.24 KB' },
    { label: 'Growth', value: currentData.growth, color: 'border-green-500', size: '0.16 KB' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 font-light">
      <div className="max-w-7xl mx-auto p-12">
        {/* Header */}
        <div className="mb-16">
          <h1 
            className={`text-6xl font-extralight text-gray-900 mb-4 tracking-tight transition-all duration-1000 ${
              animationPhase >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
            }`}
          >
            Total Visitors
          </h1>
          <p 
            className={`text-xl text-gray-500 font-light transition-all duration-1000 delay-200 ${
              animationPhase >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
            }`}
          >
            Total for the last 3 months
          </p>
        </div>

        {/* Main Chart Container */}
        <div className="relative bg-white rounded-none shadow-sm border-0">
          
          {/* Legend */}
          <div className="absolute top-8 left-8 z-10 flex gap-8">
            <div 
              className={`flex items-center gap-2 transition-all duration-800 delay-300 ${
                animationPhase >= 2 ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'
              }`}
            >
              <div className="w-3 h-3 rounded-full border-2 border-blue-500 bg-blue-50"></div>
              <span className="text-gray-700 font-medium">Mobile</span>
              <span className="text-gray-900 font-semibold">{currentData.mobile[currentData.mobile.length - 1]}</span>
            </div>
            <div 
              className={`flex items-center gap-2 transition-all duration-800 delay-400 ${
                animationPhase >= 2 ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'
              }`}
            >
              <div className="w-3 h-3 rounded-full border-2 border-gray-700 bg-gray-50"></div>
              <span className="text-gray-700 font-medium">Desktop</span>
              <span className="text-gray-900 font-semibold">{currentData.desktop[currentData.desktop.length - 1]}</span>
            </div>
          </div>

          {/* Period Selection */}
          <div className="absolute top-8 right-8 z-10 flex flex-col gap-2">
            {periods.map((period, index) => (
              <div
                key={period.label}
                className={`
                  cursor-pointer transition-all duration-700 hover:scale-105 hover:shadow-lg
                  ${selectedPeriod === period.label 
                    ? 'bg-gray-900 text-white shadow-lg' 
                    : 'bg-white text-gray-700 hover:bg-gray-50 shadow-sm border border-gray-200'
                  }
                  ${animationPhase >= 2 ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'}
                `}
                style={{
                  transitionDelay: `${500 + index * 150}ms`,
                  borderRadius: '8px',
                  padding: '10px 16px',
                  minWidth: '140px'
                }}
                onClick={() => setSelectedPeriod(period.label)}
              >
                <div className="flex items-center justify-between mb-1">
                  <div className={`w-2 h-2 rounded-full ${period.color}`}></div>
                  <span className="text-sm font-medium">{period.size}</span>
                </div>
                <div className="text-xs opacity-80">{period.label}</div>
              </div>
            ))}
          </div>

          {/* Chart Area */}
          <div className="p-8 pt-20 pb-16">
            <div className="h-96 relative">
              <svg className="w-full h-full" viewBox="0 0 800 400">
                {/* Background Grid */}
                <defs>
                  <pattern id="grid" width="40" height="30" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 30" fill="none" stroke="#f8fafc" strokeWidth="1"/>
                  </pattern>
                </defs>
                <rect width="800" height="400" fill="url(#grid)"/>

                {/* Desktop Area */}
                <path
                  d={generateSmoothPath(currentData.desktop, 340, true)}
                  fill="rgba(107, 114, 128, 0.08)"
                  className={`transition-all duration-2000 ${
                    chartVisible ? 'opacity-100' : 'opacity-0'
                  }`}
                  style={{
                    transform: chartVisible ? 'scale(1)' : 'scale(0.95)',
                    transformOrigin: 'center bottom'
                  }}
                />

                {/* Mobile Area */}
                <path
                  d={generateSmoothPath(currentData.mobile, 340, true)}
                  fill="rgba(59, 130, 246, 0.08)"
                  className={`transition-all duration-2000 ${
                    chartVisible ? 'opacity-100' : 'opacity-0'
                  }`}
                  style={{
                    transform: chartVisible ? 'scale(1)' : 'scale(0.95)',
                    transformOrigin: 'center bottom',
                    transitionDelay: '300ms'
                  }}
                />

                {/* Desktop Line */}
                <path
                  d={generateSmoothPath(currentData.desktop, 340)}
                  fill="none"
                  stroke="#374151"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  className={`transition-all duration-2000 ${
                    chartVisible ? 'opacity-100' : 'opacity-0'
                  }`}
                  style={{
                    strokeDasharray: chartVisible ? 'none' : '1000',
                    strokeDashoffset: chartVisible ? '0' : '1000',
                    transitionDelay: '600ms'
                  }}
                />

                {/* Mobile Line */}
                <path
                  d={generateSmoothPath(currentData.mobile, 340)}
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  className={`transition-all duration-2000 ${
                    chartVisible ? 'opacity-100' : 'opacity-0'
                  }`}
                  style={{
                    strokeDasharray: chartVisible ? 'none' : '1000',
                    strokeDashoffset: chartVisible ? '0' : '1000',
                    transitionDelay: '900ms'
                  }}
                />

                {/* Data Points */}
                {currentData.dates.map((date, index) => {
                  const padding = 60;
                  const chartWidth = 800 - padding * 2;
                  const chartHeight = 340 - padding * 2;
                  const x = padding + (index / (currentData.dates.length - 1)) * chartWidth;
                  const mobileY = padding + (1 - currentData.mobile[index] / maxValue) * chartHeight;
                  const desktopY = padding + (1 - currentData.desktop[index] / maxValue) * chartHeight;
                  
                  return (
                    <g key={index}>
                      {/* Desktop Point */}
                      <circle
                        cx={x}
                        cy={desktopY}
                        r={hoveredPoint === index ? 5 : 3}
                        fill="#374151"
                        className={`transition-all duration-500 cursor-pointer ${
                          chartVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-0'
                        }`}
                        style={{
                          transitionDelay: `${1200 + index * 100}ms`
                        }}
                        onMouseEnter={() => setHoveredPoint(index)}
                        onMouseLeave={() => setHoveredPoint(null)}
                      />
                      
                      {/* Mobile Point */}
                      <circle
                        cx={x}
                        cy={mobileY}
                        r={hoveredPoint === index ? 5 : 3}
                        fill="#3b82f6"
                        className={`transition-all duration-500 cursor-pointer ${
                          chartVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-0'
                        }`}
                        style={{
                          transitionDelay: `${1300 + index * 100}ms`
                        }}
                        onMouseEnter={() => setHoveredPoint(index)}
                        onMouseLeave={() => setHoveredPoint(null)}
                      />
                    </g>
                  );
                })}

                {/* X-axis Labels */}
                {currentData.dates.map((date, index) => {
                  const padding = 60;
                  const chartWidth = 800 - padding * 2;
                  const x = padding + (index / (currentData.dates.length - 1)) * chartWidth;
                  
                  return (
                    <text
                      key={index}
                      x={x}
                      y={365}
                      textAnchor="middle"
                      fill="#9ca3af"
                      fontSize="13"
                      fontWeight="400"
                      className={`transition-all duration-500 ${
                        chartVisible ? 'opacity-100' : 'opacity-0'
                      }`}
                      style={{
                        transitionDelay: `${1500 + index * 50}ms`
                      }}
                    >
                      {date}
                    </text>
                  );
                })}

                {/* Hover Tooltip */}
                {hoveredPoint !== null && (
                  <g>
                    <rect
                      x={60 + (hoveredPoint / (currentData.dates.length - 1)) * 680 - 50}
                      y={20}
                      width="100"
                      height="70"
                      fill="white"
                      stroke="#e5e7eb"
                      strokeWidth="1"
                      rx="6"
                      className="drop-shadow-xl"
                    />
                    <text
                      x={60 + (hoveredPoint / (currentData.dates.length - 1)) * 680}
                      y={38}
                      textAnchor="middle"
                      fill="#1f2937"
                      fontSize="12"
                      fontWeight="600"
                    >
                      {currentData.dates[hoveredPoint]}
                    </text>
                    <text
                      x={60 + (hoveredPoint / (currentData.dates.length - 1)) * 680}
                      y={55}
                      textAnchor="middle"
                      fill="#3b82f6"
                      fontSize="11"
                      fontWeight="500"
                    >
                      Mobile: {currentData.mobile[hoveredPoint]}
                    </text>
                    <text
                      x={60 + (hoveredPoint / (currentData.dates.length - 1)) * 680}
                      y={72}
                      textAnchor="middle"
                      fill="#374151"
                      fontSize="11"
                      fontWeight="500"
                    >
                      Desktop: {currentData.desktop[hoveredPoint]}
                    </text>
                  </g>
                )}
              </svg>
            </div>
          </div>

          {/* Bottom Metrics */}
          <div className="px-8 pb-8 flex justify-between items-end">
            <div className="flex gap-4">
              {metrics.map((metric, index) => (
                <div
                  key={metric.label}
                  className={`
                    bg-white rounded-lg shadow-sm border-2 ${metric.color} p-4 min-w-[120px]
                    transition-all duration-800 hover:scale-105 hover:shadow-md
                    ${animationPhase >= 3 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'}
                  `}
                  style={{
                    transitionDelay: `${1800 + index * 200}ms`
                  }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="w-2 h-2 rounded-full bg-current opacity-60"></div>
                    <span className="text-xs text-gray-500 font-medium">{metric.size}</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
                  <div className="text-sm text-gray-600 font-medium">{metric.label}</div>
                </div>
              ))}
            </div>

            {/* Bundle Size */}
            <div 
              className={`bg-gray-900 text-white px-6 py-3 rounded-lg transition-all duration-800 ${
                animationPhase >= 3 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
              }`}
              style={{ transitionDelay: '2400ms' }}
            >
              <div className="flex items-center gap-3">
                <span className="text-gray-300 font-medium">Bundle size</span>
                <span className="font-bold">{currentData.peak + currentData.average} visitors</span>
              </div>
              <div className="w-48 h-2 bg-gray-700 rounded-full mt-2 overflow-hidden">
                <div 
                  className={`h-full bg-gradient-to-r from-blue-500 via-green-500 to-orange-500 rounded-full transition-all duration-2000 ${
                    chartVisible ? 'w-full' : 'w-0'
                  }`}
                  style={{ transitionDelay: '2800ms' }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CleanWireframeAnalytics;

demo.tsx
import CleanWireframeAnalytics from "@/components/ui/line-graph-statistics";

export default function DemoOne() {
  return <CleanWireframeAnalytics />;
}

```

Implementation Guidelines
 1. Analyze the component structure and identify all required dependencies
 2. Review the component's argumens and state
 3. Identify any required context providers or hooks and install them
 4. Questions to Ask
 - What data/props will be passed to this component?
 - Are there any specific state management requirements?
 - Are there any required assets (images, icons, etc.)?
 - What is the expected responsive behavior?
 - What is the best place to use this component in the app?

Steps to integrate
 0. Copy paste all the code above in the correct directories
 1. Install external dependencies
 2. Fill image assets with Unsplash stock images you know exist
 3. Use lucide-react icons for svgs or logos if component requires them

other idea of the graph

You are given a task to integrate an existing React component in the codebase

The codebase should support:
- shadcn project structure  
- Tailwind CSS
- Typescript

If it doesn't, provide instructions on how to setup project via shadcn CLI, install Tailwind or Typescript.

Determine the default path for components and styles. 
If default path for components is not /components/ui, provide instructions on why it's important to create this folder
Copy-paste this component to /components/ui folder:
```tsx
horizontal-bar-chart.tsx
'use client';

import React from 'react';
import {
  BarChart,
  LinearYAxis,
  LinearYAxisTickSeries,
  LinearYAxisTickLabel,
  LinearXAxis,
  LinearXAxisTickSeries,
  BarSeries,
  Bar,
  GridlineSeries,
  Gridline,
} from 'reaviz';
import { motion } from 'framer-motion';

// Data Definitions and Validation
interface ChartCategoryData {
  key: string;
  data: number | null; // Allow null for raw data before validation
}

const categoryDataRaw: ChartCategoryData[] = [
  { key: 'Brute Force', data: 100 },
  { key: 'Web Attack', data: 80 },
  { key: 'Malware', data: 120 },
  { key: 'Phishing', data: 90 },
];

// Validate and prepare chart data
const validatedCategoryData = categoryDataRaw.map(item => ({
  ...item,
  data: (typeof item.data === 'number' && !isNaN(item.data)) ? item.data : 0,
}));

const chartColors = ['#9152EE', '#40D3F4', '#40E5D1', '#4C86FF'];

interface MetricItem {
  id: string;
  iconSvg: JSX.Element;
  label: string;
  value: string;
  trendIconSvg: JSX.Element;
  delay: number;
}

const metrics: MetricItem[] = [
  {
    id: 'mttRespond',
    iconSvg: (
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path d="M9.92844 1.25411C9.32947 1.25895 8.73263 1.49041 8.28293 1.94747L1.92062 8.41475C1.02123 9.32885 1.03336 10.8178 1.94748 11.7172L8.41476 18.0795C9.32886 18.9789 10.8178 18.9667 11.7172 18.0526L18.0795 11.5861C18.0798 11.5859 18.08 11.5856 18.0803 11.5853C18.979 10.6708 18.9667 9.18232 18.0526 8.28291L11.5853 1.92061C11.1283 1.47091 10.5274 1.24926 9.92844 1.25411ZM9.93901 2.49597C10.2155 2.49373 10.4926 2.59892 10.7089 2.81172L17.1762 9.17403C17.6087 9.59962 17.6139 10.2767 17.1884 10.7097L10.8261 17.1761C10.4005 17.6087 9.72379 17.614 9.29123 17.1884L2.82394 10.826C2.39139 10.4005 2.38613 9.72378 2.81174 9.29121L9.17404 2.82393C9.38684 2.60765 9.66256 2.4982 9.93901 2.49597ZM9.99028 5.40775C9.82481 5.41034 9.66711 5.47845 9.55178 5.59714C9.43645 5.71583 9.37289 5.87541 9.37505 6.04089V11.0409C9.37388 11.1237 9.38918 11.2059 9.42006 11.2828C9.45095 11.3596 9.4968 11.4296 9.55495 11.4886C9.6131 11.5476 9.6824 11.5944 9.75881 11.6264C9.83522 11.6583 9.91722 11.6748 10 11.6748C10.0829 11.6748 10.1649 11.6583 10.2413 11.6264C10.3177 11.5944 10.387 11.5476 10.4451 11.4886C10.5033 11.4296 10.5492 11.3596 10.58 11.2828C10.6109 11.2059 10.6262 11.1237 10.625 11.0409V6.04089C10.6261 5.95731 10.6105 5.87435 10.5789 5.79694C10.5474 5.71952 10.5006 5.64922 10.4415 5.59019C10.3823 5.53115 10.3119 5.48459 10.2344 5.45326C10.1569 5.42192 10.0739 5.40645 9.99028 5.40775ZM10 12.9159C9.77904 12.9159 9.56707 13.0037 9.41079 13.16C9.25451 13.3162 9.16672 13.5282 9.16672 13.7492C9.16672 13.9702 9.25451 14.1822 9.41079 14.3385C9.56707 14.4948 9.77904 14.5826 10 14.5826C10.2211 14.5826 10.433 14.4948 10.5893 14.3385C10.7456 14.1822 10.8334 13.9702 10.8334 13.7492C10.8334 13.5282 10.7456 13.3162 10.5893 13.16C10.433 13.0037 10.2211 12.9159 10 12.9159Z" fill="#E84045" />
      </svg>
    ),
    label: 'Mean Time to Respond',
    value: '6 Hours',
    trendIconSvg: (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="28" height="28" rx="14" fill="#E84045" fillOpacity="0.4" />
        <path d="M9.50134 12.6111L14.0013 8.16663M14.0013 8.16663L18.5013 12.6111M14.0013 8.16663L14.0013 19.8333" stroke="#F08083" strokeWidth="2" strokeLinecap="square" />
      </svg>
    ),
    delay: 0,
  },
  {
    id: 'incidentResponseTime',
    iconSvg: (
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path d="M10.0001 1.66663C5.40511 1.66663 1.66675 5.40499 1.66675 9.99996C1.66675 14.5949 5.40511 18.3333 10.0001 18.3333C14.5951 18.3333 18.3334 14.5949 18.3334 9.99996C18.3334 5.40499 14.5951 1.66663 10.0001 1.66663ZM10.0001 2.91663C13.9195 2.91663 17.0834 6.08054 17.0834 9.99996C17.0834 13.9194 13.9195 17.0833 10.0001 17.0833C6.08066 17.0833 2.91675 13.9194 2.91675 9.99996C2.91675 6.08054 6.08066 2.91663 10.0001 2.91663ZM9.99032 5.82434C9.8247 5.82693 9.66688 5.89515 9.55152 6.01401C9.43616 6.13288 9.37271 6.29267 9.37508 6.45829V10.625C9.37391 10.7078 9.38921 10.79 9.42009 10.8669C9.45098 10.9437 9.49683 11.0137 9.55498 11.0726C9.61313 11.1316 9.68243 11.1785 9.75884 11.2104C9.83525 11.2424 9.91725 11.2589 10.0001 11.2589C10.0829 11.2589 10.1649 11.2424 10.2413 11.2104C10.3177 11.1785 10.387 11.1316 10.4452 11.0726C10.5033 11.0137 10.5492 10.9437 10.5801 10.8669C10.611 10.79 10.6263 10.7078 10.6251 10.625V6.45829C10.6263 6.37464 10.6107 6.2916 10.5792 6.21409C10.5477 6.13658 10.501 6.06618 10.4418 6.00706C10.3826 5.94794 10.3121 5.9013 10.2346 5.86992C10.157 5.83853 10.074 5.82303 9.99032 5.82434ZM10.0001 12.5C9.77907 12.5 9.56711 12.5878 9.41083 12.744C9.25455 12.9003 9.16675 13.1123 9.16675 13.3333C9.16675 13.5543 9.25455 13.7663 9.41083 13.9225C9.56711 14.0788 9.77907 14.1666 10.0001 14.1666C10.2211 14.1666 10.4331 14.0788 10.5893 13.9225C10.7456 13.7663 10.8334 13.5543 10.8334 13.3333C10.8334 13.1123 10.7456 12.9003 10.5893 12.744C10.4331 12.5878 10.2211 12.5 10.0001 12.5Z" fill="#E84045" />
      </svg>
    ),
    label: 'Incident Response Time',
    value: '4 Hours',
    trendIconSvg: (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="28" height="28" rx="14" fill="#E84045" fillOpacity="0.4" />
        <path d="M9.50134 12.6111L14.0013 8.16663M14.0013 8.16663L18.5013 12.6111M14.0013 8.16663L14.0013 19.8333" stroke="#F08083" strokeWidth="2" strokeLinecap="square" />
      </svg>
    ),
    delay: 0.05,
  },
  {
    id: 'incidentEscalationRate',
    iconSvg: (
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path d="M10.0001 2.10535C9.35241 2.10535 8.70472 2.42118 8.35459 3.05343L1.9044 14.7063C1.22414 15.9354 2.14514 17.5 3.5499 17.5H16.4511C17.8559 17.5 18.7769 15.9354 18.0966 14.7063L11.6456 3.05343C11.2955 2.42118 10.6478 2.10535 10.0001 2.10535ZM10.0001 3.31222C10.212 3.31222 10.4237 3.42739 10.5519 3.65889L17.0029 15.3117C17.2501 15.7585 16.9605 16.25 16.4511 16.25H3.5499C3.04051 16.25 2.7509 15.7585 2.99815 15.3117L9.44834 3.65889C9.57655 3.42739 9.78821 3.31222 10.0001 3.31222ZM9.99033 6.65776C9.82472 6.66034 9.6669 6.72856 9.55154 6.84743C9.43618 6.96629 9.37272 7.12609 9.3751 7.29171V11.4584C9.37393 11.5412 9.38923 11.6234 9.42011 11.7003C9.451 11.7771 9.49685 11.8471 9.555 11.9061C9.61315 11.965 9.68245 12.0119 9.75886 12.0438C9.83527 12.0758 9.91727 12.0923 10.0001 12.0923C10.0829 12.0923 10.1649 12.0758 10.2413 12.0438C10.3178 12.0119 10.387 11.965 10.4452 11.9061C10.5034 11.8471 10.5492 11.7771 10.5801 11.7003C10.611 11.6234 10.6263 11.5412 10.6251 11.4584V7.29171C10.6263 7.20806 10.6107 7.12501 10.5792 7.0475C10.5477 6.96999 10.501 6.89959 10.4418 6.84047C10.3826 6.78135 10.3121 6.73472 10.2346 6.70333C10.157 6.67195 10.074 6.65645 9.99033 6.65776ZM10.0001 13.3334C9.77909 13.3334 9.56712 13.4212 9.41084 13.5775C9.25456 13.7337 9.16677 13.9457 9.16677 14.1667C9.16677 14.3877 9.25456 14.5997 9.41084 14.756C9.56712 14.9122 9.77909 15 10.0001 15C10.2211 15 10.4331 14.9122 10.5894 14.756C10.7456 14.5997 10.8334 14.3877 10.8334 14.1667C10.8334 13.9457 10.7456 13.7337 10.5894 13.5775C10.4331 13.4212 10.2211 13.3334 10.0001 13.3334Z" fill="#E84045" />
      </svg>
    ),
    label: 'Incident Escalation Rate',
    value: '10%',
    trendIconSvg: (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="28" height="28" rx="14" fill="#40E5D1" fillOpacity="0.4" />
        <path d="M18.4987 15.3889L13.9987 19.8334M13.9987 19.8334L9.49866 15.3889M13.9987 19.8334V8.16671" stroke="#40E5D1" strokeWidth="2" strokeLinecap="square" />
      </svg>
    ),
    delay: 0.1,
  },
];

interface IncidentSummaryCardProps {
  // Props can be added here for customization
}

function IncidentSummaryCard({}: IncidentSummaryCardProps): JSX.Element {
  return (
    <div className="flex flex-col pt-4 pb-4 bg-white dark:bg-black rounded-3xl shadow-[11px_21px_3px_rgba(0,0,0,0.06),14px_27px_7px_rgba(0,0,0,0.10),19px_38px_14px_rgba(0,0,0,0.13),27px_54px_27px_rgba(0,0,0,0.16),39px_78px_50px_rgba(0,0,0,0.20),55px_110px_86px_rgba(0,0,0,0.26)] w-[375px] h-[560px] overflow-hidden transition-colors duration-300">
      <h3 className="text-3xl text-left p-7 pt-6 pb-8 font-bold text-neutral-800 dark:text-white">
        Incident Report
      </h3>
      <div className="flex-grow px-4 h-[200px]"> {/* Explicit height for chart container */}
        <BarChart
          id="horizontal-incident-summary-chart"
          height={200} // Explicit height for the chart itself
          data={validatedCategoryData}
          yAxis={
            <LinearYAxis
              type="category"
              tickSeries={
                <LinearYAxisTickSeries
                  label={
                    <LinearYAxisTickLabel
                      format={(text: string) => (text.length > 5 ? `${text.slice(0, 5)}...` : text)}
                      fill="#9A9AAF" // Dark/light theme compatible color
                    />
                  }
                />
              }
            />
          }
          xAxis={
            <LinearXAxis
              type="value"
              axisLine={null}
              tickSeries={
                <LinearXAxisTickSeries
                  label={null}
                  line={null}
                  tickSize={10} // Optimized tickSize
                />
              }
            />
          }
          series={
            <BarSeries
              layout="horizontal"
              bar={
                <Bar
                  glow={{
                    blur: 20,
                    opacity: 0.5,
                  }}
                  gradient={null}
                />
              }
              colorScheme={chartColors}
              padding={0.2}
            />
          }
          gridlines={
            <GridlineSeries
              line={<Gridline strokeColor="#7E7E8F75" />} // Specific color for gridlines
            />
          }
        />
      </div>
      <div className="flex flex-col pl-8 pr-8 pt-8 font-mono divide-y divide-neutral-200 dark:divide-[#262631]">
        {metrics.map(metric => (
          <motion.div
            key={metric.id}
            initial={{
              opacity: 0,
              y: 20,
            }}
            animate={{
              opacity: 1,
              y: 0,
            }}
            transition={{
              delay: metric.delay,
            }}
            className="flex w-full pb-4 pt-4 items-center gap-2"
          >
            <div className="flex flex-row gap-2 items-center text-base w-1/2 text-neutral-500 dark:text-[#9A9AAF]">
              {metric.iconSvg}
              <span className="truncate" title={metric.label}>
                {metric.label}
              </span>
            </div>
            <div className="flex gap-2 w-1/2 justify-end items-center">
              <span className="font-semibold text-xl text-neutral-800 dark:text-white">{metric.value}</span>
              {metric.trendIconSvg}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}

export default IncidentSummaryCard;

demo.tsx
import React from 'react';
import IncidentSummaryCard from '@/components/ui/horizontal-bar-chart';

function IncidentSummaryCardDemoPage() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-neutral-900 p-4 transition-colors duration-300">
      <IncidentSummaryCard />
    </div>
  );
}

export default IncidentSummaryCardDemoPage;
```

Install NPM dependencies:
```bash
reaviz, framer-motion
```

Implementation Guidelines
 1. Analyze the component structure and identify all required dependencies
 2. Review the component's argumens and state
 3. Identify any required context providers or hooks and install them
 4. Questions to Ask
 - What data/props will be passed to this component?
 - Are there any specific state management requirements?
 - Are there any required assets (images, icons, etc.)?
 - What is the expected responsive behavior?
 - What is the best place to use this component in the app?

Steps to integrate
 0. Copy paste all the code above in the correct directories
 1. Install external dependencies
 2. Fill image assets with Unsplash stock images you know exist
 3. Use lucide-react icons for svgs or logos if component requires them
