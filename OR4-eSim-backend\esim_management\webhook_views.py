# -*- coding: utf-8 -*-
"""
Professional Webhook Views for TraveRoam Integration
Complete webhook handling for real-time eSIM updates
"""

import hashlib
import hmac
import json
import logging

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from .models import ESIM, TraveRoamWebhook
from .services import ESIMWorkflowService

logger = logging.getLogger(__name__)


def verify_webhook_signature(payload, signature, secret):
    """Verify webhook signature for security"""
    try:
        if not secret:
            logger.warning(
                "No webhook secret configured, skipping signature verification"
            )
            return True

        # Create expected signature
        expected_signature = hmac.new(
            secret.encode("utf-8"), payload.encode("utf-8"), hashlib.sha256
        ).hexdigest()

        # Compare signatures
        return hmac.compare_digest(signature, expected_signature)
    except Exception as e:
        logger.error(f"Error verifying webhook signature: {str(e)}")
        return False


@api_view(["POST"])
@permission_classes([AllowAny])
@csrf_exempt
@require_http_methods(["POST"])
def traveroam_webhook(request):
    """
    Professional webhook handler for TraveRoam real-time updates

    Supported events:
    - activation: eSIM activated by user
    - usage: Data usage updates
    - expiry: eSIM expired
    - error: Error notifications
    - bundle_assigned: New bundle assigned
    - bundle_revoked: Bundle revoked
    - status_change: eSIM status change
    """
    try:
        # Get webhook signature for validation
        signature = request.headers.get("X-TraveRoam-Signature", "")
        webhook_secret = getattr(settings, "TRAVEROAM_WEBHOOK_SECRET", "")

        # Get raw payload for signature verification
        raw_payload = request.body.decode("utf-8")

        # Verify signature if secret is configured
        if webhook_secret and not verify_webhook_signature(
            raw_payload, signature, webhook_secret
        ):
            logger.warning(f"Invalid webhook signature: {signature}")
            return Response(
                {"error": "Invalid signature"}, status=status.HTTP_401_UNAUTHORIZED
            )

        # Parse webhook data
        webhook_data = request.data

        # Log webhook receipt
        event_type = webhook_data.get("event_type", "unknown")
        event_id = webhook_data.get("event_id", "unknown")
        timestamp = webhook_data.get("timestamp", timezone.now().isoformat())

        logger.info(
            f"📨 Received TraveRoam webhook: {event_type} (ID: {event_id}) at {timestamp}"
        )

        # Check if webhook already processed (idempotency)
        existing_webhook = TraveRoamWebhook.objects.filter(
            event_id=event_id, event_type=event_type
        ).first()

        if existing_webhook:
            logger.info(f"🔄 Webhook already processed: {event_id}")
            return Response(
                {
                    "status": "already_processed",
                    "event_id": event_id,
                    "processed_at": (
                        existing_webhook.processed_at.isoformat()
                        if existing_webhook.processed_at
                        else None
                    ),
                },
                status=status.HTTP_200_OK,
            )

        # Process webhook using service
        success = ESIMWorkflowService.process_traveroam_webhook(webhook_data)

        if success:
            logger.info(
                f"✅ Successfully processed webhook: {event_type} (ID: {event_id})"
            )

            # Update cache statistics
            cache_key = f"webhook_stats_{event_type}"
            stats = cache.get(cache_key, {"count": 0, "last_processed": None})
            stats["count"] += 1
            stats["last_processed"] = timezone.now().isoformat()
            cache.set(cache_key, stats, 3600)

            return Response(
                {
                    "status": "success",
                    "event_type": event_type,
                    "event_id": event_id,
                    "processed_at": timezone.now().isoformat(),
                    "message": f"Webhook {event_type} processed successfully",
                },
                status=status.HTTP_200_OK,
            )
        else:
            logger.error(f"❌ Failed to process webhook: {event_type} (ID: {event_id})")
            return Response(
                {
                    "status": "error",
                    "event_type": event_type,
                    "event_id": event_id,
                    "error": "Processing failed",
                    "timestamp": timezone.now().isoformat(),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    except json.JSONDecodeError as e:
        logger.error(f"❌ Invalid JSON in webhook payload: {str(e)}")
        return Response(
            {
                "error": "Invalid JSON payload",
                "details": str(e),
                "timestamp": timezone.now().isoformat(),
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logger.error(f"❌ Error processing TraveRoam webhook: {str(e)}")
        return Response(
            {
                "error": "Internal server error",
                "details": str(e),
                "timestamp": timezone.now().isoformat(),
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([AllowAny])
def webhook_test(request):
    """
    Test endpoint for webhook functionality
    """
    return Response(
        {
            "status": "success",
            "message": "Webhook endpoint is working",
            "timestamp": timezone.now().isoformat(),
            "supported_events": [
                "activation",
                "usage",
                "expiry",
                "error",
                "bundle_assigned",
                "bundle_revoked",
                "status_change",
            ],
            "webhook_url": f"{getattr(settings, 'BASE_URL', 'http://localhost:8000')}/api/v1/esim/webhooks/traveroam/",
            "documentation": "Configure this URL in TraveRoam webhook settings",
            "security": {
                "signature_verification": bool(
                    getattr(settings, "TRAVEROAM_WEBHOOK_SECRET", "")
                ),
                "csrf_exempt": True,
                "allowed_methods": ["POST"],
            },
        },
        status=status.HTTP_200_OK,
    )


@api_view(["GET"])
@permission_classes([AllowAny])
def webhook_status(request):
    """
    Get comprehensive webhook processing status and statistics
    """
    try:
        # Get recent webhooks
        recent_webhooks = TraveRoamWebhook.objects.order_by("-created_at")[:20]

        # Get statistics
        total_webhooks = TraveRoamWebhook.objects.count()
        successful_webhooks = TraveRoamWebhook.objects.filter(processed=True).count()
        failed_webhooks = total_webhooks - successful_webhooks

        # Get event type breakdown
        event_types = {}
        for webhook in TraveRoamWebhook.objects.values("webhook_type").distinct():
            event_type = webhook["webhook_type"]
            count = TraveRoamWebhook.objects.filter(webhook_type=event_type).count()
            event_types[event_type] = count

        # Get cache statistics
        cache_stats = {}
        for event_type in [
            "activation",
            "usage",
            "expiry",
            "error",
            "bundle_assigned",
            "bundle_revoked",
        ]:
            stats = cache.get(f"webhook_stats_{event_type}", {})
            cache_stats[event_type] = stats

        return Response(
            {
                "status": "success",
                "webhook_endpoint": f"{getattr(settings, 'BASE_URL', 'http://localhost:8000')}/api/v1/esim/webhooks/traveroam/",
                "statistics": {
                    "total_webhooks": total_webhooks,
                    "successful_webhooks": successful_webhooks,
                    "failed_webhooks": failed_webhooks,
                    "success_rate": (
                        (successful_webhooks / total_webhooks * 100)
                        if total_webhooks > 0
                        else 0
                    ),
                    "last_webhook": (
                        recent_webhooks[0].created_at.isoformat()
                        if recent_webhooks
                        else None
                    ),
                },
                "event_types": event_types,
                "cache_statistics": cache_stats,
                "recent_webhooks": [
                    {
                        "id": webhook.id,
                        "event_type": webhook.webhook_type,
                        "event_id": webhook.payload.get("event_id", "unknown"),
                        "received_at": webhook.created_at.isoformat(),
                        "processed": webhook.processed,
                        "processed_at": (
                            webhook.processed_at.isoformat()
                            if webhook.processed_at
                            else None
                        ),
                        "processing_time": (
                            (webhook.processed_at - webhook.created_at).total_seconds()
                            if webhook.processed_at
                            else None
                        ),
                    }
                    for webhook in recent_webhooks
                ],
                "system_info": {
                    "webhook_secret_configured": bool(
                        getattr(settings, "TRAVEROAM_WEBHOOK_SECRET", "")
                    ),
                    "cache_enabled": True,
                    "timestamp": timezone.now().isoformat(),
                },
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"❌ Error getting webhook status: {str(e)}")
        return Response(
            {
                "error": "Failed to get webhook status",
                "details": str(e),
                "timestamp": timezone.now().isoformat(),
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def webhook_simulate(request):
    """
    Simulate webhook for testing purposes
    """
    try:
        # Get simulation data
        event_type = request.data.get("event_type", "activation")
        iccid = request.data.get("iccid", "test-iccid")

        # Create simulated webhook data
        webhook_data = {
            "event_type": event_type,
            "event_id": f"sim-{timezone.now().timestamp()}",
            "timestamp": timezone.now().isoformat(),
            "iccid": iccid,
            "data_used": request.data.get("data_used", 0),
            "data_remaining": request.data.get("data_remaining", 1000),
            "location": request.data.get("location", "Unknown"),
            "status": request.data.get("status", "active"),
            "bundle_name": request.data.get("bundle_name", "test-bundle"),
            "customer_ref": request.data.get("customer_ref", "test-customer"),
        }

        # Process simulated webhook
        success = ESIMWorkflowService.process_traveroam_webhook(webhook_data)

        if success:
            return Response(
                {
                    "status": "success",
                    "message": f"Simulated {event_type} webhook processed successfully",
                    "webhook_data": webhook_data,
                    "timestamp": timezone.now().isoformat(),
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "error",
                    "message": f"Failed to process simulated {event_type} webhook",
                    "webhook_data": webhook_data,
                    "timestamp": timezone.now().isoformat(),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    except Exception as e:
        logger.error(f"❌ Error simulating webhook: {str(e)}")
        return Response(
            {
                "error": "Failed to simulate webhook",
                "details": str(e),
                "timestamp": timezone.now().isoformat(),
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([AllowAny])
def webhook_health(request):
    """
    Health check endpoint for webhook system
    """
    try:
        # Check database connectivity
        webhook_count = TraveRoamWebhook.objects.count()

        # Check cache connectivity
        cache_test_key = "webhook_health_test"
        cache.set(cache_test_key, "test", 60)
        cache_test = cache.get(cache_test_key) == "test"

        # Check settings
        webhook_secret_configured = bool(
            getattr(settings, "TRAVEROAM_WEBHOOK_SECRET", "")
        )
        base_url_configured = bool(getattr(settings, "BASE_URL", ""))

        health_status = (
            "healthy"
            if all([cache_test, webhook_secret_configured, base_url_configured])
            else "degraded"
        )

        return Response(
            {
                "status": "success",
                "health": health_status,
                "checks": {
                    "database": True,
                    "cache": cache_test,
                    "webhook_secret_configured": webhook_secret_configured,
                    "base_url_configured": base_url_configured,
                },
                "statistics": {
                    "total_webhooks_processed": webhook_count,
                    "last_webhook": (
                        TraveRoamWebhook.objects.order_by("-created_at")
                        .first()
                        .created_at.isoformat()
                        if TraveRoamWebhook.objects.exists()
                        else None
                    ),
                },
                "timestamp": timezone.now().isoformat(),
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"❌ Health check failed: {str(e)}")
        return Response(
            {
                "status": "error",
                "health": "unhealthy",
                "error": str(e),
                "timestamp": timezone.now().isoformat(),
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def webhook_retry(request):
    """
    Retry processing failed webhooks
    """
    try:
        webhook_id = request.data.get("webhook_id")

        if not webhook_id:
            return Response(
                {"error": "webhook_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Get the webhook
        webhook = TraveRoamWebhook.objects.get(id=webhook_id)

        if webhook.processed:
            return Response(
                {"error": "Webhook already processed"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Retry processing
        success = ESIMWorkflowService.process_traveroam_webhook(webhook.payload)

        if success:
            webhook.processed = True
            webhook.processed_at = timezone.now()
            webhook.save()

            return Response(
                {
                    "status": "success",
                    "message": "Webhook retry successful",
                    "webhook_id": webhook_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "error",
                    "message": "Webhook retry failed",
                    "webhook_id": webhook_id,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    except TraveRoamWebhook.DoesNotExist:
        return Response(
            {"error": "Webhook not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"❌ Error retrying webhook: {str(e)}")
        return Response(
            {"error": "Failed to retry webhook", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
