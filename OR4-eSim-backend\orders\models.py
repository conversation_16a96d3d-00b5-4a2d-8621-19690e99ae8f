from django.conf import settings
from django.db import models
from simple_history.models import HistoricalRecords


class Order(models.Model):
    """
    Order model for SIM and eSIM orders
    """

    class OrderStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        CONFIRMED = "confirmed", "Confirmed"
        PROCESSING = "processing", "Processing"
        DISPATCHED = "dispatched", "Dispatched"
        DELIVERED = "delivered", "Delivered"
        ACTIVATED = "activated", "Activated"
        COMPLETED = "completed", "Completed"
        CANCELLED = "cancelled", "Cancelled"
        REFUNDED = "refunded", "Refunded"

    class OrderType(models.TextChoices):
        SIM = "sim", "SIM Card"
        ESIM = "esim", "eSIM"

    class OrderSource(models.TextChoices):
        APP = "app", "Mobile App"
        RESELLER = "reseller", "Reseller"
        ADMIN = "admin", "Admin"

    # Order details
    order_number = models.Char<PERSON>ield(max_length=50, unique=True)
    order_type = models.CharField(max_length=10, choices=OrderType.choices)
    order_source = models.CharField(max_length=20, choices=OrderSource.choices)
    status = models.CharField(
        max_length=20, choices=OrderStatus.choices, default=OrderStatus.PENDING
    )

    # Relationships
    reseller = models.ForeignKey(
        "resellers.Reseller",
        on_delete=models.CASCADE,
        related_name="orders",
        null=True,
        blank=True,
    )
    client = models.ForeignKey(
        "clients.Client",
        on_delete=models.CASCADE,
        related_name="orders",
        null=True,
        blank=True,
    )

    # eSIM Integration
    esim_id = models.PositiveIntegerField(
        null=True, blank=True, help_text="Link to eSIM record"
    )
    traveroam_order_reference = models.CharField(
        max_length=100, blank=True, null=True, help_text="TraveRoam order reference"
    )

    # Product details
    product_name = models.CharField(max_length=200)
    product_description = models.TextField(blank=True, null=True)
    quantity = models.PositiveIntegerField(default=1)

    # Pricing
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    subtotal = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    delivery_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)

    # Delivery information
    delivery_address = models.TextField(blank=True, null=True)
    delivery_city = models.CharField(max_length=100, blank=True, null=True)
    delivery_country = models.CharField(max_length=100, blank=True, null=True)
    delivery_phone = models.CharField(max_length=15, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    dispatched_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "orders"
        ordering = ["-created_at"]

    def __str__(self):
        return f"Order {self.order_number} - {self.product_name}"

    @property
    def customer(self):
        return self.client

    @property
    def is_paid(self):
        from payments.models import Payment

        return Payment.objects.filter(order=self, status="completed").exists()


class OrderItem(models.Model):
    """
    Individual items in an order
    """

    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name="items")
    esim = models.ForeignKey(
        "esim_management.ESIM",
        on_delete=models.CASCADE,
        related_name="order_items",
        null=True,
        blank=True,
    )

    # Item details
    product_name = models.CharField(max_length=200)
    product_description = models.TextField(blank=True, null=True)
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "order_items"

    def __str__(self):
        return f"{self.product_name} x {self.quantity}"


class OrderStatusHistory(models.Model):
    """
    Track order status changes
    """

    order = models.ForeignKey(
        Order, on_delete=models.CASCADE, related_name="status_history"
    )
    old_status = models.CharField(max_length=20, choices=Order.OrderStatus.choices)
    new_status = models.CharField(max_length=20, choices=Order.OrderStatus.choices)
    changed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "order_status_history"
        ordering = ["-created_at"]

    def __str__(self):
        return (
            f"Order {self.order.order_number} - {self.old_status} to {self.new_status}"
        )


class DeliveryTracking(models.Model):
    """
    Track delivery status and updates
    """

    class TrackingStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        PICKED_UP = "picked_up", "Picked Up"
        IN_TRANSIT = "in_transit", "In Transit"
        OUT_FOR_DELIVERY = "out_for_delivery", "Out for Delivery"
        DELIVERED = "delivered", "Delivered"
        FAILED = "failed", "Failed"

    order = models.OneToOneField(
        Order, on_delete=models.CASCADE, related_name="delivery_tracking"
    )
    tracking_number = models.CharField(max_length=100, blank=True, null=True)
    courier_name = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(
        max_length=20, choices=TrackingStatus.choices, default=TrackingStatus.PENDING
    )

    # Tracking details
    current_location = models.CharField(max_length=200, blank=True, null=True)
    estimated_delivery = models.DateTimeField(null=True, blank=True)
    actual_delivery = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "delivery_tracking"

    def __str__(self):
        return f"Tracking for Order {self.order.order_number}"


class OrderNotification(models.Model):
    """
    Track notifications sent for orders
    """

    class NotificationType(models.TextChoices):
        ORDER_CONFIRMED = "order_confirmed", "Order Confirmed"
        ORDER_DISPATCHED = "order_dispatched", "Order Dispatched"
        ORDER_DELIVERED = "order_delivered", "Order Delivered"
        ORDER_ACTIVATED = "order_activated", "Order Activated"
        ORDER_CANCELLED = "order_cancelled", "Order Cancelled"

    class NotificationMethod(models.TextChoices):
        EMAIL = "email", "Email"
        SMS = "sms", "SMS"
        PUSH = "push", "Push Notification"
        IN_APP = "in_app", "In App"

    order = models.ForeignKey(
        Order, on_delete=models.CASCADE, related_name="notifications"
    )
    notification_type = models.CharField(
        max_length=20, choices=NotificationType.choices
    )
    notification_method = models.CharField(
        max_length=20, choices=NotificationMethod.choices
    )
    recipient = models.CharField(max_length=200)  # email or phone number
    message = models.TextField()
    sent_at = models.DateTimeField(auto_now_add=True)
    delivered = models.BooleanField(default=False)
    delivered_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "order_notifications"
        ordering = ["-sent_at"]

    def __str__(self):
        return f"{self.notification_type} for Order {self.order.order_number}"
