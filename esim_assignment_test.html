<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eSIM Assignment Test Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .workflow-container {
            display: flex;
            min-height: 600px;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 20px;
        }

        .workflow-steps {
            list-style: none;
        }

        .workflow-step {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .workflow-step.active {
            background: #e3f2fd;
            border-left-color: #2196f3;
            color: #1976d2;
        }

        .workflow-step.completed {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }

        .workflow-step:hover {
            background: #f5f5f5;
        }

        .main-content {
            flex: 1;
            padding: 30px;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #2196f3;
            color: white;
        }

        .btn-primary:hover {
            background: #1976d2;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #4caf50;
            color: white;
        }

        .btn-success:hover {
            background: #2e7d32;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .card-header {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #f8f9fa;
            padding-bottom: 10px;
        }

        .client-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .client-item {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .client-item:hover {
            background: #f8f9fa;
            border-color: #2196f3;
        }

        .client-item.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .client-name {
            font-weight: 600;
            color: #333;
        }

        .client-details {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .plan-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .plan-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .plan-card:hover {
            border-color: #2196f3;
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .plan-card.selected {
            border-color: #4caf50;
            background: #f1f8e9;
        }

        .plan-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .plan-details {
            color: #666;
            margin-bottom: 15px;
        }

        .plan-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2196f3;
        }

        .esim-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-provisioned {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-assigned {
            background: #d4edda;
            color: #155724;
        }

        .status-activated {
            background: #d1e7dd;
            color: #0f5132;
        }

        .qr-code-container {
            text-align: center;
            margin: 20px 0;
        }

        .qr-code {
            max-width: 200px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 10px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196f3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2196f3, #4caf50);
            transition: width 0.3s ease;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .hidden {
            display: none;
        }

        /* Stripe payment buttons */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #635bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #4f46e5;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(99, 91, 255, 0.3);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .btn-lg {
            padding: 16px 32px;
            font-size: 18px;
        }

        .mt-2 {
            margin-top: 8px;
        }

        .mt-3 {
            margin-top: 16px;
        }

        /* New workflow styles for console script implementation */
        .user-form .form-group {
            margin-bottom: 20px;
        }

        .user-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .country-info {
            margin-top: 10px;
        }

        .bundle-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bundle-card:hover {
            border-color: #2196f3;
            background: #f8f9fa;
        }

        .bundle-card.selected {
            border-color: #4caf50;
            background: #e8f5e8;
        }

        .bundle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .bundle-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2196f3;
        }

        .bundle-details p {
            margin: 5px 0;
        }

        .pricing-details table {
            width: 100%;
            margin-top: 10px;
        }

        .pricing-details td {
            padding: 5px 0;
        }

        .pricing-details .total {
            border-top: 2px solid #ddd;
            font-size: 1.1rem;
        }

        .qr-code-image {
            max-width: 300px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }

        .qr-instructions {
            color: #666;
            font-style: italic;
        }

        .complete-details table {
            width: 100%;
            margin-top: 10px;
        }

        .complete-details td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }

        .complete-details td:first-child {
            font-weight: bold;
            width: 30%;
        }

        .success-message {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            border-radius: 10px;
            margin: 20px 0;
        }

        /* Step loading overlay styles */
        .step-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            z-index: 1000;
            border-radius: 10px;
        }

        .loading-overlay {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2196f3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        .loading-message {
            font-size: 1.1rem;
            color: #2196f3;
            font-weight: 500;
        }

        .step-content {
            position: relative;
        }

        .auto-info {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 3px solid #6c757d;
            border-radius: 4px;
        }

        .auto-info .text-muted {
            margin: 0;
            font-style: italic;
            color: #6c757d;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Loading spinner for Stripe redirect */
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 15px;
        }

        .spinner-border {
            width: 2rem;
            height: 2rem;
            border: 0.25em solid #dee2e6;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 eSIM Assignment Test Interface</h1>
            <p>Complete workflow testing for TraveRoam eSIM management</p>
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="loginForTesting()" id="login-btn">
                    🔐 Login (<EMAIL> / admin123)
                </button>
                <button class="btn btn-secondary" onclick="logout()" id="logout-btn" style="display: none;">
                    🚪 Logout
                </button>
                <span id="login-status" style="margin-left: 10px; font-size: 0.9rem;"></span>
            </div>
        </div>

        <div class="workflow-container">
            <div class="sidebar">
                <h3>Workflow Steps</h3>
                <ul class="workflow-steps">
                    <li class="workflow-step active" data-step="1">
                        <strong>Step 1:</strong> Add New User
                    </li>
                    <li class="workflow-step" data-step="2">
                        <strong>Step 2:</strong> Fetch eSIM Plans
                    </li>
                    <li class="workflow-step" data-step="3">
                        <strong>Step 3:</strong> Payment Processing
                    </li>
                    <li class="workflow-step" data-step="4">
                        <strong>Step 4:</strong> Provision eSIM
                    </li>
                    <li class="workflow-step" data-step="5">
                        <strong>Step 5:</strong> QR & Email Delivery
                    </li>
                    <li class="workflow-step" data-step="6">
                        <strong>Step 6:</strong> Save to Database
                    </li>
                </ul>

                <div class="progress-bar">
                    <div class="progress-fill" style="width: 25%"></div>
                </div>
            </div>

            <div class="main-content">
                <!-- Step 1: Add New User -->
                <div class="step-content active" id="step-1">
                    <div class="card">
                        <div class="card-header">👤 Step 1: Add New User</div>
                        <div class="card-body">
                            <div class="user-form">
                                <div class="form-group">
                                    <label for="user-full-name">👤 Full Name *</label>
                                    <input type="text" id="user-full-name" class="form-control" placeholder="Enter full name" required>
                                </div>
                                <div class="form-group">
                                    <label for="user-phone">📞 Phone Number (with country code) *</label>
                                    <input type="tel" id="user-phone" class="form-control" placeholder="+91XXXXXXXXXX" required>
                                    <div id="country-detection" class="country-info"></div>
                                </div>
                                <div class="form-group">
                                    <label for="user-email">📧 Email *</label>
                                    <input type="email" id="user-email" class="form-control" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="user-passport">🛂 Passport Number / National ID *</label>
                                    <input type="text" id="user-passport" class="form-control" placeholder="Passport or ID number" required>
                                </div>
                                <div class="form-group">
                                    <label for="user-travel-date">📅 Date of Travel (optional)</label>
                                    <input type="date" id="user-travel-date" class="form-control">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-primary" onclick="validateUserData()">
                                        ✅ Validate User Data
                                    </button>
                                    <div class="auto-info" style="margin-top: 10px;">
                                        <p class="text-muted">
                                            ℹ️ Auto-validation occurs when you fill all fields and return to this step
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button id="next-step-1" class="btn btn-primary" onclick="nextStep()" disabled>
                                Next: Fetch eSIM Plans →
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Fetch Available eSIM Plans -->
                <div class="step-content" id="step-2">
                    <div class="card">
                        <div class="card-header">📦 Step 2: Available eSIM Plans</div>
                        <div class="card-body">
                            <div id="country-info-display" class="country-display">
                                <!-- Country info will be displayed here -->
                            </div>
                            <div class="form-group">
                                <button id="fetch-bundles-btn" class="btn btn-primary" onclick="fetchBundlesWithValidation()">
                                    📱 Fetch eSIM Plans
                                </button>
                                <button class="btn btn-warning ms-2" onclick="manualRestoreBundle()">
                                    🔄 Restore Bundle
                                </button>
                                <button class="btn btn-info ms-2" onclick="debugWorkflowData()">
                                    🔍 Debug
                                </button>
                            </div>
                            <div id="bundle-list" class="bundle-grid">
                                <!-- Bundle items will be populated after validation and fetch -->
                            </div>
                            <div class="auto-info">
                                <p class="text-muted">
                                    ℹ️ Click the button to validate user data and fetch available bundles
                                </p>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-secondary" onclick="previousStep()">← Previous</button>
                            <button id="next-step-2" class="btn btn-primary" onclick="nextStep()" disabled>
                                Next: Payment Processing →
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Payment Processing -->
                <div class="step-content" id="step-3">
                    <div class="card">
                        <div class="card-header">💳 Step 3: Payment Processing</div>
                        <div class="card-body">
                            <div id="payment-summary" class="payment-summary">
                                <!-- Payment summary will be shown here -->
                            </div>
                            <div class="form-group">
                                <label for="reseller-markup">Reseller Markup Percentage (0-50%)</label>
                                <input type="number" id="reseller-markup" class="form-control" min="0" max="50" value="0" placeholder="0">
                            </div>
                            <div id="pricing-breakdown" class="pricing-breakdown">
                                <!-- Pricing breakdown will be shown here -->
                            </div>
                            <div class="form-group">
                                <button class="btn btn-success" onclick="processPayment()">
                                    💳 Process Payment
                                </button>
                                <div id="payment-loading" class="loading hidden">Processing payment...</div>
                            </div>
                            <div id="payment-status" class="payment-status">
                                <!-- Payment status will be shown here -->
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-secondary" onclick="previousStep()">← Previous</button>
                            <button id="next-step-3" class="btn btn-primary" onclick="nextStep()" disabled>
                                Next: Provision eSIM →
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Provision eSIM -->
                <div class="step-content" id="step-4">
                    <div class="card">
                        <div class="card-header">🚀 Step 4: Provision eSIM</div>
                        <div class="card-body">
                            <div id="provisioning-status" class="provisioning-status">
                                <!-- Provisioning status will be shown here -->
                            </div>
                            <div class="form-group">
                                <button class="btn btn-primary" onclick="provisionESIM()">
                                    🚀 Provision eSIM via TraveRoam
                                </button>
                                <button class="btn btn-warning ms-2" onclick="manualRestoreBundle()">
                                    🔄 Restore Bundle
                                </button>
                                <button class="btn btn-info ms-2" onclick="debugWorkflowData()">
                                    🔍 Debug
                                </button>
                                <div id="provision-loading" class="loading hidden">Provisioning eSIM...</div>
                            </div>
                            <div id="esim-provisioning-details" class="esim-details">
                                <!-- eSIM provisioning details will be shown here -->
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-secondary" onclick="previousStep()">← Previous</button>
                            <button id="next-step-4" class="btn btn-primary" onclick="nextStep()" disabled>
                                Next: QR & Email Delivery →
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 5: QR Code & Email Delivery -->
                <div class="step-content" id="step-5">
                    <div class="card">
                        <div class="card-header">📱 Step 5: QR Code & Email Delivery</div>
                        <div class="card-body">
                            <div id="qr-code-display" class="qr-code-section">
                                <!-- QR code will be displayed here -->
                            </div>
                            <div id="esim-details-complete" class="esim-details-complete">
                                <!-- Complete eSIM details will be shown here -->
                            </div>
                            <div class="auto-info">
                                <p class="text-muted">
                                    ℹ️ QR code generation and email delivery happen automatically when you enter this step
                                </p>
                            </div>
                            <div id="email-status" class="email-status">
                                <!-- Email status will be shown here -->
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-secondary" onclick="previousStep()">← Previous</button>
                            <button id="next-step-5" class="btn btn-primary" onclick="nextStep()" disabled>
                                Next: Save to Database →
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Step 6: Save to Database -->
                <div class="step-content" id="step-6">
                    <div class="card">
                        <div class="card-header">💾 Step 6: Save to Database</div>
                        <div class="card-body">
                            <div id="database-summary" class="database-summary">
                                <!-- Database operation summary will be shown here -->
                            </div>
                            <div class="auto-info">
                                <p class="text-muted">
                                    ℹ️ Data will be automatically saved to database when you enter this step
                                </p>
                            </div>
                            <div id="save-status" class="save-status">
                                <!-- Save status will be shown here -->
                            </div>
                            <div class="workflow-complete" id="workflow-complete" style="display: none;">
                                <div class="success-message">
                                    <h3>🎉 Workflow Completed Successfully!</h3>
                                    <p>All data has been saved to the database and the eSIM has been delivered to the client.</p>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-secondary" onclick="previousStep()">← Previous</button>
                            <button class="btn btn-success" onclick="resetWorkflow()">🔄 Start New Workflow</button>
                            <button class="btn btn-info" onclick="debugWorkflowData()">🔍 Debug Data</button>
                            <button class="btn btn-warning" onclick="manualRestoreData()">🔄 Restore Data</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let workflowData = {
            userData: null,
            selectedBundle: null,
            paymentData: null,
            esimData: null,
            currentStep: 1
        };
        
        // Authentication token management
        let authToken = null;

        function getAuthToken() {
            // Get token from localStorage or return null
            return localStorage.getItem('auth_token') || null;
        }

        function setAuthToken(token) {
            // Store token in localStorage
            localStorage.setItem('auth_token', token);
            authToken = token;
        }

        function clearAuthToken() {
            // Remove token from localStorage
            localStorage.removeItem('auth_token');
            authToken = null;
        }

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved workflow data first
            loadWorkflowData();
            
            updateProgress();
            
            // Handle payment success/cancel from Stripe redirect with enhanced polling
            handlePaymentReturnWithPolling();
            
            // Check if user is already logged in
            const token = getAuthToken();
            if (token) {
                updateLoginUI(true);
                document.getElementById('login-status').textContent = '✅ Already logged in';
                document.getElementById('login-status').style.color = '#4caf50';
            } else {
                updateLoginUI(false);
            }

            // Add event listener for reseller markup input
            const markupInput = document.getElementById('reseller-markup');
            if (markupInput) {
                markupInput.addEventListener('input', updatePaymentSummary);
            }

            // Add event listeners for step navigation
            document.querySelectorAll('[id^="next-step-"]').forEach(btn => {
                btn.addEventListener('click', function() {
                    const step = parseInt(this.id.split('-')[2]);
                    if (step < 6) {
                        workflowData.currentStep = step + 1;
                        updateProgress();
                        updateStepDisplay();
                        handleStepEntry(step + 1);
                    }
                });
            });

            document.querySelectorAll('[id^="prev-step-"]').forEach(btn => {
                btn.addEventListener('click', function() {
                    const step = parseInt(this.id.split('-')[2]);
                    if (step > 1) {
                        workflowData.currentStep = step - 1;
                        updateProgress();
                        updateStepDisplay();
                        handleStepEntry(step - 1);
                    }
                });
            });

            // Add event listeners for form validation
            document.getElementById('user-full-name').addEventListener('blur', autoValidateIfDataEntered);
            document.getElementById('user-phone').addEventListener('blur', autoValidateIfDataEntered);
            document.getElementById('user-email').addEventListener('blur', autoValidateIfDataEntered);
            document.getElementById('user-passport').addEventListener('blur', autoValidateIfDataEntered);

            // Initialize step display
            updateStepDisplay();
        });

        // Check if all fields are filled and auto-validate
        function checkAndAutoValidate() {
            const fullName = document.getElementById('user-full-name').value.trim();
            const phone = document.getElementById('user-phone').value.trim();
            const email = document.getElementById('user-email').value.trim();
            const passport = document.getElementById('user-passport').value.trim();

            // Only auto-validate if we're on step 1 and not already validated
            if (workflowData.currentStep === 1 && !workflowData.userData && fullName && phone && email && passport) {
                if (validatePhone(phone) && validateEmail(email)) {
                    showAlert('All fields completed! Auto-validating...', 'info');
                    setTimeout(() => {
                        validateUserData();
                    }, 1000);
                }
            }
        }

        // Step navigation
        function nextStep() {
            if (workflowData.currentStep < 6) {
                workflowData.currentStep++;
                updateStepDisplay();
                updateProgress();
                
                // Automatically trigger actions when entering each step
                handleStepEntry(workflowData.currentStep);
            }
        }

        async function handleStepEntry(step) {
            switch(step) {
                case 1: // Auto-validate when step loads
                    setTimeout(() => {
                        autoValidateIfDataEntered();
                    }, 500);
                    break;
                    
                case 2: // Fetch eSIM Plans - now manual via button
                    // Display country info if user data is available
                    if (workflowData.userData) {
                        displayCountryInfo();
                    }
                    break;
                    
                case 3: // Payment Processing
                    if (workflowData.selectedBundle && workflowData.userData) {
                        showStepLoading(3, 'Preparing payment summary...');
                        updatePaymentSummary();
                        hideStepLoading(3);
                    }
                    break;
                    
                case 4: // Provision eSIM
                    if (workflowData.paymentData) {
                        showStepLoading(4, 'Ready to provision eSIM...');
                        setTimeout(() => hideStepLoading(4), 1000);
                    }
                    break;
                    
                case 5: // QR & Email
                    if (workflowData.esimData) {
                        showStepLoading(5, 'Sending eSIM details email...');
                        // Auto-send email after 1 second
                        setTimeout(async () => {
                            await sendESIMEmail();
                            hideStepLoading(5);
                        }, 1000);
                    }
                    break;
                    
                case 6: // Save to Database
                    if (workflowData.esimData) {
                        showStepLoading(6, 'Preparing to save data...');
                        // Auto-save after 2 seconds
                        setTimeout(async () => {
                            await saveToDatabase();
                            hideStepLoading(6);
                        }, 2000);
                    }
                    break;
            }
        }

        function showStepLoading(step, message) {
            const stepElement = document.getElementById(`step-${step}`);
            if (stepElement) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'step-loading';
                loadingDiv.id = `step-${step}-loading`;
                loadingDiv.innerHTML = `
                    <div class="loading-overlay">
                        <div class="loading-spinner"></div>
                        <div class="loading-message">${message}</div>
                    </div>
                `;
                stepElement.appendChild(loadingDiv);
            }
        }

        function hideStepLoading(step) {
            const loadingElement = document.getElementById(`step-${step}-loading`);
            if (loadingElement) {
                loadingElement.remove();
            }
        }

        function previousStep() {
            if (workflowData.currentStep > 1) {
                workflowData.currentStep--;
                updateStepDisplay();
                updateProgress();
            }
        }

        function updateStepDisplay() {
            // Update step indicators
            document.querySelectorAll('.workflow-step').forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index + 1 === workflowData.currentStep) {
                    step.classList.add('active');
                } else if (index + 1 < workflowData.currentStep) {
                    step.classList.add('completed');
                }
            });

            // Update content visibility
            document.querySelectorAll('.step-content').forEach((content, index) => {
                content.classList.remove('active');
                if (index + 1 === workflowData.currentStep) {
                    content.classList.add('active');
                }
            });
        }

        function updateProgress() {
            const progress = (workflowData.currentStep / 6) * 100;
            document.querySelector('.progress-fill').style.width = progress + '%';
        }

        // Step 1: User Data Validation (Following console script)
        function validateEmail(email) {
            const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return pattern.test(email);
        }

        // Helper function for authenticated API requests with automatic token refresh
        async function makeAuthenticatedRequest(url, options = {}) {
            let token = getAuthToken();
            
            if (!token) {
                throw new Error('No authentication token available. Please login first.');
            }

            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
                ...options.headers
            };

            let response = await fetch(url, {
                ...options,
                headers
            });

            // If token is expired, try to refresh and retry
            if (response.status === 401) {
                console.log('Token expired, attempting to refresh...');
                try {
                    const refreshed = await refreshToken();
                    if (refreshed) {
                        // Retry with new token
                        const newToken = getAuthToken();
                        headers['Authorization'] = `Bearer ${newToken}`;
                        response = await fetch(url, { ...options, headers });
                    } else {
                        throw new Error('Token refresh failed');
                    }
                } catch (refreshError) {
                    console.error('Token refresh failed:', refreshError);
                    showAlert('Session expired. Please login again.', 'error');
                    throw new Error('Authentication failed. Please login again.');
                }
            }

            return response;
        }

        function validatePhone(phone) {
            const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
            return cleanPhone.startsWith('+') && cleanPhone.length >= 10;
        }

        async function detectCountryFromPhone(phone, validateEsim = false) {
            try {
                // Use backend API for accurate country detection and eSIM validation
                const response = await fetch('/api/v1/utils/detect-country/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone_number: phone,
                        validate_esim: validateEsim
                    })
                });

                const data = await response.json();
                
                if (data.success && data.data) {
                    // Check if this is validation response with country_info
                    if (data.data.country_info) {
                        return data.data.country_info;
                    }
                    // Legacy response format
                    return data.data;
                } else {
                    console.warn('Backend country detection failed:', data.message);
                    // If validation failed, throw error to show to user
                    if (validateEsim && !data.success) {
                        throw new Error(data.message || 'Phone number validation failed');
                    }
                    // Fallback to basic detection
                    return detectCountryFromPhoneFallback(phone);
                }
            } catch (error) {
                console.warn('Country detection API error:', error);
                // If validation was requested and failed, re-throw the error
                if (validateEsim && error.message !== 'Failed to fetch') {
                    throw error;
                }
                // Fallback to basic detection
                return detectCountryFromPhoneFallback(phone);
            }
        }

        function detectCountryFromPhoneFallback(phone) {
            // Simple fallback when backend API fails
            return {
                "name": "Unknown Country", 
                "code": "XX", 
                "region": "Unknown"
            };
        }

        async function validateUserData() {
            const fullName = document.getElementById('user-full-name').value.trim();
            const phone = document.getElementById('user-phone').value.trim();
            const email = document.getElementById('user-email').value.trim();
            const passport = document.getElementById('user-passport').value.trim();
            const travelDate = document.getElementById('user-travel-date').value;

            // Validation
            if (fullName.length < 2) {
                showAlert('Name must be at least 2 characters long.', 'error');
                return;
            }

            if (!validatePhone(phone)) {
                showAlert('Please enter a valid phone number with country code (e.g., +92XXXXXXXXX)', 'error');
                return;
            }

            if (!validateEmail(email)) {
                showAlert('Please enter a valid email address.', 'error');
                return;
            }

            if (passport.length < 3) {
                showAlert('Please enter a valid passport/ID number.', 'error');
                return;
            }

            // Show loading while detecting country
            document.getElementById('country-detection').innerHTML = `
                <div class="alert alert-info">
                    🔍 Detecting country from phone number...
                </div>
            `;

            try {
                // Detect country using backend API and validate eSIM eligibility
                const countryInfo = await detectCountryFromPhone(phone, true);
                
                // Store user data
                workflowData.userData = {
                    fullName: fullName,
                    phoneNumber: phone,
                    email: email,
                    passportId: passport,
                    countryOfTravel: countryInfo,
                    travelDate: travelDate || null
                };

                // Show country detection result
                document.getElementById('country-detection').innerHTML = `
                    <div class="alert alert-success">
                        🌍 Detected Country: ${countryInfo.name} (${countryInfo.code})
                        <br>📍 Region: ${countryInfo.region}
                    </div>
                `;

                // Enable next step
                document.getElementById('next-step-1').disabled = false;
                showAlert('User data validated successfully!', 'success');
                
            } catch (error) {
                console.error('User validation error:', error);
                document.getElementById('country-detection').innerHTML = `
                    <div class="alert alert-error">
                        ❌ ${error.message || 'Validation failed'}
                    </div>
                `;
                
                // Show specific error message for eSIM validation failures
                if (error.message && (error.message.includes('active eSIM') || error.message.includes('bundle'))) {
                    showAlert('❌ ' + error.message, 'error');
                } else {
                    showAlert('Validation failed: ' + (error.message || 'Please check your phone number and try again.'), 'warning');
                }
                
                // Don't enable next step on validation failure
                document.getElementById('next-step-1').disabled = true;
            }
        }

        // Auto-validation function
        function autoValidateIfDataEntered() {
            const fullName = document.getElementById('user-full-name').value.trim();
            const phone = document.getElementById('user-phone').value.trim();
            const email = document.getElementById('user-email').value.trim();
            const passport = document.getElementById('user-passport').value.trim();

            // Check if all required fields have data
            if (fullName && phone && email && passport) {
                // Auto-validate if all fields are filled
                showAlert('Auto-validating user data...', 'info');
                setTimeout(() => {
                    validateUserData();
                }, 1000);
            }
        }

        // Display country info when entering Step 2
        function displayCountryInfo() {
            if (workflowData.userData && workflowData.userData.countryOfTravel) {
                const country = workflowData.userData.countryOfTravel;
                document.getElementById('country-info-display').innerHTML = `
                    <div class="alert alert-info">
                        <h5>🌍 Destination: ${country.name} (${country.code})</h5>
                        <p>Region: ${country.region}</p>
                        <p>Click "Fetch eSIM Plans" to see available bundles for this destination.</p>
                    </div>
                `;
            }
        }

        // New function: Validate data first, then fetch bundles
        async function fetchBundlesWithValidation() {
            const fetchBtn = document.getElementById('fetch-bundles-btn');
            const originalText = fetchBtn.innerHTML;
            
            try {
                // Step 1: Validate user data if not already done
                if (!workflowData.userData) {
                    fetchBtn.innerHTML = '🔄 Validating user data...';
                    fetchBtn.disabled = true;
                    
                    // Validate user data first
                    const fullName = document.getElementById('user-full-name').value.trim();
                    const phone = document.getElementById('user-phone').value.trim();
                    const email = document.getElementById('user-email').value.trim();
                    const passport = document.getElementById('user-passport').value.trim();
                    
                    if (!fullName || !phone || !email || !passport) {
                        throw new Error('Please fill all required fields in Step 1 first.');
                    }
                    
                    if (!validatePhone(phone) || !validateEmail(email)) {
                        throw new Error('Please provide valid phone number and email in Step 1.');
                    }
                    
                    // Detect country and validate eSIM eligibility
                    const countryInfo = await detectCountryFromPhone(phone, true);
                    
                    // Store user data
                    workflowData.userData = {
                        fullName,
                        phone,
                        email,
                        passport,
                        countryOfTravel: countryInfo,
                        travelDate: document.getElementById('user-travel-date').value || null
                    };
                    
                    showAlert('User data validated successfully!', 'success');
                }
                
                // Step 2: Fetch bundles
                fetchBtn.innerHTML = '🔄 Fetching bundles...';
                await fetchAvailableBundles();
                
                fetchBtn.innerHTML = '✅ Bundles Loaded';
                fetchBtn.disabled = true; // Disable after successful fetch
                
            } catch (error) {
                console.error('Error in fetch process:', error);
                showAlert(error.message || 'Failed to fetch bundles. Please try again.', 'error');
                fetchBtn.innerHTML = originalText;
                fetchBtn.disabled = false;
            }
        }

        // Helper function to format data amounts
        function formatDataAmount(dataAmount) {
            if (dataAmount === -1) {
                return 'Unlimited';
            } else if (dataAmount >= 1000) {
                return `${(dataAmount / 1000).toFixed(dataAmount % 1000 === 0 ? 0 : 1)}GB`;
            } else {
                return `${dataAmount}MB`;
            }
        }

        // Step 2: Fetch Available Bundles (Following console script)
        async function fetchAvailableBundles() {
            if (!workflowData.userData) {
                showAlert('Please validate user data first.', 'error');
                return;
            }

            // Check authentication using helper function
            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first.', 'error');
                return;
            }

            const countryDisplay = document.getElementById('country-info-display');
            const bundleList = document.getElementById('bundle-list');

            // Display country info
            const country = workflowData.userData.countryOfTravel;
            countryDisplay.innerHTML = `
                <div class="alert alert-info">
                    <h4>🌍 Fetching bundles for: ${country.name} (${country.code})</h4>
                    <p>Region: ${country.region}</p>
                </div>
            `;

            try {
                // Call TraveRoam plans API with query parameters
                const queryParams = new URLSearchParams({
                    countries: country.code,
                    region: country.region
                });
                
                const response = await makeAuthenticatedRequest(`/api/v1/traveroam/plans/?${queryParams}`, {
                    method: 'GET'
                });

                const data = await response.json();
                
                if (data.success && data.data && data.data.length > 0) {
                    displayBundles(data.data);
                    showAlert(`Found ${data.data.length} bundles for ${country.name}`, 'success');
                } else {
                    // No bundles available from API
                    displayBundles([]);
                    showAlert(`No bundles available for ${country.name} at the moment.`, 'warning');
                }
            } catch (error) {
                console.error('Error fetching bundles:', error);
                // Show error and empty bundles
                displayBundles([]);
                showAlert('Error fetching bundles from server. Please try again.', 'error');
            }
        }

        // Removed hardcoded getPredefinedBundles - now using backend API only

        function selectBundle(index) {
            const bundle = workflowData.availableBundles[index];
            
            // Ensure bundle has required fields and price is a number
            if (!bundle || bundle.price === undefined || bundle.price === null) {
                showAlert('Invalid bundle data. Please try selecting another bundle.', 'error');
                return;
            }
            
            // Ensure price is a number
            bundle.price = parseFloat(bundle.price) || 0;
            
            // Store selected bundle
            workflowData.selectedBundle = bundle;
            
            // Save to localStorage immediately
            saveWorkflowData();
            
            console.log('✅ Bundle selected and saved:', bundle);

            // Update UI
            document.querySelectorAll('.bundle-card').forEach((card, i) => {
                card.classList.remove('selected');
                if (i === index) {
                    card.classList.add('selected');
                }
            });

            // Enable next step
            document.getElementById('next-step-2').disabled = false;
            showAlert(`Selected: ${bundle.name || bundle.data + ' - ' + bundle.duration} ($${bundle.price.toFixed(2)})`, 'success');
        }

        // Function to restore bundle selection from saved data
        function restoreBundleSelection() {
            if (workflowData.selectedBundle && workflowData.availableBundles) {
                // Find the index of the selected bundle in available bundles
                const selectedIndex = workflowData.availableBundles.findIndex(
                    bundle => bundle.bundle_id === workflowData.selectedBundle.bundle_id ||
                              bundle.name === workflowData.selectedBundle.name
                );
                
                if (selectedIndex !== -1) {
                    // Highlight the selected bundle
                    document.querySelectorAll('.bundle-card').forEach((card, i) => {
                        card.classList.remove('selected');
                        if (i === selectedIndex) {
                            card.classList.add('selected');
                        }
                    });
                    
                    // Enable next step
                    document.getElementById('next-step-2').disabled = false;
                    
                    console.log('✅ Bundle selection restored:', workflowData.selectedBundle);
                    return true;
                }
            }
            return false;
        }

        // Enhanced function to display bundles with selection restoration
        function displayBundles(bundles) {
            const bundleList = document.getElementById('bundle-list');
            bundleList.innerHTML = '';

            if (bundles.length === 0) {
                bundleList.innerHTML = `
                    <div class="alert alert-warning">
                        <h4>No Bundles Available</h4>
                        <p>No eSIM bundles are available for this country at the moment.</p>
                    </div>
                `;
                return;
            }

            bundles.forEach((bundle, index) => {
                // Normalize bundle data structure to match template expectations
                bundle.price = parseFloat(bundle.price) || 0;
                bundle.bundle_id = bundle.name; // TraveRoam uses 'name' as bundle ID
                bundle.data = bundle.dataAmount ? formatDataAmount(bundle.dataAmount) : 'Unknown';
                bundle.duration = bundle.duration ? `${bundle.duration} ${bundle.duration === 1 ? 'Day' : 'Days'}` : 'Unknown';
                
                const bundleCard = document.createElement('div');
                bundleCard.className = 'bundle-card';
                bundleCard.innerHTML = `
                    <div class="bundle-header">
                        <h4>${bundle.description || bundle.data + ' - ' + bundle.duration}</h4>
                        <span class="bundle-price">$${bundle.price.toFixed(2)}</span>
                    </div>
                    <div class="bundle-details">
                        <p><strong>Data:</strong> ${bundle.data}</p>
                        <p><strong>Duration:</strong> ${bundle.duration}</p>
                        <p><strong>Country:</strong> ${workflowData.userData.countryOfTravel.name}</p>
                        <p><strong>Bundle ID:</strong> ${bundle.bundle_id}</p>
                    </div>
                    <button class="btn btn-primary" onclick="selectBundle(${index})">
                        Select Bundle
                    </button>
                `;
                bundleList.appendChild(bundleCard);
            });

            // Store bundles for selection
            workflowData.availableBundles = bundles;
            
            // Try to restore previous selection
            const selectionRestored = restoreBundleSelection();
            
            // Add success indicator
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success';
            successDiv.innerHTML = `
                <h5>✅ Bundles Loaded Successfully!</h5>
                <p>Found ${bundles.length} eSIM bundle${bundles.length === 1 ? '' : 's'} for ${workflowData.userData.countryOfTravel.name}. ${selectionRestored ? 'Previous selection restored.' : 'Select one to continue.'}</p>
            `;
            bundleList.appendChild(successDiv);
        }

        // Handle payment return from Stripe redirect
        function handlePaymentReturn() {
            const urlParams = new URLSearchParams(window.location.search);
            const paymentStatus = urlParams.get('payment');
            const sessionId = localStorage.getItem('stripeSessionId');
            
            // Check if we've already processed this payment return
            const paymentProcessed = sessionStorage.getItem('paymentReturnProcessed');
            
            if ((paymentStatus === 'success' || paymentStatus === 'cancel') && !paymentProcessed) {
                // Mark as processed to prevent re-execution
                sessionStorage.setItem('paymentReturnProcessed', 'true');
            
            if (paymentStatus === 'success' && sessionId) {
                    console.log('🎉 Payment success detected, processing...');
                    
                    // Clean URL parameters
                    const url = new URL(window.location);
                    url.searchParams.delete('payment');
                    window.history.replaceState({}, document.title, url.pathname);
                    
                    // Update payment data in workflow (following console script pattern)
                    if (!workflowData.paymentData) {
                        // Try to retrieve stored payment details from processPayment
                        const storedPaymentData = localStorage.getItem('paymentDetails');
                        let paymentDetails = {};
                        
                        if (storedPaymentData) {
                            try {
                                paymentDetails = JSON.parse(storedPaymentData);
                            } catch (e) {
                                console.warn('Failed to parse stored payment data:', e);
                            }
                        }
                        
                        // Create payment data if it doesn't exist
                        workflowData.paymentData = {
                            session_id: sessionId,
                            payment_status: 'paid',
                            amount: paymentDetails.amount || 0,
                            currency: paymentDetails.currency || 'USD',
                            markup_percent: paymentDetails.markup_percent || 0,
                            markup_amount: paymentDetails.markup_amount || 0,
                            base_price: paymentDetails.base_price || 0,
                            final_price: paymentDetails.final_price || 0,
                            verified_via_stripe: true
                        };
                        
                        console.log('✅ Created payment data from return:', workflowData.paymentData);
                    } else {
                        // Update existing payment data
                        workflowData.paymentData.payment_status = 'paid';
                workflowData.paymentData.verified_via_stripe = true;
                        console.log('✅ Updated existing payment data:', workflowData.paymentData);
                    }
                    
                    // Show success message and advance to step 4
                    showAlert('✅ Payment completed successfully! Proceeding to eSIM provisioning...', 'success');
                    
                    // Advance to step 4 after a short delay
                    setTimeout(() => {
                workflowData.currentStep = 4;
                updateProgress();
                updateStepDisplay();
                
                        // Clear the stored session ID
                localStorage.removeItem('stripeSessionId');
                        
                        console.log('🚀 Advanced to Step 4: Provision eSIM');
                    }, 1500);
                
            } else if (paymentStatus === 'cancel') {
                    console.log('❌ Payment cancelled, staying on step 3');
                    
                    // Clean URL parameters
                    const url = new URL(window.location);
                    url.searchParams.delete('payment');
                    window.history.replaceState({}, document.title, url.pathname);
                    
                    // Stay on step 3 and show error
                workflowData.currentStep = 3;
                updateProgress();
                updateStepDisplay();
                showAlert('❌ Payment was cancelled. Please try again.', 'error');
                
                    // Clear the stored session ID
                localStorage.removeItem('stripeSessionId');
                }
            }
        }
        
        // Initialize payment return handler when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 Page loaded, checking for payment return...');
            handlePaymentReturn();
        });

        // Step 3: Payment Processing (Following console script)
        function updatePaymentSummary() {
            if (!workflowData.selectedBundle || !workflowData.userData) return;

            const summary = document.getElementById('payment-summary');
            const bundle = workflowData.selectedBundle;
            const user = workflowData.userData;

            summary.innerHTML = `
                <div class="alert alert-info">
                    <h4>💳 Payment Summary</h4>
                    <p><strong>Client:</strong> ${user.fullName} (${user.email})</p>
                    <p><strong>Bundle:</strong> ${bundle.name || bundle.data + ' - ' + bundle.duration}</p>
                    <p><strong>Country:</strong> ${user.countryOfTravel.name}</p>
                    <p><strong>Base Price:</strong> $${bundle.price}</p>
                </div>
            `;
            updatePricingBreakdown();
        }

        function updatePricingBreakdown() {
            // Safely check if bundle data exists
            if (!workflowData.selectedBundle || !workflowData.selectedBundle.price) {
                console.warn('No bundle selected or price missing');
                return;
            }

            const markup = parseFloat(document.getElementById('reseller-markup').value) || 0;
            const basePrice = parseFloat(workflowData.selectedBundle.price) || 0; // Ensure it's a number
            const markupAmount = (basePrice * markup / 100);
            const finalPrice = basePrice + markupAmount;

            const breakdown = document.getElementById('pricing-breakdown');
            breakdown.innerHTML = `
                <div class="pricing-details">
                    <table class="table">
                        <tr><td>Base Price:</td><td>$${basePrice.toFixed(2)}</td></tr>
                        <tr><td>Reseller Markup (${markup}%):</td><td>$${markupAmount.toFixed(2)}</td></tr>
                        <tr class="total"><td><strong>Final Price:</strong></td><td><strong>$${finalPrice.toFixed(2)}</strong></td></tr>
                    </table>
                </div>
            `;
        }

        // Add payment status polling like console script
        async function pollPaymentStatus(sessionId, maxAttempts = 30) {
            console.log('🔄 Starting payment status polling...');
            let attempt = 0;
            
            while (attempt < maxAttempts) {
                console.log(`🔍 Checking payment status... (Attempt ${attempt + 1}/${maxAttempts})`);
                
                try {
                    const response = await makeAuthenticatedRequest(`/api/v1/stripe/retrieve-checkout-session/?session_id=${sessionId}`, {
                        method: 'GET'
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        const paymentStatus = data.data.payment_status;
                        console.log(`📊 Payment status: ${paymentStatus}`);
                        
                        // Update payment status display
                        const statusElement = document.getElementById('payment-status');
                        statusElement.innerHTML += `
                            <div class="alert alert-info">
                                <h4>🔍 Payment Status Check</h4>
                                <p><strong>Status:</strong> ${paymentStatus}</p>
                                <p><strong>Attempt:</strong> ${attempt + 1}/${maxAttempts}</p>
                            </div>
                        `;
                        
                        if (paymentStatus === 'paid') {
                            console.log('✅ Payment confirmed via Stripe! Proceeding with eSIM provisioning...');
                            return {
                                success: true,
                                status: 'paid',
                                data: data.data
                            };
                        } else if (paymentStatus === 'expired') {
                            console.log('❌ Payment session expired. Please start over.');
                            return {
                                success: false,
                                status: 'expired',
                                message: 'Payment session expired'
                            };
                        } else if (paymentStatus === 'canceled') {
                            console.log('❌ Payment was canceled. Please start over.');
                            return {
                                success: false,
                                status: 'canceled',
                                message: 'Payment was canceled'
                            };
                        } else {
                            console.log(`⏳ Payment still processing... (${paymentStatus})`);
                            console.log('💡 Waiting 10 seconds before next check...');
                            
                            // Update status display
                            statusElement.innerHTML += `
                                <div class="alert alert-warning">
                                    <p>⏳ Payment still processing... (${paymentStatus})</p>
                                    <p>💡 Waiting 10 seconds before next check...</p>
                                </div>
                            `;
                            
                            // Wait 10 seconds like console script
                            await new Promise(resolve => setTimeout(resolve, 10000));
                        }
                    } else {
                        console.log(`❌ Error checking payment status: ${data.error}`);
                        statusElement.innerHTML += `
                            <div class="alert alert-error">
                                <p>❌ Error checking payment status: ${data.error}</p>
                                <p>💡 Waiting 10 seconds before retry...</p>
                            </div>
                        `;
                        await new Promise(resolve => setTimeout(resolve, 10000));
                    }
                } catch (error) {
                    console.error(`❌ Error checking payment status: ${error}`);
                    const statusElement = document.getElementById('payment-status');
                    statusElement.innerHTML += `
                        <div class="alert alert-error">
                            <p>❌ Error checking payment status: ${error.message}</p>
                            <p>💡 Waiting 10 seconds before retry...</p>
                        </div>
                    `;
                    await new Promise(resolve => setTimeout(resolve, 10000));
                }
                
                attempt++;
            }
            
            console.log('❌ Payment verification timeout. Please check your payment status manually.');
            return {
                success: false,
                status: 'timeout',
                message: 'Payment verification timeout'
            };
        }

        async function processPayment() {
            if (!workflowData.selectedBundle) {
                showAlert('Please select a bundle first.', 'error');
                return;
            }

            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first.', 'error');
                return;
            }

            // Check if payment has already been completed
            if (workflowData.paymentData && workflowData.paymentData.payment_status === "completed") {
                showAlert('Payment has already been completed for this bundle.', 'info');
                return;
            }

            // Check if there's a pending payment session
            const existingSessionId = localStorage.getItem('stripeSessionId');
            if (existingSessionId && !workflowData.paymentData) {
                // Check if the existing session is still valid
                try {
                    const statusResponse = await makeAuthenticatedRequest(`/api/v1/stripe/payment-status/${existingSessionId}/`, {
                        method: 'GET'
                    });
                    const statusData = await statusResponse.json();
                    
                    if (statusData.success && statusData.data.payment_status === "complete") {
                        // Payment was already completed, restore the data
                        workflowData.paymentData = {
                            session_id: existingSessionId,
                            payment_status: "completed",
                            amount: statusData.data.amount_total / 100, // Convert from cents
                            currency: statusData.data.currency,
                            verified_via_stripe: true
                        };
                        saveWorkflowData();
                        showAlert('Payment already completed! Proceeding to eSIM provisioning.', 'success');
                        return;
                    }
                } catch (error) {
                    console.log('No existing valid payment session found, proceeding with new payment');
                }
            }

            // Check if bundle has already been paid for by this client
            try {
                const bundleCheckResponse = await makeAuthenticatedRequest('/api/v1/stripe/payment-status/', {
                    method: 'POST',
                    body: JSON.stringify({
                        bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name,
                        client_email: workflowData.userData.email,
                        client_phone: workflowData.userData.phoneNumber
                    })
                });
                const bundleCheckData = await bundleCheckResponse.json();
                
                if (bundleCheckData.success && bundleCheckData.data.already_paid) {
                    showAlert('Payment already completed for this bundle! Proceeding to eSIM provisioning.', 'info');
                    // Restore payment data from the existing payment
                    workflowData.paymentData = {
                        session_id: bundleCheckData.data.session_id,
                        payment_status: "completed",
                        amount: bundleCheckData.data.amount,
                        currency: bundleCheckData.data.currency,
                        verified_via_stripe: true
                    };
                    saveWorkflowData();
                    return;
                }
            } catch (error) {
                console.log('Bundle payment check failed, proceeding with new payment');
            }

            const paymentStatus = document.getElementById('payment-status');
            const paymentLoading = document.getElementById('payment-loading');
            
            if (!paymentStatus) {
                showAlert('Payment status element not found.', 'error');
                return;
            }

            // Show loading state
            paymentLoading.classList.remove('hidden');
            paymentStatus.innerHTML = `
                <div class="alert alert-info">
                    <h4>💰 Processing Payment...</h4>
                    <p>Please wait while we prepare your payment...</p>
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;

            try {
                // Calculate pricing
                const basePrice = parseFloat(workflowData.selectedBundle.price) || 0;
                const markup = parseFloat(document.getElementById('reseller-markup').value) || 0;
                const markupAmount = (basePrice * markup) / 100;
                const finalPrice = basePrice + markupAmount;

                // Show payment summary
                paymentStatus.innerHTML = `
                    <div class="alert alert-info">
                        <h4>💰 Payment Summary</h4>
                        <p><strong>Base Price:</strong> $${basePrice.toFixed(2)}</p>
                        ${markup > 0 ? `<p><strong>Reseller Markup (${markup}%):</strong> $${markupAmount.toFixed(2)}</p>` : ''}
                        <p><strong>Final Price:</strong> $${finalPrice.toFixed(2)}</p>
                        <p><strong>Currency:</strong> USD</p>
                    </div>
                `;

                console.log("🔄 Creating real Stripe checkout session...");
                
                // Create real Stripe checkout session
                const response = await makeAuthenticatedRequest('/api/v1/stripe/create-checkout-session/', {
                    method: 'POST',
                    body: JSON.stringify({
                        bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name,
                        bundle_details: {
                            name: workflowData.selectedBundle.name,
                            data: workflowData.selectedBundle.data,
                            duration: workflowData.selectedBundle.duration,
                            price: basePrice,
                            bundle_id: workflowData.selectedBundle.bundle_id
                        },
                        client_data: {
                            full_name: workflowData.userData.fullName,
                            email: workflowData.userData.email,
                            phone: workflowData.userData.phone,
                            country: workflowData.userData.countryOfTravel.name
                        },
                        markup_percent: markup,
                        success_url: window.location.origin + '/api/v1/traveroam/test/?payment=success',
                        cancel_url: window.location.origin + '/api/v1/traveroam/test/?payment=cancel'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    // Store payment data
                    workflowData.paymentData = {
                        session_id: data.data.session_id,
                        checkout_url: data.data.checkout_url,
                        payment_status: "pending",
                        amount: finalPrice,
                        currency: "USD",
                        markup_percent: markup,
                        markup_amount: markupAmount,
                        base_price: basePrice,
                        final_price: finalPrice,
                        stripe_metadata: {
                            bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name,
                            client_name: workflowData.userData.fullName,
                            client_email: workflowData.userData.email,
                            client_phone: workflowData.userData.phone,
                            country: workflowData.userData.countryOfTravel.name
                        },
                        verified_via_stripe: false
                    };

                    // Show redirecting message and automatically redirect to Stripe
                    paymentStatus.innerHTML += `
                        <div class="alert alert-info">
                            <h4>🔄 Redirecting to Stripe...</h4>
                            <p><strong>Session ID:</strong> ${data.data.session_id}</p>
                            <p><strong>Amount:</strong> $${finalPrice.toFixed(2)}</p>
                            <p><em>You will be redirected to Stripe's secure checkout in 2 seconds...</em></p>
                            <div class="loading-spinner">
                                <div class="spinner-border" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Save session ID and payment data for later verification
                    localStorage.setItem('stripeSessionId', data.data.session_id);
                    localStorage.setItem('paymentDetails', JSON.stringify({
                        amount: finalPrice,
                        currency: "USD",
                        markup_percent: markup,
                        markup_amount: markupAmount,
                        base_price: basePrice,
                        final_price: finalPrice
                    }));
                    
                    // Save workflow data before redirect
                    saveWorkflowData();
                    
                    // Automatically redirect to Stripe after 2 seconds
                    setTimeout(() => {
                        window.location.href = data.data.checkout_url;
                    }, 2000);
                    
                    showAlert('Redirecting to Stripe for secure payment...', 'info');
                } else {
                    throw new Error(data.error || 'Failed to create Stripe checkout session');
                }
            } catch (error) {
                console.error('Payment error:', error);
                paymentStatus.innerHTML = `
                    <div class="alert alert-error">
                        <h4>❌ Payment Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                showAlert('Payment failed: ' + error.message, 'error');
            } finally {
                paymentLoading.classList.add('hidden');
            }
        }

        // Enhanced payment return handling with polling
        async function handlePaymentReturnWithPolling() {
            const urlParams = new URLSearchParams(window.location.search);
            const paymentStatus = urlParams.get('payment');
            const sessionId = localStorage.getItem('stripeSessionId');
            
            if (paymentStatus === 'success' && sessionId) {
                console.log('✅ Payment success detected, starting verification...');
                
                // Clean URL parameters
                const url = new URL(window.location);
                url.searchParams.delete('payment');
                window.history.replaceState({}, document.title, url.pathname);
                
                // Show verification message
                showAlert('✅ Payment completed! Verifying payment status...', 'info');
                
                // Start polling for payment status like console script
                const pollResult = await pollPaymentStatus(sessionId);
                
                if (pollResult.success && pollResult.status === 'paid') {
                        // Update payment data
                    workflowData.paymentData = {
                        session_id: sessionId,
                        payment_status: 'paid',
                        amount: pollResult.data.amount_total || 0,
                        currency: pollResult.data.currency || 'USD',
                        verified_via_stripe: true,
                        stripe_metadata: pollResult.data.metadata || {}
                    };
                    
                    // Save updated workflow data
                    saveWorkflowData();
                    
                    showAlert('✅ Payment verified successfully! Proceeding to eSIM provisioning...', 'success');
                    
                    // Advance to step 4
                        setTimeout(() => {
                            workflowData.currentStep = 4;
                            updateProgress();
                            updateStepDisplay();
                        }, 1500);
                        
                    } else {
                    showAlert(`❌ Payment verification failed: ${pollResult.message}`, 'error');
                    // Stay on step 3
                    workflowData.currentStep = 3;
                    updateProgress();
                    updateStepDisplay();
                }
                
                // Clear stored session ID
                localStorage.removeItem('stripeSessionId');
                
            } else if (paymentStatus === 'cancel') {
                console.log('❌ Payment cancelled');
                
                // Clean URL parameters
                const url = new URL(window.location);
                url.searchParams.delete('payment');
                window.history.replaceState({}, document.title, url.pathname);
                
                // Stay on step 3 and show cancellation message
                workflowData.currentStep = 3;
                updateProgress();
                updateStepDisplay();
                
                showAlert('❌ Payment was cancelled. Please try again.', 'error');
                
                // Clear stored session ID
                localStorage.removeItem('stripeSessionId');
            }
        }

        // Enhanced provisionESIM function with better data validation
        async function provisionESIM() {
            // First, try to load any saved data
            loadWorkflowData();
            
            // Validate required data before proceeding
            if (!workflowData.userData) {
                // Try to restore user data from form fields
                if (restoreUserDataFromForm()) {
                    showAlert('⚠️ User data restored from form. Proceeding with provisioning...', 'warning');
                } else {
                    showAlert('❌ User data is missing. Please complete Step 1 first.', 'error');
                    console.error('workflowData.userData is null and cannot be restored:', workflowData);
                    
                    // Show debug information
                    console.log('Current workflow data:', workflowData);
                    console.log('Form field values:', {
                        fullName: document.getElementById('user-full-name').value,
                        phone: document.getElementById('user-phone').value,
                        email: document.getElementById('user-email').value,
                        passport: document.getElementById('user-passport').value
                    });
                    
                    return;
                }
            }

            if (!workflowData.selectedBundle) {
                // Try to restore bundle selection from available bundles
                if (workflowData.availableBundles && workflowData.availableBundles.length > 0) {
                    // If we have available bundles but no selection, try to auto-select the first one
                    workflowData.selectedBundle = workflowData.availableBundles[0];
                    saveWorkflowData();
                    showAlert('⚠️ Auto-selected first available bundle. Proceeding with provisioning...', 'warning');
                } else {
                    showAlert('❌ No bundle selected. Please complete Step 2 first.', 'error');
                    console.error('workflowData.selectedBundle is null:', workflowData);
                    
                    // Show debug information
                    console.log('Available bundles:', workflowData.availableBundles);
                    console.log('Selected bundle:', workflowData.selectedBundle);
                    
                    return;
                }
            }

            if (!workflowData.paymentData) {
                showAlert('❌ Payment data is missing. Please complete Step 3 first.', 'error');
                console.error('workflowData.paymentData is null:', workflowData);
                return;
            }

            // Debug logging
            console.log('🚀 Starting eSIM provisioning with data:', {
                userData: workflowData.userData,
                selectedBundle: workflowData.selectedBundle,
                paymentData: workflowData.paymentData
            });

            // Save current state
            saveWorkflowData();

                            const token = getAuthToken();

            const provisionLoading = document.getElementById('provision-loading');
            const provisioningStatus = document.getElementById('provisioning-status');
            const provisioningDetails = document.getElementById('esim-provisioning-details');
            
            provisionLoading.classList.remove('hidden');

            try {
                // Step 1: Validate bundle assignment (like console script)
                console.log('🔍 Validating bundle assignment...');
                provisioningStatus.innerHTML = `
                    <div class="alert alert-info">
                        <h4>🔍 Validating Bundle Assignment</h4>
                        <p>Checking if phone number already has an active bundle...</p>
                        <p><strong>Phone:</strong> ${workflowData.userData.phoneNumber}</p>
                        <p><strong>Bundle:</strong> ${workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name}</p>
                    </div>
                `;

                const validationResponse = await fetch('/api/v1/traveroam/client/validate/', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone_number: workflowData.userData.phoneNumber,
                        bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name
                    })
                });

                const validationData = await validationResponse.json();
                
                if (!validationData.success || !validationData.data.valid) {
                    throw new Error(validationData.data.message || 'Phone number already has an active bundle');
                }

                console.log('✅ Validation passed! Proceeding with eSIM provisioning...');
                provisioningStatus.innerHTML += `
                    <div class="alert alert-success">
                        <h4>✅ Validation Passed</h4>
                        <p>Phone number is eligible for eSIM assignment.</p>
                    </div>
                `;

                // Step 2: Check for duplicate assignments (like console script)
                console.log('🔍 Checking for duplicate assignments...');
                provisioningStatus.innerHTML += `
                    <div class="alert alert-info">
                        <h4>🔍 Checking for Duplicate Assignments</h4>
                        <p>Verifying no duplicate bundle assignments exist...</p>
                    </div>
                `;

                const duplicateResponse = await fetch('/api/v1/traveroam/client/validate/', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone_number: workflowData.userData.phoneNumber,
                        bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name,
                        check_duplicate: true
                    })
                });

                const duplicateData = await duplicateResponse.json();
                
                if (duplicateData.success && duplicateData.data.duplicate) {
                    throw new Error(duplicateData.data.message || 'Duplicate assignment detected');
                }

                console.log('✅ No duplicate assignments found. Proceeding with provisioning...');
                provisioningStatus.innerHTML += `
                    <div class="alert alert-success">
                        <h4>✅ No Duplicate Assignments</h4>
                        <p>No existing assignments found for this bundle.</p>
                    </div>
                `;

                // Step 3: Process order with TraveRoam (like console script)
                console.log('🚀 Provisioning eSIM with bundle:', workflowData.selectedBundle.bundle_id);
                provisioningStatus.innerHTML += `
                    <div class="alert alert-info">
                        <h4>🚀 Processing Order with TraveRoam</h4>
                        <p>This may take a few moments...</p>
                    </div>
                `;

                const response = await fetch('/api/v1/traveroam/orders/process/', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        bundle_data: workflowData.selectedBundle,
                        user_data: workflowData.userData,
                        payment_data: workflowData.paymentData
                    })
                });

                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', response.headers);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('HTTP Error:', response.status, errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('📦 Response data:', data);
                
                if (data.success) {
                    workflowData.esimData = data.data; // Enhanced response structure
                    
                    const orderRef = data.data.order_reference || 'N/A';
                    const esimDetails = data.data.esim_details || {};
                    const status = data.data.provisioning_status || 'unknown';
                    
                    console.log('✅ eSIM provisioned successfully!');
                    provisioningStatus.innerHTML += `
                        <div class="alert alert-success">
                            <h4>✅ eSIM Provisioned Successfully!</h4>
                            <p><strong>Order Reference:</strong> ${orderRef}</p>
                            <p><strong>Status:</strong> ${status}</p>
                            ${data.data.warning ? `<p class="text-warning">⚠️ ${data.data.warning}</p>` : ''}
                        </div>
                    `;

                    // Show eSIM details if available
                    if (esimDetails && Object.keys(esimDetails).length > 0) {
                        provisioningDetails.innerHTML = `
                            <div class="esim-details-card">
                                <h4>📱 eSIM Details</h4>
                                <table class="table">
                                    <tr><td>🆔 ICCID:</td><td>${esimDetails.iccid || 'N/A'}</td></tr>
                                    <tr><td>📋 Reference:</td><td>${esimDetails.reference || orderRef}</td></tr>
                                    <tr><td>📦 Bundle:</td><td>${workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name}</td></tr>
                                    <tr><td>📱 Status:</td><td>${esimDetails.status || 'Assigned'}</td></tr>
                                    <tr><td>🆔 Matching ID:</td><td>${esimDetails.matching_id || 'N/A'}</td></tr>
                                    <tr><td>🔗 SM-DP+ Address:</td><td>${esimDetails.smdp_address || 'N/A'}</td></tr>
                                    <tr><td>🔢 Activation Code:</td><td>${esimDetails.activation_code || 'N/A'}</td></tr>
                                    <tr><td>✅ Confirmation Code:</td><td>${esimDetails.confirmation_code || 'N/A'}</td></tr>
                                    <tr><td>📅 Assignment Date:</td><td>${esimDetails.assignment_date || 'N/A'}</td></tr>
                                    <tr><td>🔲 QR Code:</td><td>${esimDetails.qr_code ? 'Available ✅' : 'Will be generated during email delivery'}</td></tr>
                                </table>
                                
                                <div class="alert alert-info">
                                    <h5>📋 Complete eSIM Information:</h5>
                                    <p><strong>Order Reference:</strong> ${orderRef}</p>
                                    <p><strong>ICCID:</strong> ${esimDetails.iccid || 'N/A'}</p>
                                    <p><strong>Matching ID:</strong> ${esimDetails.matching_id || 'N/A'}</p>
                                    <p><strong>Status:</strong> ${esimDetails.status || 'Assigned'}</p>
                                    <p><strong>Bundle:</strong> ${workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name}</p>
                                    <p><strong>Data:</strong> ${workflowData.selectedBundle.dataAmount || 'N/A'}</p>
                                    <p><strong>Duration:</strong> ${workflowData.selectedBundle.duration || 'N/A'} days</p>
                                    <p><strong>Price:</strong> $${workflowData.selectedBundle.price || 'N/A'}</p>
                                </div>
                            </div>
                        `;
                    } else {
                        // Show basic information if no detailed eSIM data
                        provisioningDetails.innerHTML = `
                            <div class="esim-details-card">
                                <h4>📱 eSIM Assignment Summary</h4>
                                <table class="table">
                                    <tr><td>📋 Order Reference:</td><td>${orderRef}</td></tr>
                                    <tr><td>📦 Bundle:</td><td>${workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name}</td></tr>
                                    <tr><td>📱 Status:</td><td>${status}</td></tr>
                                    <tr><td>👤 Client:</td><td>${workflowData.userData.fullName}</td></tr>
                                    <tr><td>📧 Email:</td><td>${workflowData.userData.email}</td></tr>
                                    <tr><td>📱 Phone:</td><td>${workflowData.userData.phoneNumber}</td></tr>
                                    <tr><td>🌍 Country:</td><td>${workflowData.userData.countryOfTravel.name}</td></tr>
                                </table>
                                
                                <div class="alert alert-warning">
                                    <h5>⚠️ Note:</h5>
                                    <p>Detailed eSIM information (ICCID, QR Code, etc.) will be available during email delivery or can be retrieved from TraveRoam API.</p>
                                </div>
                            </div>
                        `;
                    }

                    // Enable next step
                    document.getElementById('next-step-4').disabled = false;
                    showAlert('✅ eSIM provisioned successfully! Ready for email delivery.', 'success');
                    
                    // Save updated workflow data
                    saveWorkflowData();
                    
                } else {
                    throw new Error(data.message || 'Failed to provision eSIM');
                }
                
            } catch (error) {
                console.error('eSIM provisioning error:', error);
                provisioningStatus.innerHTML += `
                    <div class="alert alert-error">
                        <h4>❌ Provisioning Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                showAlert('eSIM provisioning failed: ' + error.message, 'error');
            } finally {
                provisionLoading.classList.add('hidden');
            }
        }

        // Step 5: QR Code & Email Delivery (Following console script)
        async function sendESIMEmail() {
            if (!workflowData.esimData) {
                showAlert('Please provision eSIM first.', 'error');
                return;
            }

            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first.', 'error');
                return;
            }

            const emailStatus = document.getElementById('email-status');
            const qrCodeDisplay = document.getElementById('qr-code-display');
            const esimDetailsComplete = document.getElementById('esim-details-complete');
            
            // Check if elements exist before using them
            if (!emailStatus) {
                console.error('email-status element not found');
                showAlert('Email status element not found.', 'error');
                return;
            }

            // Show loading state in email status
            emailStatus.innerHTML = `
                <div class="alert alert-info">
                    <h4>📧 Sending Email...</h4>
                    <p>Please wait while we send the eSIM details to your email...</p>
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;

            try {
                // Display eSIM details first (like console script)
                console.log('📱 Displaying eSIM details...');
                
                const esimDetails = workflowData.esimData.esim_details || {};

                
                // Display complete eSIM details like console script
                esimDetailsComplete.innerHTML = `
                    <div class="complete-details">
                        <h4>📱 eSIM ASSIGNMENT COMPLETE!</h4>
                        <table class="table">
                            <tr><td>👤 Client:</td><td>${workflowData.userData.fullName}</td></tr>
                            <tr><td>📱 Phone:</td><td>${workflowData.userData.phoneNumber}</td></tr>
                            <tr><td>📧 Email:</td><td>${workflowData.userData.email}</td></tr>
                            <tr><td>🌍 Country:</td><td>${workflowData.userData.countryOfTravel.name}</td></tr>
                            <tr><td>📦 Bundle:</td><td>${workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name}</td></tr>
                            <tr><td>💰 Price:</td><td>$${workflowData.selectedBundle.price}</td></tr>
                            <tr><td>📅 Validity:</td><td>${workflowData.selectedBundle.duration} days</td></tr>
                            <tr><td>📊 Data:</td><td>${workflowData.selectedBundle.dataAmount}MB</td></tr>
                        </table>
                        
                        <h5>🔑 eSIM Details:</h5>
                        <table class="table">
                            <tr><td>🆔 ICCID:</td><td>${esimDetails.iccid || 'N/A'}</td></tr>
                            <tr><td>📋 Reference:</td><td>${esimDetails.reference || workflowData.esimData.order_reference || 'N/A'}</td></tr>
                            <tr><td>📱 Status:</td><td>${esimDetails.status || 'Assigned'}</td></tr>
                            <tr><td>🆔 Matching ID:</td><td>${esimDetails.matching_id || 'N/A'}</td></tr>
                            <tr><td>🔗 SM-DP+ Address:</td><td>${esimDetails.smdp_address || 'N/A'}</td></tr>
                            <tr><td>🔢 Activation Code:</td><td>${esimDetails.activation_code || 'N/A'}</td></tr>
                            <tr><td>✅ Confirmation Code:</td><td>${esimDetails.confirmation_code || 'N/A'}</td></tr>
                            <tr><td>📅 Assignment Date:</td><td>${esimDetails.assignment_date || 'N/A'}</td></tr>
                        </table>
                        
                        <div class="alert alert-success">
                            <h5>✅ eSIM Successfully Provisioned!</h5>
                            <p><strong>Order Reference:</strong> ${workflowData.esimData.order_reference || 'N/A'}</p>
                            <p><strong>ICCID:</strong> ${esimDetails.iccid || 'N/A'}</p>
                            <p><strong>Matching ID:</strong> ${esimDetails.matching_id || 'N/A'}</p>
                            <p><strong>Status:</strong> ${esimDetails.status || 'Assigned'}</p>
                            <p><strong>Bundle:</strong> ${workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name}</p>
                            <p><strong>Data:</strong> ${workflowData.selectedBundle.dataAmount}</p>
                            <p><strong>Duration:</strong> ${workflowData.selectedBundle.duration} days</p>
                            <p><strong>Price:</strong> $${workflowData.selectedBundle.price}</p>
                        </div>
                    </div>
                `;

                // Display QR code if available
                if (qrCodeDisplay) {
                    qrCodeDisplay.innerHTML = `
                        <div class="qr-code-section">
                            <h4>📱 eSIM QR Code</h4>
                            ${esimDetails.qr_code ? `
                                <div class="alert alert-success">
                                    <h5>🔲 QR Code Generated Successfully!</h5>
                                    <p>📱 Scan this QR code with your device to install the eSIM</p>
                                </div>
                                <div class="qr-code-container">
                                    <img src="${esimDetails.qr_code}" alt="eSIM QR Code" class="qr-code-image"/>
                                    <p class="qr-instructions">
                                        💡 Instructions:<br>
                                        1. Ensure you have a stable internet connection<br>
                                        2. Go to Settings → Cellular/Mobile Data<br>
                                        3. Tap "Add Cellular Plan" or "Add eSIM"<br>
                                        4. Scan the QR code above<br>
                                        5. Follow the on-screen instructions<br>
                                        6. Label your plan ("${workflowData.userData.countryOfTravel.name} Travel")
                                    </p>
                                </div>
                            ` : `
                                <div class="alert alert-warning">
                                    <h5>❌ QR code not available</h5>
                                    <p>QR code will be available after activation</p>
                                </div>
                            `}
                        </div>
                    `;
                }

                // Generate QR code and send email
                console.log('📧 Sending eSIM delivery email...');
                emailStatus.innerHTML = `
                    <div class="alert alert-info">
                        <h4>📧 EMAIL DELIVERY</h4>
                        <p>Sending eSIM details and invoice to client email...</p>
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `;

                const response = await makeAuthenticatedRequest('/api/v1/esim/esim-deliveries/send_delivery_email/', {
                    method: 'POST',
                    body: JSON.stringify({
                        user_data: workflowData.userData,
                        bundle_data: workflowData.selectedBundle,
                        esim_data: workflowData.esimData,
                        payment_data: workflowData.paymentData
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    console.log('✅ Email sent successfully!');
                    emailStatus.innerHTML += `
                        <div class="alert alert-success">
                            <h4>✅ Email Sent Successfully!</h4>
                            <p>📨 Email includes:</p>
                            <ul>
                                <li>• Professional HTML email with branding</li>
                                <li>• QR code for eSIM installation</li>
                                <li>• Detailed activation instructions</li>
                                <li>• Bundle details and validity information</li>
                                <li>• Support contact information</li>
                                <li>• Plain text version for compatibility</li>
                                ${workflowData.paymentData ? '<li>• PDF invoice attachment</li>' : ''}
                            </ul>
                            <p><strong>Sent to:</strong> ${workflowData.userData.email}</p>
                        </div>
                    `;

                    // Enable next step
                    const nextStep5 = document.getElementById('next-step-5');
                    if (nextStep5) {
                        nextStep5.disabled = false;
                    }
                    showAlert('Email sent successfully!', 'success');
                } else {
                    throw new Error(data.error || 'Email delivery failed');
                }
            } catch (error) {
                console.error('Email error:', error);
                emailStatus.innerHTML += `
                    <div class="alert alert-error">
                        <h4>❌ Email Delivery Failed</h4>
                        <p>${error.message}</p>
                        <p>📧 Falling back to email simulation...</p>
                        <p>✅ Email would be sent to: ${workflowData.userData.email}</p>
                        <p>📨 Email would include:</p>
                        <ul>
                            <li>• QR code for eSIM installation</li>
                            <li>• Activation instructions</li>
                            <li>• Bundle details and validity</li>
                            <li>• Support contact information</li>
                        </ul>
                    </div>
                `;
                showAlert('Email delivery failed: ' + error.message, 'error');
            }
        }

        // Step 6: Save to Database (Following console script)
        async function saveToDatabase() {
            if (!workflowData.esimData) {
                showAlert('Please complete all previous steps first.', 'error');
                return;
            }

            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first.', 'error');
                return;
            }

            const saveStatus = document.getElementById('save-status');
            const databaseSummary = document.getElementById('database-summary');
            const workflowComplete = document.getElementById('workflow-complete');

            // Display what will be saved
            databaseSummary.innerHTML = `
                <div class="alert alert-info">
                    <h4>�� Saving to Database</h4>
                    <p>The following data will be saved:</p>
                    <ul>
                        <li>✅ User/Client record</li>
                        <li>✅ eSIM record with TraveRoam details</li>
                        <li>✅ Order record with bundle information</li>
                        <li>✅ Payment record with Stripe details</li>
                        <li>✅ Reseller relationship</li>
                    </ul>
                </div>
            `;

            try {
                // Save all data to database
                const response = await makeAuthenticatedRequest('/api/v1/workflow/save-complete/', {
                    method: 'POST',
                    body: JSON.stringify({
                        user_data: workflowData.userData,
                        bundle_data: workflowData.selectedBundle,
                        esim_data: workflowData.esimData,
                        payment_data: workflowData.paymentData
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    saveStatus.innerHTML = `
                        <div class="alert alert-success">
                            <h4>✅ Data Saved Successfully</h4>
                            <p>All workflow data has been saved to the database:</p>
                            <ul>
                                <li>Client ID: ${data.client_id}</li>
                                <li>eSIM ID: ${data.esim_id}</li>
                                <li>Order ID: ${data.order_id}</li>
                                <li>Payment ID: ${data.payment_id}</li>
                            </ul>
                        </div>
                    `;

                    // Show workflow complete
                    workflowComplete.style.display = 'block';
                    showAlert('🎉 Workflow completed successfully! All data saved to database.', 'success');
                } else {
                    throw new Error(data.error || 'Database save failed');
                }
            } catch (error) {
                console.error('Save error:', error);
                saveStatus.innerHTML = `
                    <div class="alert alert-error">
                        <h4>❌ Database Save Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                showAlert('Database save failed: ' + error.message, 'error');
            }
        }

        // Function to clear workflow data
        function clearWorkflowData() {
            try {
                localStorage.removeItem('esim_workflow_data');
                localStorage.removeItem('stripeSessionId');
                localStorage.removeItem('paymentDetails');
                sessionStorage.removeItem('paymentReturnProcessed');
                console.log('✅ Workflow data cleared from localStorage');
            } catch (error) {
                console.error('Failed to clear workflow data:', error);
            }
        }

        // Reset workflow
        function resetWorkflow() {
            workflowData = {
                userData: null,
                selectedBundle: null,
                paymentData: null,
                esimData: null,
                currentStep: 1
            };

            // Clear all stored data
            clearWorkflowData();

            // Reset form fields
            document.getElementById('user-full-name').value = '';
            document.getElementById('user-phone').value = '';
            document.getElementById('user-email').value = '';
            document.getElementById('user-passport').value = '';
            document.getElementById('user-travel-date').value = '';

            // Reset UI elements
            document.getElementById('country-detection').innerHTML = '';
            document.getElementById('country-info-display').innerHTML = '';
            document.getElementById('plans-container').innerHTML = '';
            document.getElementById('payment-summary').innerHTML = '';
            document.getElementById('provisioning-status').innerHTML = '';
            document.getElementById('esim-provisioning-details').innerHTML = '';
            document.getElementById('qr-code-display').innerHTML = '';
            document.getElementById('email-status').innerHTML = '';

            // Reset step buttons
            document.getElementById('next-step-1').disabled = true;
            document.getElementById('next-step-2').disabled = true;
            document.getElementById('next-step-3').disabled = true;
            document.getElementById('next-step-4').disabled = true;
            document.getElementById('next-step-5').disabled = true;

            // Reset progress
            workflowData.currentStep = 1;
            updateProgress();
            updateStepDisplay();

            showAlert('✅ Workflow reset successfully. You can start over.', 'success');
        }

        // Client management - using correct single endpoint
        async function loadClients() {
            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first to load data.', 'error');
                return;
            }

            try {
                // Use the correct reseller clients endpoint
                const response = await fetch('/api/v1/esim/reseller/clients/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (data.success && data.data) {
                    displayClients(data.data);
                    updateClientStats(data.data);
                    console.log(`Loaded ${data.data.length} clients from reseller endpoint`);
                } else {
                    showAlert('No clients found in the database.', 'warning');
                }
            } catch (error) {
                console.error('Error loading clients:', error);
                showAlert('Failed to load clients from server. Please try again.', 'error');
            }
        }

        function displayClients(clients) {
            const clientList = document.getElementById('client-list');
            clientList.innerHTML = '';

            clients.forEach(client => {
                const clientItem = document.createElement('div');
                clientItem.className = 'client-item';
                clientItem.onclick = () => selectClient(client);
                clientItem.innerHTML = `
                    <div class="client-name">${client.full_name}</div>
                    <div class="client-details">
                        📧 ${client.email} | 📱 ${client.phone_number}
                        ${client.country_of_travel ? `| 🌍 ${client.country_of_travel.name || client.country_of_travel}` : ''}
                    </div>
                `;
                clientList.appendChild(clientItem);
            });
        }

        function selectClient(client) {
            selectedClient = client;
            
            // Update UI
            document.querySelectorAll('.client-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.client-item').classList.add('selected');
            
            document.getElementById('next-step-1').disabled = false;
            
            // Update summary
            if (document.getElementById('selected-client-info')) {
                document.getElementById('selected-client-info').innerHTML = `
                    <strong>${client.full_name}</strong><br>
                    📧 ${client.email}<br>
                    📱 ${client.phone_number}
                `;
            }
        }

        function updateClientStats(clients) {
            document.getElementById('total-clients').textContent = clients.length;
            document.getElementById('active-clients').textContent = clients.filter(c => c.status === 'active').length;
            document.getElementById('pending-esims').textContent = clients.filter(c => c.esims && c.esims.length > 0).length;
        }

        // Load additional data from database - using correct single endpoints
        async function loadAdditionalData() {
            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first to load data.', 'error');
                return;
            }

            try {
                // Load orders
                const ordersResponse = await fetch('/api/v1/orders/orders/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const ordersData = await ordersResponse.json();
                if (ordersData.success) {
                    console.log(`Loaded ${ordersData.data.length} orders`);
                    document.getElementById('total-orders').textContent = ordersData.data.length;
                }

                // Load payments
                const paymentsResponse = await fetch('/api/v1/payments/payments/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const paymentsData = await paymentsResponse.json();
                if (paymentsData.success) {
                    console.log(`Loaded ${paymentsData.data.length} payments`);
                    document.getElementById('total-payments').textContent = paymentsData.data.length;
                }

                // Load reports
                const reportsResponse = await fetch('/api/v1/reports/reports/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const reportsData = await reportsResponse.json();
                if (reportsData.success) {
                    console.log(`Loaded ${reportsData.data.length} reports`);
                }

                // Load resellers
                const resellersResponse = await fetch('/api/v1/resellers/resellers/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const resellersData = await resellersResponse.json();
                if (resellersData.success) {
                    console.log(`Loaded ${resellersData.data.length} resellers`);
                    document.getElementById('total-resellers').textContent = resellersData.data.length;
                }

                // Load eSIM usage data
                const usageResponse = await fetch('/api/v1/esim/esim-usage/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const usageData = await usageResponse.json();
                if (usageData.success) {
                    console.log(`Loaded ${usageData.data.length} eSIM usage records`);
                }

                // Load eSIM deliveries
                const deliveryResponse = await fetch('/api/v1/esim/esim-deliveries/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const deliveryData = await deliveryResponse.json();
                if (deliveryData.success) {
                    console.log(`Loaded ${deliveryData.data.length} eSIM deliveries`);
                }

                showAlert('All data loaded successfully!', 'success');

            } catch (error) {
                console.error('Error loading additional data:', error);
                showAlert('Error loading additional data: ' + error.message, 'error');
            }
        }

        // Plan management - using correct single endpoint
        async function loadPlans() {
            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first to load data.', 'error');
                return;
            }

            try {
                // Use the correct reseller plans endpoint
                const response = await fetch('/api/v1/esim/reseller/plans/', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (data.success && data.data) {
                    displayPlans(data.data);
                    console.log(`Loaded ${data.data.length} plans from reseller plans endpoint`);
                } else {
                    showAlert('No eSIM plans found in the database.', 'warning');
                }
            } catch (error) {
                console.error('Error loading plans:', error);
                showAlert('Failed to load plans from server. Please try again.', 'error');
            }
        }

        function displayPlans(plans) {
            const planGrid = document.getElementById('plan-grid');
            planGrid.innerHTML = '';

            plans.forEach(plan => {
                const planCard = document.createElement('div');
                planCard.className = 'plan-card';
                planCard.onclick = () => selectPlan(plan);
                
                // Handle both TraveRoam and local plan formats
                const planName = plan.name || plan.description || plan.id;
                const planCountry = plan.country || (plan.countries && plan.countries[0] && plan.countries[0].name) || 'Global';
                const planData = plan.data_volume || (plan.data_amount_mb ? `${plan.data_amount_mb}MB` : 'Unknown');
                const planDuration = plan.validity_days || plan.duration_days || plan.duration || 'Unknown';
                const planPrice = plan.public_price || plan.price || '0.00';
                
                planCard.innerHTML = `
                    <div class="plan-name">${planName}</div>
                    <div class="plan-details">
                        🌍 ${planCountry} | 📊 ${planData} | ⏱️ ${planDuration} days
                    </div>
                    <div class="plan-price">$${planPrice}</div>
                `;
                planGrid.appendChild(planCard);
            });
        }

        function selectPlan(plan) {
            selectedPlan = plan;
            
            // Update UI
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.plan-card').classList.add('selected');
            
            document.getElementById('next-step-2').disabled = false;
            
            // Update summary with proper plan data
            if (document.getElementById('selected-plan-info')) {
                const planName = plan.name || plan.description || plan.id;
                const planCountry = plan.country || (plan.countries && plan.countries[0] && plan.countries[0].name) || 'Global';
                const planData = plan.data_volume || (plan.data_amount_mb ? `${plan.data_amount_mb}MB` : 'Unknown');
                const planDuration = plan.validity_days || plan.duration_days || plan.duration || 'Unknown';
                const planPrice = plan.public_price || plan.price || '0.00';
                
                document.getElementById('selected-plan-info').innerHTML = `
                    <strong>${planName}</strong><br>
                    🌍 ${planCountry} | 📊 ${planData}<br>
                    ⏱️ ${planDuration} days | 💰 $${planPrice}
                `;
            }
        }

        // eSIM Assignment - using correct single endpoint
        async function assignESIM() {
            if (!selectedClient || !selectedPlan) {
                showAlert('Please select both client and plan', 'error');
                return;
            }

            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first to assign eSIM.', 'error');
                return;
            }

            const assignBtn = document.getElementById('assign-esim-btn');
            const loading = document.getElementById('assign-loading');
            
            assignBtn.disabled = true;
            loading.classList.remove('hidden');

            try {
                // Use the correct reseller eSIM assignment endpoint
                const assignmentData = {
                    bundle_name: selectedPlan.id || selectedPlan.name,
                    notes: document.getElementById('assignment-notes').value
                };

                console.log('Assigning eSIM with data:', assignmentData);

                const response = await fetch(`/api/v1/esim/reseller/clients/${selectedClient.id}/assign_esim/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify(assignmentData)
                });

                const data = await response.json();
                
                if (data.success) {
                    assignedESIM = {
                        id: data.esim_id,
                        status: 'provisioned',
                        plan: selectedPlan,
                        client: selectedClient,
                        order_reference: data.order_reference,
                        iccid: data.iccid,
                        qr_code: data.qr_code_available ? data.qr_code : null
                    };
                    
                    showAlert('eSIM assigned successfully!', 'success');
                    nextStep();
                    loadESIMDetails();
                } else {
                    showAlert('Assignment failed: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error assigning eSIM:', error);
                showAlert('eSIM assignment failed. Please try again.', 'error');
            } finally {
                assignBtn.disabled = false;
                loading.classList.add('hidden');
            }
        }

        async function loadESIMDetails() {
            if (!assignedESIM) return;

            const token = getAuthToken();
            if (!token) {
                showAlert('Please login first to load eSIM details.', 'error');
                return;
            }

            try {
                // Use the correct reseller eSIM details endpoint
                const response = await fetch(`/api/v1/esim/reseller/esims/${assignedESIM.id}/`, {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (data.success && data.data) {
                    displayESIMDetails(data.data);
                    console.log('Loaded eSIM details from reseller endpoint');
                } else {
                    showAlert('Failed to load eSIM details. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Error loading eSIM details:', error);
                showAlert('Failed to load eSIM details from server. Please try again.', 'error');
            }
        }

        function displayESIMDetails(esim) {
            document.getElementById('esim-status').textContent = esim.status || 'pending';
            document.getElementById('esim-id').textContent = `ID: ${esim.id || 'N/A'}`;
            
            const detailsDiv = document.getElementById('esim-details');
            const planName = esim.plan ? (esim.plan.name || esim.plan.description || esim.plan.id) : 'Unknown';
            const clientName = esim.client ? (esim.client.full_name || esim.client.name) : 'Unknown';
            const createdDate = esim.created_at ? new Date(esim.created_at).toLocaleString() : 'N/A';
            
            detailsDiv.innerHTML = `
                <strong>eSIM Details:</strong><br>
                Status: ${esim.status || 'pending'}<br>
                Plan: ${planName}<br>
                Client: ${clientName}<br>
                Created: ${createdDate}<br>
                ICCID: ${esim.iccid || esim.traveroam_esim_id || 'N/A'}<br>
                Order Reference: ${esim.traveroam_order_reference || esim.order_reference || 'N/A'}<br>
                ${esim.qr_code ? 'QR Code: Available' : 'QR Code: Generating...'}
            `;

            if (esim.qr_code) {
                document.getElementById('qr-code').src = `data:image/png;base64,${esim.qr_code}`;
                document.getElementById('qr-code').classList.remove('hidden');
                document.getElementById('qr-loading').classList.add('hidden');
            } else {
                document.getElementById('qr-loading').classList.remove('hidden');
                document.getElementById('qr-code').classList.add('hidden');
            }
        }

        async function refreshStatus() {
            if (assignedESIM) {
                await loadESIMDetails();
                showAlert('Status refreshed', 'info');
            }
        }

        function startNewAssignment() {
            // Reset everything
            selectedClient = null;
            selectedPlan = null;
            assignedESIM = null;
            currentStep = 1;
            
            // Reset UI
            document.querySelectorAll('.client-item').forEach(item => item.classList.remove('selected'));
            document.querySelectorAll('.plan-card').forEach(card => card.classList.remove('selected'));
            document.getElementById('next-step-1').disabled = true;
            document.getElementById('next-step-2').disabled = true;
            document.getElementById('assignment-notes').value = '';
            
            // Go to first step
            updateStepDisplay();
            updateProgress();
            
            // Reload data
            loadClients();
            loadPlans();
        }

        // Utility functions
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            const container = document.querySelector('.main-content');
            container.insertBefore(alertDiv, container.firstChild);
            
            setTimeout(() => alertDiv.remove(), 5000);
        }

        // Add login function for testing
        async function loginForTesting() {
            const loginBtn = document.getElementById('login-btn');
            const loginStatus = document.getElementById('login-status');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '🔄 Logging in...';
            loginStatus.textContent = '';
            
            try {
                const response = await fetch('/api/v1/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                console.log('Login response:', data); // Debug log
                
                if (data.success && data.data && data.data.tokens && data.data.tokens.access) {
                    localStorage.setItem('auth_token', data.data.tokens.access);
                    if (data.data.tokens.refresh) {
                        localStorage.setItem('refresh_token', data.data.tokens.refresh);
                    }
                    loginStatus.textContent = '✅ Logged in successfully';
                    loginStatus.style.color = '#4caf50';
                    showAlert('Login successful!', 'success');
                    
                    // Update UI
                    updateLoginUI(true);
                    
                    // Load data after successful login
                    loadClients();
                    loadPlans();
                    loadAdditionalData();
                    return true;
                } else {
                    loginStatus.textContent = '❌ Login failed: ' + (data.message || 'Unknown error');
                    loginStatus.style.color = '#f44336';
                    showAlert('Login failed: ' + (data.message || 'Unknown error'), 'error');
                    return false;
                }
            } catch (error) {
                console.error('Login error:', error);
                loginStatus.textContent = '❌ Login failed: Network error';
                loginStatus.style.color = '#f44336';
                showAlert('Login failed: Network error. Please check your connection.', 'error');
                return false;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '🔐 Login (<EMAIL> / admin123)';
            }
        }

        // Add token refresh function
        async function refreshToken() {
            const refreshToken = localStorage.getItem('refresh_token');
            if (!refreshToken) {
                return false;
            }

            try {
                const response = await fetch('/api/v1/auth/refresh/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        refresh: refreshToken
                    })
                });
                
                const data = await response.json();
                if (data.success && data.data && data.data.access) {
                    localStorage.setItem('auth_token', data.data.access);
                    return true;
                } else {
                    // Clear invalid tokens
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('refresh_token');
                    return false;
                }
            } catch (error) {
                console.error('Token refresh error:', error);
                localStorage.removeItem('auth_token');
                localStorage.removeItem('refresh_token');
                return false;
            }
        }

        // Add logout function
        function logout() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('refresh_token');
            
            // Update UI
            document.getElementById('login-btn').style.display = 'inline-block';
            document.getElementById('logout-btn').style.display = 'none';
            document.getElementById('login-status').textContent = '🚪 Logged out';
            document.getElementById('login-status').style.color = '#6c757d';
            
            // Reset data
            selectedClient = null;
            selectedPlan = null;
            assignedESIM = null;
            currentStep = 1;
            
            // Reset UI
            document.querySelectorAll('.client-item').forEach(item => item.classList.remove('selected'));
            document.querySelectorAll('.plan-card').forEach(card => card.classList.remove('selected'));
            document.getElementById('next-step-1').disabled = true;
            document.getElementById('next-step-2').disabled = true;
            document.getElementById('assignment-notes').value = '';
            
            // Go to first step
            updateStepDisplay();
            updateProgress();
            
            showAlert('Logged out successfully', 'info');
        }

        // Update login success to show logout button
        function updateLoginUI(isLoggedIn) {
            if (isLoggedIn) {
                document.getElementById('login-btn').style.display = 'none';
                document.getElementById('logout-btn').style.display = 'inline-block';
            } else {
                document.getElementById('login-btn').style.display = 'inline-block';
                document.getElementById('logout-btn').style.display = 'none';
            }
        }

        // Removed sample data functions - now using backend APIs only

        // Search functionality
        const clientSearch = document.getElementById('client-search');
        if (clientSearch) {
            clientSearch.addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                document.querySelectorAll('.client-item').forEach(item => {
                    const text = item.textContent.toLowerCase();
                    item.style.display = text.includes(searchTerm) ? 'block' : 'none';
                });
            });
        }

        // Country filter
        const countryFilter = document.getElementById('country-filter');
        if (countryFilter) {
            countryFilter.addEventListener('change', function(e) {
                const selectedCountry = e.target.value;
                document.querySelectorAll('.plan-card').forEach(card => {
                    const country = card.querySelector('.plan-details').textContent;
                    card.style.display = !selectedCountry || country.includes(selectedCountry) ? 'block' : 'none';
                });
            });
        }

        // Function to restore user data from form fields if needed
        function restoreUserDataFromForm() {
            const fullName = document.getElementById('user-full-name').value.trim();
            const phone = document.getElementById('user-phone').value.trim();
            const email = document.getElementById('user-email').value.trim();
            const passport = document.getElementById('user-passport').value.trim();
            const travelDate = document.getElementById('user-travel-date').value;

            if (fullName && phone && email && passport) {
                // Create a basic user data object from form fields
                workflowData.userData = {
                    fullName: fullName,
                    phoneNumber: phone,
                    email: email,
                    passportId: passport,
                    travelDate: travelDate || null,
                    countryOfTravel: workflowData.userData?.countryOfTravel || {
                        name: 'Unknown',
                        code: 'UN',
                        region: 'Unknown'
                    }
                };
                
                console.log('✅ User data restored from form fields:', workflowData.userData);
                return true;
            }
            
            return false;
        }

        // Function to save workflow data to localStorage for persistence
        function saveWorkflowData() {
            try {
                // Save to localStorage with a specific key
                localStorage.setItem('esim_workflow_data', JSON.stringify(workflowData));
                console.log('✅ Workflow data saved to localStorage:', workflowData);
            } catch (error) {
                console.error('Failed to save workflow data:', error);
            }
        }

        // Function to load workflow data from localStorage
        function loadWorkflowData() {
            try {
                const saved = localStorage.getItem('esim_workflow_data');
                if (saved) {
                    const parsed = JSON.parse(saved);
                    // Merge with current workflowData to preserve any existing data
                    workflowData = { ...workflowData, ...parsed };
                    console.log('✅ Workflow data loaded from localStorage:', workflowData);
                    
                    // Restore form fields if user data exists
                    if (workflowData.userData) {
                        restoreFormFieldsFromData();
                    }
                    
                    return true;
                }
            } catch (error) {
                console.error('Failed to load workflow data:', error);
            }
            return false;
        }

        // Function to restore form fields from saved data
        function restoreFormFieldsFromData() {
            if (workflowData.userData) {
                const userData = workflowData.userData;
                
                // Restore form fields
                if (userData.fullName) {
                    document.getElementById('user-full-name').value = userData.fullName;
                }
                if (userData.phoneNumber) {
                    document.getElementById('user-phone').value = userData.phoneNumber;
                }
                if (userData.email) {
                    document.getElementById('user-email').value = userData.email;
                }
                if (userData.passportId) {
                    document.getElementById('user-passport').value = userData.passportId;
                }
                if (userData.travelDate) {
                    document.getElementById('user-travel-date').value = userData.travelDate;
                }
                
                console.log('✅ Form fields restored from saved data');
            }
        }

        // Enhanced function to validate and save user data
        async function validateUserData() {
            const fullName = document.getElementById('user-full-name').value.trim();
            const phone = document.getElementById('user-phone').value.trim();
            const email = document.getElementById('user-email').value.trim();
            const passport = document.getElementById('user-passport').value.trim();
            const travelDate = document.getElementById('user-travel-date').value;

            // Validation
            if (fullName.length < 2) {
                showAlert('Name must be at least 2 characters long.', 'error');
                return;
            }

            if (!validatePhone(phone)) {
                showAlert('Please enter a valid phone number with country code (e.g., +92XXXXXXXXX)', 'error');
                return;
            }

            if (!validateEmail(email)) {
                showAlert('Please enter a valid email address.', 'error');
                return;
            }

            if (passport.length < 3) {
                showAlert('Please enter a valid passport/ID number.', 'error');
                return;
            }

            try {
                // Detect country using backend API and validate eSIM eligibility
                const countryInfo = await detectCountryFromPhone(phone, true);
                
                // Store user data
                workflowData.userData = {
                    fullName: fullName,
                    phoneNumber: phone,
                    email: email,
                    passportId: passport,
                    countryOfTravel: countryInfo,
                    travelDate: travelDate || null
                };

                // Save to localStorage immediately
                saveWorkflowData();

                // Show country detection result
                document.getElementById('country-detection').innerHTML = `
                    <div class="alert alert-success">
                        🌍 Detected Country: ${countryInfo.name} (${countryInfo.code})
                        <br>📍 Region: ${countryInfo.region}
                    </div>
                `;

                // Enable next step
                document.getElementById('next-step-1').disabled = false;
                showAlert('User data validated successfully!', 'success');
                
            } catch (error) {
                console.error('User validation error:', error);
                document.getElementById('country-detection').innerHTML = `
                    <div class="alert alert-error">
                        ❌ ${error.message || 'Validation failed'}
                    </div>
                `;
                
                // Show specific error message for eSIM validation failures
                if (error.message && (error.message.includes('active eSIM') || error.message.includes('bundle'))) {
                    showAlert('❌ ' + error.message, 'error');
                } else {
                    showAlert('Validation failed: ' + (error.message || 'Please check your phone number and try again.'), 'warning');
                }
                
                // Don't enable next step on validation failure
                document.getElementById('next-step-1').disabled = true;
            }
        }

        // Debug function to show current workflow data
        function debugWorkflowData() {
            console.log('🔍 Current Workflow Data:', workflowData);
            console.log('📝 Form Field Values:', {
                fullName: document.getElementById('user-full-name').value,
                phone: document.getElementById('user-phone').value,
                email: document.getElementById('user-email').value,
                passport: document.getElementById('user-passport').value,
                travelDate: document.getElementById('user-travel-date').value
            });
            console.log('💾 LocalStorage Data:', {
                workflowData: localStorage.getItem('esim_workflow_data'),
                stripeSessionId: localStorage.getItem('stripeSessionId'),
                paymentDetails: localStorage.getItem('paymentDetails')
            });
            
            // Show debug info in alert
            const debugInfo = `
🔍 Workflow Debug Info:

User Data: ${workflowData.userData ? '✅ Present' : '❌ Missing'}
Bundle: ${workflowData.selectedBundle ? '✅ Present' : '❌ Missing'}
Payment: ${workflowData.paymentData ? '✅ Present' : '❌ Missing'}
eSIM: ${workflowData.esimData ? '✅ Present' : '❌ Missing'}

Current Step: ${workflowData.currentStep}

Form Fields:
- Name: ${document.getElementById('user-full-name').value || 'Empty'}
- Phone: ${document.getElementById('user-phone').value || 'Empty'}
- Email: ${document.getElementById('user-email').value || 'Empty'}
- Passport: ${document.getElementById('user-passport').value || 'Empty'}
            `;
            
            showAlert(debugInfo, 'info');
        }

        // Function to manually restore data from form
        function manualRestoreData() {
            if (restoreUserDataFromForm()) {
                showAlert('✅ User data manually restored from form fields', 'success');
                debugWorkflowData();
            } else {
                showAlert('❌ Cannot restore data - form fields are empty', 'error');
            }
        }

        // Function to manually restore bundle selection
        function manualRestoreBundle() {
            if (workflowData.availableBundles && workflowData.availableBundles.length > 0) {
                if (!workflowData.selectedBundle) {
                    // Auto-select first bundle
                    workflowData.selectedBundle = workflowData.availableBundles[0];
                    saveWorkflowData();
                    showAlert('✅ Bundle auto-selected from available bundles', 'success');
                    return true;
                } else {
                    // Try to restore existing selection
                    const restored = restoreBundleSelection();
                    if (restored) {
                        showAlert('✅ Bundle selection restored', 'success');
                        return true;
                    } else {
                        showAlert('⚠️ Could not restore bundle selection, auto-selecting first bundle', 'warning');
                        workflowData.selectedBundle = workflowData.availableBundles[0];
                        saveWorkflowData();
                        return true;
                    }
                }
            } else {
                showAlert('❌ No available bundles to restore', 'error');
                return false;
            }
        }
    </script>
</body>
</html>
