# Generated by Django 4.2.7 on 2025-08-12 07:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("orders", "0002_remove_historicalorder_public_user_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("resellers", "0004_historicalreseller_suspension_reason_and_more"),
        ("esim_management", "0002_remove_esim_public_user_and_more"),
        ("clients", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="historicalpublicuser",
            name="history_user",
        ),
        migrations.RemoveField(
            model_name="historicalpublicuser",
            name="user",
        ),
        migrations.RemoveField(
            model_name="publicuser",
            name="user",
        ),
        migrations.RemoveField(
            model_name="publicuseractivity",
            name="public_user",
        ),
        migrations.RemoveField(
            model_name="ticketresponse",
            name="ticket",
        ),
        migrations.RemoveField(
            model_name="ticketresponse",
            name="user",
        ),
        migrations.AlterModelOptions(
            name="client",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Client",
                "verbose_name_plural": "Clients",
            },
        ),
        migrations.RemoveField(
            model_name="supportticket",
            name="priority",
        ),
        migrations.RemoveField(
            model_name="supportticket",
            name="public_user",
        ),
        migrations.AddField(
            model_name="client",
            name="address",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="admin_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="admin_override_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="admin_override_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="client_overrides",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="admin_override_reason",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="auto_renewal",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="client",
            name="city",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="client_type",
            field=models.CharField(
                choices=[
                    ("reseller_client", "Reseller Client"),
                    ("direct_user", "Direct User"),
                ],
                default="direct_user",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="country",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="current_plan",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="last_activity",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="last_login",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="plan_end_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="plan_start_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="preferred_network",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="preferred_package",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("inactive", "Inactive"),
                    ("suspended", "Suspended"),
                    ("blocked", "Blocked"),
                    ("pending_verification", "Pending Verification"),
                ],
                default="active",
                max_length=25,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="tier",
            field=models.CharField(
                choices=[
                    ("basic", "Basic"),
                    ("premium", "Premium"),
                    ("enterprise", "Enterprise"),
                ],
                default="basic",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="total_logins",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="address",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="admin_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="admin_override_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="admin_override_by",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="admin_override_reason",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="auto_renewal",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="city",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="client_type",
            field=models.CharField(
                choices=[
                    ("reseller_client", "Reseller Client"),
                    ("direct_user", "Direct User"),
                ],
                default="direct_user",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="country",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="current_plan",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="last_activity",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="last_login",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="plan_end_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="plan_start_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="preferred_network",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="preferred_package",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("inactive", "Inactive"),
                    ("suspended", "Suspended"),
                    ("blocked", "Blocked"),
                    ("pending_verification", "Pending Verification"),
                ],
                default="active",
                max_length=25,
            ),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="tier",
            field=models.CharField(
                choices=[
                    ("basic", "Basic"),
                    ("premium", "Premium"),
                    ("enterprise", "Enterprise"),
                ],
                default="basic",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="historicalclient",
            name="total_logins",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="client",
            name="reseller",
            field=models.ForeignKey(
                blank=True,
                help_text="Required for reseller clients",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="clients",
                to="resellers.reseller",
            ),
        ),
        migrations.AlterField(
            model_name="historicalclient",
            name="reseller",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                help_text="Required for reseller clients",
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="resellers.reseller",
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="client",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="support_tickets",
                to="clients.client",
            ),
            preserve_default=False,
        ),
        migrations.DeleteModel(
            name="ClientActivity",
        ),
        migrations.DeleteModel(
            name="HistoricalPublicUser",
        ),
        migrations.DeleteModel(
            name="PublicUser",
        ),
        migrations.DeleteModel(
            name="PublicUserActivity",
        ),
        migrations.DeleteModel(
            name="TicketResponse",
        ),
    ]
