amqp==5.3.1
asgiref==3.8.1
async-timeout==5.0.1
backports.zoneinfo==0.2.1
billiard==4.2.1
black==23.11.0
cachecontrol==0.14.2
cachetools==5.5.2
celery==5.3.4
certifi==2025.8.3
cfgv==3.4.0
charset-normalizer==3.4.2
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1.2
click-repl==0.3.0
coverage==7.3.2
crispy-bootstrap5==0.7
distlib==0.4.0
Django==4.2.7
django-celery-results==2.6.0
django-cors-headers==4.3.1
django-crispy-forms==2.0
django-environ==0.11.2
django-extensions==3.2.3
django-filter==23.3
django-simple-history==3.4.0
django-storages==1.14.2
djangorestframework==3.14.0
djangorestframework-simplejwt==5.3.0
drf-yasg==1.21.7
exceptiongroup==1.3.0
execnet==2.1.1
factory-boy==3.3.0
Faker==20.1.0
filelock==3.16.1
firebase-admin==6.2.0
flake8==6.1.0
flake8-docstrings==1.7.0
freezegun==1.2.2
google-api-core==2.25.1
google-api-python-client==2.177.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-cloud-core==2.4.3
google-cloud-firestore==2.21.0
google-cloud-storage==3.2.0
google-crc32c==1.5.0
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
grpcio==1.70.0
grpcio-status==1.70.0
httplib2==0.22.0
httpretty==1.1.4
identify==2.6.1
idna==3.10
inflection==0.5.1
iniconfig==2.1.0
isort==5.12.0
kombu==5.5.4
mccabe==0.7.0
msgpack==1.1.1
mypy-extensions==1.1.0
nodeenv==1.9.1
packaging==25.0
pathspec==0.12.1
Pillow==10.1.0
platformdirs==4.3.6
pluggy==1.5.0
pre-commit==3.5.0
prompt-toolkit==3.0.51
proto-plus==1.26.1
protobuf==5.29.5
psycopg2-binary==2.9.7
pyasn1==0.6.1
pyasn1-modules==0.4.2
pycodestyle==2.11.1
pydocstyle==6.3.0
pyflakes==3.1.0
PyJWT==2.9.0
pyparsing==3.1.4
pypng==0.20220715.0
pytest==7.4.3
pytest-cov==4.1.0
pytest-django==4.7.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.0
pytz==2025.2
PyYAML==6.0.2
qrcode==7.4.2
redis==5.0.1
reportlab==4.0.7
requests==2.32.4
responses==0.24.1
rsa==4.9.1
six==1.17.0
snowballstemmer==3.0.1
sqlparse==0.5.3
stripe==12.4.0
tomli==2.2.1
typing-extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.2.3
vine==5.1.0
virtualenv==20.33.1
wcwidth==0.2.13
