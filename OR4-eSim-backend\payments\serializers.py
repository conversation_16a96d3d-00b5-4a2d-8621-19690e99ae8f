from django_filters import rest_framework as filters
from rest_framework import serializers

from .models import Payment, StripeWebhook


class PaymentFilter(filters.FilterSet):
    """Filter for payments"""

    status = filters.CharFilter(field_name="status")
    payment_type = filters.CharFilter(field_name="payment_type")
    payment_method = filters.CharFilter(field_name="payment_method")
    date_created = filters.DateFromToRangeFilter(field_name="created_at")
    amount_min = filters.NumberFilter(field_name="amount", lookup_expr="gte")
    amount_max = filters.NumberFilter(field_name="amount", lookup_expr="lte")
    customer_name = filters.CharFilter(
        field_name="order__client__full_name", lookup_expr="icontains"
    )
    reseller_email = filters.CharFilter(
        field_name="order__reseller__user__email", lookup_expr="icontains"
    )
    order_number = filters.Char<PERSON>ilter(
        field_name="order__order_number", lookup_expr="icontains"
    )
    transaction_id = filters.CharFilter(
        field_name="transaction_id", lookup_expr="icontains"
    )
    requires_approval = filters.BooleanFilter(method="filter_requires_approval")

    def filter_requires_approval(self, queryset, name, value):
        if value:
            return queryset.filter(status=Payment.PaymentStatus.MANUAL_APPROVAL)
        return queryset

    class Meta:
        model = Payment
        fields = [
            "status",
            "payment_type",
            "payment_method",
            "date_created",
            "amount_min",
            "amount_max",
            "customer_name",
            "reseller_email",
            "order_number",
            "transaction_id",
            "requires_approval",
        ]


class PaymentSerializer(serializers.ModelSerializer):
    """Payment serializer"""

    order = serializers.SerializerMethodField()
    customer_name = serializers.ReadOnlyField()
    reseller_name = serializers.ReadOnlyField()
    requires_manual_approval = serializers.ReadOnlyField()

    class Meta:
        model = Payment
        fields = [
            "id",
            "order",
            "amount",
            "currency",
            "payment_method",
            "payment_type",
            "status",
            "transaction_id",
            "gateway_transaction_id",
            "gateway_response",
            "manual_payment_proof",
            "manual_payment_notes",
            "manual_approved_by",
            "manual_approved_at",
            "manual_rejection_reason",
            "refund_amount",
            "refund_reason",
            "refund_approved_by",
            "refund_approved_at",
            "invoice_number",
            "invoice_generated",
            "invoice_generated_at",
            "invoice_file",
            "created_at",
            "updated_at",
            "processed_at",
            "completed_at",
            "is_refunded",
            "net_amount",
            "customer_name",
            "reseller_name",
            "requires_manual_approval",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "is_refunded",
            "net_amount",
            "customer_name",
            "reseller_name",
            "requires_manual_approval",
        ]

    def get_order(self, obj):
        from orders.serializers import OrderSerializer

        return OrderSerializer(obj.order).data


class PaymentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating payments"""

    class Meta:
        model = Payment
        fields = [
            "order",
            "amount",
            "currency",
            "payment_method",
            "payment_type",
            "transaction_id",
            "gateway_transaction_id",
            "gateway_response",
            "manual_payment_proof",
            "manual_payment_notes",
        ]


class PaymentUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating payments"""

    class Meta:
        model = Payment
        fields = [
            "status",
            "payment_type",
            "gateway_transaction_id",
            "gateway_response",
            "processed_at",
            "completed_at",
            "manual_payment_notes",
        ]


class ManualPaymentApprovalSerializer(serializers.Serializer):
    """Serializer for manual payment approval"""

    approved = serializers.BooleanField()
    notes = serializers.CharField(required=False, allow_blank=True)
    rejection_reason = serializers.CharField(required=False, allow_blank=True)

    def validate(self, data):
        approved = data.get("approved")
        rejection_reason = data.get("rejection_reason")

        if not approved and not rejection_reason:
            raise serializers.ValidationError(
                "Rejection reason is required when rejecting a payment"
            )

        return data


class RefundCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating refunds"""

    class Meta:
        model = Payment
        fields = ["refund_amount", "refund_reason"]

    def validate_refund_amount(self, value):
        if value > self.instance.amount:
            raise serializers.ValidationError(
                "Refund amount cannot exceed payment amount"
            )
        return value


class PaymentDashboardSerializer(serializers.Serializer):
    """Payment dashboard serializer"""

    payment = PaymentSerializer()
    stats = serializers.DictField()


class PaymentStatisticsSerializer(serializers.Serializer):
    """Payment statistics serializer"""

    total_payments = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    completed_payments = serializers.IntegerField()
    pending_payments = serializers.IntegerField()
    failed_payments = serializers.IntegerField()
    refunded_payments = serializers.IntegerField()
    manual_approval_payments = serializers.IntegerField()
    monthly_payments = serializers.IntegerField()
    monthly_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    payments_by_type = serializers.DictField()
    payments_by_status = serializers.DictField()
    top_resellers = serializers.ListField()
    recent_payments = serializers.ListField()


class InvoiceGenerateSerializer(serializers.Serializer):
    """Serializer for invoice generation"""

    include_tax = serializers.BooleanField(default=True)
    tax_rate = serializers.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    notes = serializers.CharField(required=False, allow_blank=True)


# Stripe Payment Integration Serializers


class StripePaymentSerializer(serializers.ModelSerializer):
    """Enhanced Payment serializer with Stripe fields"""

    is_stripe_payment = serializers.ReadOnlyField()
    is_bundle_purchase = serializers.ReadOnlyField()
    total_amount_with_markup = serializers.ReadOnlyField()

    class Meta:
        model = Payment
        fields = [
            "id",
            "order",
            "amount",
            "currency",
            "payment_method",
            "payment_type",
            "status",
            "transaction_id",
            "gateway_transaction_id",
            "gateway_response",
            # Stripe-specific fields
            "stripe_payment_intent_id",
            "stripe_checkout_session_id",
            "stripe_customer_id",
            "client_secret",
            "bundle_name",
            "bundle_details",
            "base_price",
            "reseller_markup_percent",
            "reseller_markup_amount",
            # Manual payment fields
            "manual_payment_proof",
            "manual_payment_notes",
            "manual_approved_by",
            "manual_approved_at",
            "manual_rejection_reason",
            # Refund fields
            "refund_amount",
            "refund_reason",
            "refund_approved_by",
            "refund_approved_at",
            # Invoice fields
            "invoice_number",
            "invoice_generated",
            "invoice_generated_at",
            "invoice_file",
            # Timestamps
            "created_at",
            "updated_at",
            "processed_at",
            "completed_at",
            # Computed properties
            "is_refunded",
            "net_amount",
            "customer_name",
            "reseller_name",
            "requires_manual_approval",
            "is_stripe_payment",
            "is_bundle_purchase",
            "total_amount_with_markup",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "is_refunded",
            "net_amount",
            "customer_name",
            "reseller_name",
            "requires_manual_approval",
            "is_stripe_payment",
            "is_bundle_purchase",
            "total_amount_with_markup",
        ]


class BundleCheckoutRequestSerializer(serializers.Serializer):
    """Serializer for bundle checkout request"""

    client_id = serializers.IntegerField()
    bundle_name = serializers.CharField(max_length=100)
    reseller_markup_percent = serializers.DecimalField(
        max_digits=5, decimal_places=2, default=0.00, min_value=0, max_value=100
    )

    def validate_bundle_name(self, value):
        """Validate bundle name format"""
        if not value.startswith("esimp_"):
            raise serializers.ValidationError("Bundle name must start with 'esimp_'")
        return value


class PaymentIntentRequestSerializer(serializers.Serializer):
    """Serializer for payment intent request"""

    client_id = serializers.IntegerField()
    bundle_name = serializers.CharField(max_length=100)
    reseller_markup_percent = serializers.DecimalField(
        max_digits=5, decimal_places=2, default=0.00, min_value=0, max_value=100
    )

    def validate_bundle_name(self, value):
        """Validate bundle name format"""
        if not value.startswith("esimp_"):
            raise serializers.ValidationError("Bundle name must start with 'esimp_'")
        return value


class StripeWebhookSerializer(serializers.ModelSerializer):
    """Stripe webhook serializer"""

    payment_id = serializers.CharField(source="payment.id", read_only=True)

    class Meta:
        model = StripeWebhook
        fields = [
            "id",
            "stripe_event_id",
            "event_type",
            "payment_id",
            "processed",
            "processing_attempts",
            "last_processing_error",
            "stripe_created_at",
            "received_at",
            "processed_at",
        ]
        read_only_fields = [
            "id",
            "payment_id",
            "stripe_created_at",
            "received_at",
            "processed_at",
        ]


class StripePaymentStatusSerializer(serializers.Serializer):
    """Serializer for payment status response"""

    payment_intent_id = serializers.CharField()
    status = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    currency = serializers.CharField()
    metadata = serializers.DictField()
    payment_id = serializers.IntegerField(required=False)
    bundle_name = serializers.CharField(required=False)
    client_id = serializers.IntegerField(required=False)
    local_status = serializers.CharField(required=False)


class CheckoutSessionResponseSerializer(serializers.Serializer):
    """Serializer for checkout session response"""

    checkout_url = serializers.URLField()
    session_id = serializers.CharField()
    payment_id = serializers.IntegerField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    currency = serializers.CharField()
    bundle_details = serializers.DictField()


class PaymentIntentResponseSerializer(serializers.Serializer):
    """Serializer for payment intent response"""

    payment_intent_id = serializers.CharField()
    client_secret = serializers.CharField()
    payment_id = serializers.IntegerField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    currency = serializers.CharField()
    publishable_key = serializers.CharField()


class BundlePurchaseFilterSerializer(serializers.Serializer):
    """Serializer for bundle purchase filters"""

    status = serializers.CharField(required=False)
    bundle_name = serializers.CharField(required=False)
    client_id = serializers.IntegerField(required=False)
    reseller_id = serializers.IntegerField(required=False)
    start_date = serializers.DateTimeField(required=False)
    end_date = serializers.DateTimeField(required=False)
    page = serializers.IntegerField(default=1, min_value=1)
    page_size = serializers.IntegerField(default=20, min_value=1, max_value=100)
