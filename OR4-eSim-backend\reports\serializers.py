from rest_framework import serializers

from .models import AnalyticsEvent, PerformanceMetric, Report, ReportSchedule


class ReportSerializer(serializers.ModelSerializer):
    """Report serializer"""

    requested_by_user = serializers.SerializerMethodField()

    class Meta:
        model = Report
        fields = [
            "id",
            "name",
            "report_type",
            "format",
            "date_from",
            "date_to",
            "parameters",
            "file_path",
            "file_size",
            "generated_at",
            "is_generated",
            "is_downloaded",
            "requested_by_user",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_requested_by_user(self, obj):
        if obj.requested_by:
            from accounts.serializers import UserSerializer

            return UserSerializer(obj.requested_by).data
        return None


class ReportCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating reports"""

    class Meta:
        model = Report
        fields = ["name", "report_type", "format", "date_from", "date_to", "parameters"]

    def create(self, validated_data):
        # Set the requested_by field to the current user
        validated_data["requested_by"] = self.context["request"].user
        return super().create(validated_data)


class AnalyticsEventSerializer(serializers.ModelSerializer):
    """Analytics event serializer"""

    user = serializers.SerializerMethodField()

    class Meta:
        model = AnalyticsEvent
        fields = [
            "id",
            "user",
            "event_type",
            "event_data",
            "ip_address",
            "user_agent",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]

    def get_user(self, obj):
        if obj.user:
            from accounts.serializers import UserSerializer

            return UserSerializer(obj.user).data
        return None


class AnalyticsEventCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating analytics events"""

    class Meta:
        model = AnalyticsEvent
        fields = ["user", "event_type", "event_data", "ip_address", "user_agent"]


class PerformanceMetricSerializer(serializers.ModelSerializer):
    """Performance metric serializer"""

    class Meta:
        model = PerformanceMetric
        fields = ["id", "name", "category", "value", "unit", "context", "recorded_at"]
        read_only_fields = ["id"]


class ReportScheduleSerializer(serializers.ModelSerializer):
    """Report schedule serializer"""

    created_by_user = serializers.SerializerMethodField()

    class Meta:
        model = ReportSchedule
        fields = [
            "id",
            "name",
            "report_type",
            "frequency",
            "is_active",
            "last_run",
            "next_run",
            "recipients",
            "format",
            "parameters",
            "created_by_user",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_created_by_user(self, obj):
        if obj.created_by:
            from accounts.serializers import UserSerializer

            return UserSerializer(obj.created_by).data
        return None


class ReportScheduleCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating report schedules"""

    class Meta:
        model = ReportSchedule
        fields = [
            "name",
            "report_type",
            "frequency",
            "is_active",
            "next_run",
            "recipients",
            "format",
            "parameters",
        ]


class RevenueReportSerializer(serializers.Serializer):
    """Revenue report serializer"""

    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    monthly_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    revenue_by_country = serializers.DictField()
    revenue_trend = serializers.ListField()


class UserGrowthReportSerializer(serializers.Serializer):
    """User growth report serializer"""

    total_users = serializers.IntegerField()
    new_users_this_month = serializers.IntegerField()
    growth_rate = serializers.FloatField()
    user_trend = serializers.ListField()
