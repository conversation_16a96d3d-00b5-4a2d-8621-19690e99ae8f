from django.urls import path

from .traveroam_api_views import (
    TraveRoamAnalyticsAPIView,
    TraveRoamBulkOperationsAPIView,
    TraveRoamClientValidationAPIView,
    TraveRoamESIMAssignmentAPIView,
    TraveRoamESIMStatusAPIView,
    TraveRoamESIMUsageAPIView,
    TraveRoamNetworksAPIView,
    TraveRoamOrderProcessingAPIView,
    TraveRoamPlansAPIView,
    TraveRoamWebhookAPIView,
)

app_name = "traveroam_api"

urlpatterns = [
    # Plans and Bundles
    path("plans/", TraveRoamPlansAPIView.as_view(), name="plans"),
    path("networks/", TraveRoamNetworksAPIView.as_view(), name="networks"),
    # eSIM Assignment and Management
    path("esim/assign/", TraveRoamESIMAssignmentAPIView.as_view(), name="esim_assign"),
    path(
        "esim/<int:esim_id>/status/",
        TraveRoamESIMStatusAPIView.as_view(),
        name="esim_status",
    ),
    path(
        "esim/<int:esim_id>/usage/",
        TraveRoamESIMUsageAPIView.as_view(),
        name="esim_usage",
    ),
    # Order Processing
    path(
        "orders/process/",
        TraveRoamOrderProcessingAPIView.as_view(),
        name="process_order",
    ),
    # Client Validation
    path(
        "client/validate/",
        TraveRoamClientValidationAPIView.as_view(),
        name="validate_client",
    ),
    # Bulk Operations
    path("bulk/assign/", TraveRoamBulkOperationsAPIView.as_view(), name="bulk_assign"),
    # Analytics and Reporting
    path("analytics/", TraveRoamAnalyticsAPIView.as_view(), name="analytics"),
    # Webhook Management
    path("webhooks/", TraveRoamWebhookAPIView.as_view(), name="webhooks"),
]
