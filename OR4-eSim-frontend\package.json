{"name": "sim-admin-panel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@react-spring/web": "^10.0.1", "@visx/gradient": "^3.12.0", "@visx/group": "^3.12.0", "@visx/mock-data": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "reaviz": "^16.0.6", "recharts": "^2.15.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "globals": "^15.9.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}}