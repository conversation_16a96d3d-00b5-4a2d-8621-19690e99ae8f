import random
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone

from accounts.models import User
from clients.models import Client
from orders.models import Order
from payments.models import Payment


class Command(BaseCommand):
    help = "Create sample payment data for testing admin functionality"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=20,
            help="Number of payments to create (default: 20)",
        )

    def handle(self, *args, **options):
        count = options["count"]

        # Get existing users and orders
        admin_users = User.objects.filter(role="admin")
        reseller_users = User.objects.filter(role="reseller")
        client_users = User.objects.filter(role="client")
        orders = Order.objects.all()

        if not orders.exists():
            self.stdout.write(
                self.style.ERROR("No orders found. Please create sample orders first.")
            )
            return

        if not admin_users.exists():
            self.stdout.write(
                self.style.ERROR(
                    "No admin users found. Please create admin users first."
                )
            )
            return

        payment_types = [
            Payment.PaymentType.STRIPE,
            Payment.PaymentType.MANUAL,
            Payment.PaymentType.BANK_TRANSFER,
            Payment.PaymentType.CASH,
        ]

        payment_statuses = [
            Payment.PaymentStatus.COMPLETED,
            Payment.PaymentStatus.PENDING,
            Payment.PaymentStatus.FAILED,
            Payment.PaymentStatus.MANUAL_APPROVAL,
            Payment.PaymentStatus.REFUNDED,
        ]

        currencies = ["USD", "EUR", "GBP"]

        with transaction.atomic():
            payments_created = 0

            for i in range(count):
                order = random.choice(orders)
                payment_type = random.choice(payment_types)
                status = random.choice(payment_statuses)
                currency = random.choice(currencies)
                amount = Decimal(str(random.randint(10, 500)))

                # Generate transaction ID
                transaction_id = f"TXN-{timezone.now().strftime('%Y%m%d')}-{i+1:04d}"

                # Create payment
                payment = Payment.objects.create(
                    order=order,
                    amount=amount,
                    currency=currency,
                    payment_method=(
                        "stripe"
                        if payment_type == Payment.PaymentType.STRIPE
                        else "manual"
                    ),
                    payment_type=payment_type,
                    status=status,
                    transaction_id=transaction_id,
                    gateway_transaction_id=(
                        f"GW-{transaction_id}"
                        if payment_type == Payment.PaymentType.STRIPE
                        else None
                    ),
                )

                # Set timestamps based on status
                if status == Payment.PaymentStatus.COMPLETED:
                    payment.processed_at = timezone.now() - timezone.timedelta(
                        hours=random.randint(1, 24)
                    )
                    payment.completed_at = payment.processed_at
                elif status == Payment.PaymentStatus.MANUAL_APPROVAL:
                    # Add some manual payment details
                    payment.manual_payment_notes = (
                        f"Sample manual payment note for payment {i+1}"
                    )
                    if random.choice([True, False]):
                        payment.manual_approved_by = random.choice(admin_users)
                        payment.manual_approved_at = (
                            timezone.now()
                            - timezone.timedelta(hours=random.randint(1, 12))
                        )
                elif status == Payment.PaymentStatus.REFUNDED:
                    payment.refund_amount = amount * Decimal("0.5")  # 50% refund
                    payment.refund_reason = "Customer requested partial refund"
                    payment.refund_approved_by = random.choice(admin_users)
                    payment.refund_approved_at = timezone.now() - timezone.timedelta(
                        hours=random.randint(1, 6)
                    )

                payment.save()
                payments_created += 1

                # Generate invoice for some completed payments
                if status == Payment.PaymentStatus.COMPLETED and random.choice(
                    [True, False]
                ):
                    payment.generate_invoice_number()
                    payment.invoice_generated = True
                    payment.invoice_generated_at = timezone.now()
                    payment.save()

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully created {payments_created} sample payments"
                )
            )

            # Print statistics
            total_payments = Payment.objects.count()
            completed_payments = Payment.objects.filter(
                status=Payment.PaymentStatus.COMPLETED
            ).count()
            pending_payments = Payment.objects.filter(
                status=Payment.PaymentStatus.PENDING
            ).count()
            manual_approval_payments = Payment.objects.filter(
                status=Payment.PaymentStatus.MANUAL_APPROVAL
            ).count()
            refunded_payments = Payment.objects.filter(
                status=Payment.PaymentStatus.REFUNDED
            ).count()

            self.stdout.write(
                self.style.SUCCESS(
                    f"\nPayment Statistics:\n"
                    f"Total Payments: {total_payments}\n"
                    f"Completed: {completed_payments}\n"
                    f"Pending: {pending_payments}\n"
                    f"Manual Approval Required: {manual_approval_payments}\n"
                    f"Refunded: {refunded_payments}"
                )
            )
