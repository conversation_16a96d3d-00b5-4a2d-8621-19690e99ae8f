from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Count, Q, Sum
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from api.utils import create_error_response, create_success_response

from .models import ESIM, ESIMDelivery, ESIMPlan, ESIMUsage
from .serializers import (
    ESIMDeliverySerializer,
    ESIMPlanSerializer,
    ESIMSerializer,
    ESIMUsageSerializer,
)
from .services import ESIMWorkflowService
from .traveroam_api import TraveRoamAPIClient


class TraveRoamPlansAPIView(APIView):
    """API for fetching TraveRoam plans and bundles"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get available plans from TraveRoam"""
        try:
            client = TraveRoamAPIClient()

            # Get countries parameter from request
            countries = request.query_params.get("countries", "")
            region = request.query_params.get("region", "")

            # Fetch plans from TraveRoam
            plans_data = client.get_available_plans(countries=countries, region=region)

            return create_success_response(
                "Plans retrieved successfully", data=plans_data
            )
        except Exception as e:
            return create_error_response(f"Failed to fetch plans: {str(e)}")

    def post(self, request):
        """Get specific bundle details"""
        try:
            client = TraveRoamAPIClient()
            bundle_name = request.data.get("bundle_name")

            if not bundle_name:
                return create_error_response("bundle_name is required")

            bundle_data = client.get_bundle_details(bundle_name)

            return create_success_response(
                "Bundle details retrieved successfully", data=bundle_data
            )
        except Exception as e:
            return create_error_response(f"Failed to fetch bundle details: {str(e)}")


class TraveRoamNetworksAPIView(APIView):
    """API for fetching TraveRoam networks"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get networks for countries"""
        try:
            client = TraveRoamAPIClient()

            countries = request.data.get("countries", "")
            isos = request.data.get("isos", "")
            return_all = request.data.get("returnall", False)

            networks_data = client.get_networks(
                countries=countries, isos=isos, return_all=return_all
            )

            return create_success_response(
                "Networks retrieved successfully", data=networks_data
            )
        except Exception as e:
            return create_error_response(f"Failed to fetch networks: {str(e)}")


class TraveRoamESIMAssignmentAPIView(APIView):
    """API for eSIM assignment with validation"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Assign eSIM to client with validation"""
        try:
            # Extract data from request
            client_id = request.data.get("client_id")
            bundle_name = request.data.get("bundle_name")
            phone_number = request.data.get("phone_number")

            if not all([client_id, bundle_name, phone_number]):
                return create_error_response(
                    "client_id, bundle_name, and phone_number are required"
                )

            # Validate phone number format
            if not phone_number.startswith("+"):
                return create_error_response(
                    "Phone number must start with country code (e.g., +1)"
                )

            # Check if phone number already has active eSIM
            active_esim = ESIM.objects.filter(
                client_id=client_id,
                status__in=["assigned", "activated"],
                expires_at__gt=timezone.now(),
            ).first()

            if active_esim:
                return create_error_response(
                    f"Client already has an active eSIM (ID: {active_esim.id}) that expires on {active_esim.expires_at.strftime('%Y-%m-%d %H:%M')}. "
                    "Cannot assign new eSIM until current one expires or is cancelled."
                )

            # Check if phone number is already assigned to another client
            existing_esim = (
                ESIM.objects.filter(
                    client__phone_number=phone_number,
                    status__in=["assigned", "activated"],
                    expires_at__gt=timezone.now(),
                )
                .exclude(client_id=client_id)
                .first()
            )

            if existing_esim:
                return create_error_response(
                    f"Phone number {phone_number} is already assigned to another client (ID: {existing_esim.client_id}) "
                    "with an active eSIM. Cannot reassign until current eSIM expires."
                )

            # Get client and reseller objects
            from clients.models import Client
            from resellers.models import Reseller

            client = Client.objects.get(id=client_id)
            reseller = Reseller.objects.get(user=request.user)

            # Proceed with eSIM assignment
            with transaction.atomic():
                esim_data = ESIMWorkflowService.assign_esim_to_client(
                    client=client, bundle_name=bundle_name, reseller=reseller
                )

                if not esim_data["success"]:
                    return create_error_response(esim_data["error"])

                # Deliver eSIM to client
                esim = ESIM.objects.get(id=esim_data["esim_id"])
                delivery_result = ESIMWorkflowService.deliver_esim_to_client(esim)

                return create_success_response(
                    "eSIM assigned and delivered successfully",
                    data={"esim": esim_data, "delivery": delivery_result},
                )

        except ValidationError as e:
            return create_error_response(f"Validation error: {str(e)}")
        except Exception as e:
            return create_error_response(f"Failed to assign eSIM: {str(e)}")


class TraveRoamESIMStatusAPIView(APIView):
    """API for checking eSIM status"""

    permission_classes = [IsAuthenticated]

    def get(self, request, esim_id):
        """Get eSIM status from TraveRoam"""
        try:
            # Check if user has access to this eSIM
            esim = ESIM.objects.filter(id=esim_id)

            if hasattr(request.user, "is_reseller") and request.user.is_reseller:
                esim = esim.filter(reseller__user=request.user)
            elif hasattr(request.user, "is_client") and request.user.is_client:
                esim = esim.filter(client__user=request.user)
            elif not hasattr(request.user, "is_admin") or not request.user.is_admin:
                return create_error_response("Access denied")

            esim = esim.first()
            if not esim:
                return create_error_response("eSIM not found or access denied")

            # Get status from TraveRoam
            status_data = ESIMWorkflowService.get_esim_status(esim)

            # Update local status if needed
            if status_data.get("status") != esim.status:
                esim.status = status_data.get("status", esim.status)
                esim.save()

            return create_success_response(
                "eSIM status retrieved successfully", data=status_data
            )
        except Exception as e:
            return create_error_response(f"Failed to get eSIM status: {str(e)}")


class TraveRoamESIMUsageAPIView(APIView):
    """API for checking eSIM usage"""

    permission_classes = [IsAuthenticated]

    def get(self, request, esim_id):
        """Get eSIM usage from TraveRoam"""
        try:
            # Check if user has access to this eSIM
            esim = ESIM.objects.filter(id=esim_id)

            if hasattr(request.user, "is_reseller") and request.user.is_reseller:
                esim = esim.filter(reseller__user=request.user)
            elif hasattr(request.user, "is_client") and request.user.is_client:
                esim = esim.filter(client__user=request.user)
            elif not hasattr(request.user, "is_admin") or not request.user.is_admin:
                return create_error_response("Access denied")

            esim = esim.first()
            if not esim:
                return create_error_response("eSIM not found or access denied")

            # Get usage from TraveRoam
            usage_data = ESIMWorkflowService.get_esim_usage(esim)

            return create_success_response(
                "eSIM usage retrieved successfully", data=usage_data
            )
        except Exception as e:
            return create_error_response(f"Failed to get eSIM usage: {str(e)}")


class TraveRoamOrderProcessingAPIView(APIView):
    """API for processing TraveRoam orders"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Process eSIM order"""
        try:
            # Extract order data
            bundle_name = request.data.get("bundle_name")
            quantity = request.data.get("quantity", 1)
            client_data = request.data.get("client_data", {})

            if not bundle_name:
                return create_error_response("bundle_name is required")

            # Process order with TraveRoam
            client = TraveRoamAPIClient()
            order_result = client.process_order(
                bundle_name=bundle_name, quantity=quantity, client_data=client_data
            )

            return create_success_response(
                "Order processed successfully", data=order_result
            )
        except Exception as e:
            return create_error_response(f"Failed to process order: {str(e)}")


class TraveRoamClientValidationAPIView(APIView):
    """API for validating client before eSIM assignment"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Validate client eligibility for eSIM assignment"""
        try:
            client_id = request.data.get("client_id")
            phone_number = request.data.get("phone_number")
            bundle_name = request.data.get("bundle_name")

            if not all([client_id, phone_number]):
                return create_error_response("client_id and phone_number are required")

            validation_result = {"eligible": True, "warnings": [], "errors": []}

            # Check for active eSIMs
            active_esims = ESIM.objects.filter(
                client_id=client_id,
                status__in=["assigned", "activated"],
                expires_at__gt=timezone.now(),
            )

            if active_esims.exists():
                validation_result["eligible"] = False
                validation_result["errors"].append(
                    f"Client has {active_esims.count()} active eSIM(s). "
                    "Cannot assign new eSIM until current ones expire."
                )

            # Check phone number conflicts
            phone_conflicts = ESIM.objects.filter(
                client__phone_number=phone_number,
                status__in=["assigned", "activated"],
                expires_at__gt=timezone.now(),
            ).exclude(client_id=client_id)

            if phone_conflicts.exists():
                validation_result["eligible"] = False
                validation_result["errors"].append(
                    f"Phone number {phone_number} is already assigned to another client with active eSIM."
                )

            # Check bundle availability if provided
            if bundle_name:
                try:
                    traveroam_client = TraveRoamAPIClient()
                    bundle_details = traveroam_client.get_bundle_details(bundle_name)
                    if not bundle_details.get("available", True):
                        validation_result["warnings"].append(
                            f"Bundle {bundle_name} may not be available"
                        )
                except Exception:
                    validation_result["warnings"].append(
                        f"Could not verify bundle {bundle_name} availability"
                    )

            return create_success_response(
                "Client validation completed", data=validation_result
            )
        except Exception as e:
            return create_error_response(f"Validation failed: {str(e)}")


class TraveRoamBulkOperationsAPIView(APIView):
    """API for bulk eSIM operations"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Bulk assign eSIMs to multiple clients"""
        try:
            assignments = request.data.get("assignments", [])

            if not assignments:
                return create_error_response("assignments list is required")

            results = []

            for assignment in assignments:
                try:
                    client_id = assignment.get("client_id")
                    bundle_name = assignment.get("bundle_name")
                    phone_number = assignment.get("phone_number")

                    if not all([client_id, bundle_name, phone_number]):
                        results.append(
                            {
                                "client_id": client_id,
                                "success": False,
                                "error": "Missing required fields",
                            }
                        )
                        continue

                    # Validate client eligibility
                    active_esim = ESIM.objects.filter(
                        client_id=client_id,
                        status__in=["assigned", "activated"],
                        expires_at__gt=timezone.now(),
                    ).first()

                    if active_esim:
                        results.append(
                            {
                                "client_id": client_id,
                                "success": False,
                                "error": f"Client has active eSIM (ID: {active_esim.id})",
                            }
                        )
                        continue

                    # Get client and reseller objects
                    client = Client.objects.get(id=client_id)
                    reseller = Reseller.objects.get(user=request.user)

                    # Assign eSIM
                    esim_data = ESIMWorkflowService.assign_esim_to_client(
                        client=client, bundle_name=bundle_name, reseller=reseller
                    )

                    results.append(
                        {
                            "client_id": client_id,
                            "success": True,
                            "esim_id": esim_data["esim_id"],
                        }
                    )

                except Exception as e:
                    results.append(
                        {
                            "client_id": assignment.get("client_id"),
                            "success": False,
                            "error": str(e),
                        }
                    )

            return create_success_response(
                "Bulk assignment completed", data={"results": results}
            )
        except Exception as e:
            return create_error_response(f"Bulk operation failed: {str(e)}")


class TraveRoamAnalyticsAPIView(APIView):
    """API for TraveRoam analytics and reporting"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get analytics data"""
        try:
            # Get date range from query params
            start_date = request.query_params.get("start_date")
            end_date = request.query_params.get("end_date")

            # Base queryset based on user role
            if hasattr(request.user, "is_admin") and request.user.is_admin:
                esims = ESIM.objects.all()
            elif hasattr(request.user, "is_reseller") and request.user.is_reseller:
                esims = ESIM.objects.filter(reseller__user=request.user)
            else:
                return create_error_response("Access denied")

            # Apply date filters
            if start_date:
                esims = esims.filter(created_at__gte=start_date)
            if end_date:
                esims = esims.filter(created_at__lte=end_date)

            # Calculate analytics
            analytics = {
                "total_esims": esims.count(),
                "active_esims": esims.filter(
                    status__in=["assigned", "activated"], expires_at__gt=timezone.now()
                ).count(),
                "expired_esims": esims.filter(status="expired").count(),
                "total_data_used": esims.aggregate(total=Sum("data_used"))["total"]
                or 0,
                "status_distribution": esims.values("status").annotate(
                    count=Count("id")
                ),
                "plan_distribution": esims.values("plan__name").annotate(
                    count=Count("id")
                ),
                "monthly_trends": esims.extra(
                    select={"month": "DATE_TRUNC('month', created_at)"}
                )
                .values("month")
                .annotate(count=Count("id"))
                .order_by("month"),
            }

            return create_success_response(
                "Analytics retrieved successfully", data=analytics
            )
        except Exception as e:
            return create_error_response(f"Failed to get analytics: {str(e)}")


class TraveRoamWebhookAPIView(APIView):
    """API for TraveRoam webhook management"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get webhook logs"""
        try:
            from .models import TraveRoamWebhook

            webhooks = TraveRoamWebhook.objects.all()

            # Filter by type if provided
            webhook_type = request.query_params.get("type")
            if webhook_type:
                webhooks = webhooks.filter(webhook_type=webhook_type)

            # Filter by processed status
            processed = request.query_params.get("processed")
            if processed is not None:
                webhooks = webhooks.filter(processed=processed == "true")

            # Paginate results
            page = int(request.query_params.get("page", 1))
            page_size = int(request.query_params.get("page_size", 20))
            start = (page - 1) * page_size
            end = start + page_size

            webhooks = webhooks[start:end]

            from .serializers import TraveRoamWebhookSerializer

            serializer = TraveRoamWebhookSerializer(webhooks, many=True)

            return create_success_response(
                "Webhook logs retrieved successfully", data=serializer.data
            )
        except Exception as e:
            return create_error_response(f"Failed to get webhook logs: {str(e)}")

    def post(self, request):
        """Manually trigger webhook processing"""
        try:
            webhook_id = request.data.get("webhook_id")

            if not webhook_id:
                return create_error_response("webhook_id is required")

            from .models import TraveRoamWebhook
            from .webhook_views import process_webhook

            webhook = TraveRoamWebhook.objects.get(id=webhook_id)
            result = process_webhook(webhook)

            return create_success_response(
                "Webhook processed successfully", data=result
            )
        except TraveRoamWebhook.DoesNotExist:
            return create_error_response("Webhook not found")
        except Exception as e:
            return create_error_response(f"Failed to process webhook: {str(e)}")
