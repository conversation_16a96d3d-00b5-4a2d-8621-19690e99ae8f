# Generated by Django 4.2.7 on 2025-08-23 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("esim_management", "0002_remove_esim_public_user_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="esim",
            old_name="traveroam_order_id",
            new_name="bundle_name",
        ),
        migrations.RenameField(
            model_name="historicalesim",
            old_name="traveroam_order_id",
            new_name="bundle_name",
        ),
        migrations.AddField(
            model_name="esim",
            name="bundle_details",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="esim",
            name="cancellation_reason",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="esim",
            name="cancelled_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="esim",
            name="delivered_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="esim",
            name="matching_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="esim",
            name="smdp_address",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="esim",
            name="traveroam_order_reference",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="esimdelivery",
            name="delivery_data",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalesim",
            name="bundle_details",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalesim",
            name="cancellation_reason",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalesim",
            name="cancelled_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalesim",
            name="delivered_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalesim",
            name="matching_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalesim",
            name="smdp_address",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="historicalesim",
            name="traveroam_order_reference",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="traveroamwebhook",
            name="event_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="traveroamwebhook",
            name="last_error",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="traveroamwebhook",
            name="processing_attempts",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="esimdelivery",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("sent", "Sent"),
                    ("delivered", "Delivered"),
                    ("failed", "Failed"),
                    ("retry", "Retry"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="traveroamwebhook",
            name="webhook_type",
            field=models.CharField(
                choices=[
                    ("activation", "Activation"),
                    ("usage", "Usage"),
                    ("expiry", "Expiry"),
                    ("error", "Error"),
                    ("bundle_assigned", "Bundle Assigned"),
                    ("bundle_revoked", "Bundle Revoked"),
                    ("status_change", "Status Change"),
                ],
                max_length=20,
            ),
        ),
    ]
