import { useState, useEffect } from 'react'
import { useTheme } from '../../context/ThemeContext'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../context/AuthContext'
import {
  ArrowLeft,
  Users,
  Smartphone,
  Globe,
  Calendar,
  DollarSign,
  Wifi,
  Search,
  Filter,
  Check,
  X,
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Mail,
  QrCode,
  RefreshCw,
  CreditCard,
  UserPlus,
  Loader2,
  Save,
  Send,
  Phone,
  MapPin,
  Database
} from 'lucide-react'
import toast from 'react-hot-toast'
import { apiService } from '../../services/apiService'
import { tokenService } from '../../services/tokenService'

function AssignEsimPage() {
  const { resolvedTheme } = useTheme()
  const navigate = useNavigate()
  const { user, token, isAuthenticated } = useAuth()

  // Workflow state - matching HTML test file structure (6 steps)
  const [workflowData, setWorkflowData] = useState({
    userData: null,
    selectedBundle: null,
    paymentData: null,
    esimData: null,
    currentStep: 1,
    availableBundles: []
  })

  const [currentStep, setCurrentStep] = useState(1) // 1: Add User, 2: Fetch Plans, 3: Payment, 4: Provision, 5: QR & Email, 6: Save DB
  const [stepLoading, setStepLoading] = useState({})
  const [errors, setErrors] = useState({})
  const [resellerMarkup, setResellerMarkup] = useState(0)

  // Additional state for backend integration
  const [countryInfo, setCountryInfo] = useState(null)
  const [validationStatus, setValidationStatus] = useState(null)
  const [paymentSession, setPaymentSession] = useState(null)
  const [provisioningDetails, setProvisioningDetails] = useState(null)
  const [emailDeliveryStatus, setEmailDeliveryStatus] = useState(null)
  const [databaseSaveStatus, setDatabaseSaveStatus] = useState(null)
  
  // Form data for new user creation
  const [userForm, setUserForm] = useState({
    fullName: '',
    phoneNumber: '',
    email: '',
    passportId: '',
    travelDate: ''
  })
  
  const [countryInfo, setCountryInfo] = useState(null)
  const [validationStatus, setValidationStatus] = useState(null)

  // Helper function for authenticated API requests (from HTML file)
  const makeAuthenticatedRequest = async (url, options = {}) => {
    const authToken = tokenService.getAccessToken()

    if (!authToken) {
      throw new Error('No authentication token available. Please login first.')
    }

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`,
      ...options.headers
    }

    try {
      let response = await fetch(url, {
        ...options,
        headers
      })

      // If token is expired, try to refresh and retry
      if (response.status === 401) {
        console.log('Token expired, attempting to refresh...')
        try {
          await tokenService.refreshAccessToken()
          const newToken = tokenService.getAccessToken()
          headers['Authorization'] = `Bearer ${newToken}`
          response = await fetch(url, { ...options, headers })
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError)
          throw new Error('Authentication failed. Please login again.')
        }
      }

      return response
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // Utility functions
  const showStepLoading = (step, message) => {
    setStepLoading(prev => ({ ...prev, [step]: { loading: true, message } }))
  }

  const hideStepLoading = (step) => {
    setStepLoading(prev => ({ ...prev, [step]: { loading: false, message: '' } }))
  }

  // Helper function for authenticated API requests (from HTML file)
  const makeAuthenticatedRequest = async (url, options = {}) => {
    const authToken = tokenService.getAccessToken()

    if (!authToken) {
      throw new Error('No authentication token available. Please login first.')
    }

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`,
      ...options.headers
    }

    try {
      let response = await fetch(url, {
        ...options,
        headers
      })

      // If token is expired, try to refresh and retry
      if (response.status === 401) {
        console.log('Token expired, attempting to refresh...')
        try {
          await tokenService.refreshAccessToken()
          const newToken = tokenService.getAccessToken()
          headers['Authorization'] = `Bearer ${newToken}`
          response = await fetch(url, { ...options, headers })
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError)
          throw new Error('Authentication failed. Please login again.')
        }
      }

      return response
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // Utility functions
  const showStepLoading = (step, message) => {
    setStepLoading(prev => ({ ...prev, [step]: { loading: true, message } }))
  }

  const hideStepLoading = (step) => {
    setStepLoading(prev => ({ ...prev, [step]: { loading: false, message: '' } }))
  }

  // Validation functions from HTML file
  const validateEmail = (email) => {
    const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return pattern.test(email)
  }

  const validatePhone = (phone) => {
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')
    return cleanPhone.startsWith('+') && cleanPhone.length >= 10
  }

  // Country detection function from HTML file
  const detectCountryFromPhone = async (phone, validateEsim = false) => {
    try {
      const response = await fetch('/api/v1/utils/detect-country/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phone_number: phone,
          validate_esim: validateEsim
        })
      })

      const data = await response.json()
      
      if (data.success && data.data) {
        return data.data.country_info || data.data
      } else {
        if (validateEsim && !data.success) {
          throw new Error(data.message || 'Phone number validation failed')
        }
        return { name: "Unknown Country", code: "XX", region: "Unknown" }
      }
    } catch (error) {
      console.warn('Country detection API error:', error)
      if (validateEsim && error.message !== 'Failed to fetch') {
        throw error
      }
      return { name: "Unknown Country", code: "XX", region: "Unknown" }
    }
  }

  // User validation function from HTML file
  const validateUserData = async () => {
    const { fullName, phoneNumber, email, passportId, travelDate } = userForm

    // Validation
    if (fullName.length < 2) {
      toast.error('Name must be at least 2 characters long.')
      return false
    }

    if (!validatePhone(phoneNumber)) {
      toast.error('Please enter a valid phone number with country code (e.g., +92XXXXXXXXX)')
      return false
    }

    if (!validateEmail(email)) {
      toast.error('Please enter a valid email address.')
      return false
    }

    if (passportId.length < 3) {
      toast.error('Please enter a valid passport/ID number.')
      return false
    }

    setValidationStatus('detecting')

    try {
      // Detect country using backend API and validate eSIM eligibility
      const countryInfo = await detectCountryFromPhone(phoneNumber, true)
      
      // Store user data
      const userData = {
        fullName,
        phoneNumber,
        email,
        passportId,
        countryOfTravel: countryInfo,
        travelDate: travelDate || null
      }

      setWorkflowData(prev => ({ ...prev, userData }))
      setCountryInfo(countryInfo)
      setValidationStatus('success')
      toast.success('User data validated successfully!')
      return true
      
    } catch (error) {
      console.error('User validation error:', error)
      setValidationStatus('error')
      toast.error('Validation failed: ' + error.message)
      return false
    }
  }

  // Fetch available bundles function from HTML file
  const fetchAvailableBundles = async () => {
    if (!workflowData.userData) {
      toast.error('Please validate user data first.')
      return
    }

    // Check authentication using helper function
    if (!isAuthenticated) {
      toast.error('Please login first.')
      return
    }

    showStepLoading(2, 'Fetching available eSIM plans from TraveRoam...')

    try {
      console.log('🔄 Fetching available bundles from TraveRoam API...')

      const country = workflowData.userData.countryOfTravel

      // Build query parameters for TraveRoam API (from HTML file)
      const queryParams = new URLSearchParams({
        countries: country.code,
        region: country.region
      })

      const response = await makeAuthenticatedRequest(`/api/v1/traveroam/plans/?${queryParams}`, {
        method: 'GET'
      })

      const data = await response.json()

      if (data.success && data.data) {
        console.log(`✅ Loaded ${data.data.length} plans from TraveRoam API`)
        setWorkflowData(prev => ({ ...prev, availableBundles: data.data }))
        hideStepLoading(2)
        toast.success(`Found ${data.data.length} eSIM plans for ${country.name}!`)
      } else {
        throw new Error(data.message || 'No plans available for this destination')
      }
    } catch (error) {
      console.error('❌ Failed to load eSIM plans:', error)
      hideStepLoading(2)
      toast.error('Failed to load eSIM plans: ' + error.message)
      setWorkflowData(prev => ({ ...prev, availableBundles: [] }))
    }
  }

  // Process payment function from HTML file
  const processPayment = async () => {
    if (!workflowData.selectedBundle) {
      toast.error('Please select a bundle first.')
      return
    }

    if (!isAuthenticated) {
      toast.error('Please login first.')
      return
    }

    showStepLoading(3, 'Processing payment via Stripe...')

    try {
      console.log('💳 Processing payment...')

      const basePrice = parseFloat(workflowData.selectedBundle.price) || 0
      const markupAmount = (basePrice * resellerMarkup / 100)
      const finalPrice = basePrice + markupAmount

      // Check if bundle has already been paid for by this client (from HTML file)
      try {
        const bundleCheckResponse = await makeAuthenticatedRequest('/api/v1/stripe/payment-status/', {
          method: 'POST',
          body: JSON.stringify({
            bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name,
            client_email: workflowData.userData.email,
            client_phone: workflowData.userData.phoneNumber
          })
        })

        const bundleCheckData = await bundleCheckResponse.json()

        if (bundleCheckData.success && bundleCheckData.data.payment_exists) {
          // Payment already exists
          const existingPayment = bundleCheckData.data
          setWorkflowData(prev => ({
            ...prev,
            paymentData: {
              amount: existingPayment.amount,
              currency: existingPayment.currency,
              status: 'completed',
              session_id: existingPayment.session_id,
              timestamp: existingPayment.created_at
            }
          }))
          hideStepLoading(3)
          toast.success('Payment already completed for this bundle!')
          return
        }
      } catch (bundleCheckError) {
        console.log('No existing payment found, proceeding with new payment...')
      }

      // Create real Stripe checkout session (from HTML file)
      const response = await makeAuthenticatedRequest('/api/v1/stripe/create-checkout-session/', {
        method: 'POST',
        body: JSON.stringify({
          bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name,
          bundle_details: {
            name: workflowData.selectedBundle.name,
            price: basePrice,
            currency: workflowData.selectedBundle.currency || 'USD'
          },
          client_details: {
            name: workflowData.userData.fullName,
            email: workflowData.userData.email,
            phone: workflowData.userData.phoneNumber,
            country: workflowData.userData.countryOfTravel.name
          },
          markup_percent: resellerMarkup,
          success_url: window.location.origin + '/reseller-dashboard/assign-esim?payment=success',
          cancel_url: window.location.origin + '/reseller-dashboard/assign-esim?payment=cancel'
        })
      })

      const data = await response.json()

      if (data.success && data.data && data.data.checkout_url) {
        // Store session ID for later verification
        localStorage.setItem('stripeSessionId', data.data.session_id)
        setPaymentSession(data.data)

        // Redirect to Stripe checkout
        window.location.href = data.data.checkout_url
      } else {
        throw new Error(data.message || 'Failed to create payment session')
      }

    } catch (error) {
      console.error('❌ Payment processing failed:', error)
      hideStepLoading(3)
      toast.error('Payment failed: ' + error.message)
    }
  }

  // Provision eSIM function from HTML file
  const provisionESIM = async () => {
    if (!workflowData.paymentData) {
      toast.error('Please complete payment first.')
      return
    }

    if (!isAuthenticated) {
      toast.error('Please login first.')
      return
    }

    showStepLoading(4, 'Provisioning eSIM via TraveRoam...')

    try {
      console.log('🚀 Processing order with TraveRoam...')

      // Client validation step (from HTML file)
      const validationResponse = await makeAuthenticatedRequest('/api/v1/traveroam/client/validate/', {
        method: 'POST',
        body: JSON.stringify({
          email: workflowData.userData.email,
          phone: workflowData.userData.phoneNumber,
          name: workflowData.userData.fullName,
          bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name
        })
      })

      const validationData = await validationResponse.json()

      if (!validationData.success) {
        throw new Error(validationData.message || 'Client validation failed')
      }

      // Check for duplicate assignments (from HTML file)
      const duplicateResponse = await makeAuthenticatedRequest('/api/v1/traveroam/client/validate/', {
        method: 'POST',
        body: JSON.stringify({
          email: workflowData.userData.email,
          bundle_name: workflowData.selectedBundle.bundle_id || workflowData.selectedBundle.name,
          check_duplicates: true
        })
      })

      const duplicateData = await duplicateResponse.json()

      if (duplicateData.success && duplicateData.data.has_duplicate) {
        throw new Error('This client already has this bundle assigned')
      }

      // Process order with TraveRoam (from HTML file)
      const response = await makeAuthenticatedRequest('/api/v1/traveroam/orders/process/', {
        method: 'POST',
        body: JSON.stringify({
          user_data: workflowData.userData,
          bundle_data: workflowData.selectedBundle,
          payment_data: workflowData.paymentData,
          reseller_markup: resellerMarkup
        })
      })

      const data = await response.json()

      if (data.success && data.data) {
        const esimData = {
          esim_id: data.data.esim_id,
          qr_code: data.data.qr_code,
          activation_code: data.data.activation_code,
          iccid: data.data.iccid,
          status: 'provisioned',
          order_id: data.data.order_id,
          timestamp: new Date().toISOString()
        }

        setWorkflowData(prev => ({ ...prev, esimData }))
        setProvisioningDetails(esimData)
        hideStepLoading(4)
        toast.success('eSIM provisioned successfully via TraveRoam!')
      } else {
        throw new Error(data.message || 'eSIM provisioning failed')
      }

    } catch (error) {
      console.error('❌ eSIM provisioning failed:', error)
      hideStepLoading(4)
      toast.error('eSIM provisioning failed: ' + error.message)
    }
  }

  // Send eSIM email function from HTML file
  const sendESIMEmail = async () => {
    if (!workflowData.esimData) {
      toast.error('Please provision eSIM first.')
      return
    }

    if (!isAuthenticated) {
      toast.error('Please login first.')
      return
    }

    showStepLoading(5, 'Sending eSIM details email...')

    try {
      console.log('📧 Sending eSIM details email...')

      // Send delivery email using backend API (from HTML file)
      const response = await makeAuthenticatedRequest('/api/v1/esim/esim-deliveries/send_delivery_email/', {
        method: 'POST',
        body: JSON.stringify({
          user_data: workflowData.userData,
          bundle_data: workflowData.selectedBundle,
          esim_data: workflowData.esimData,
          payment_data: workflowData.paymentData,
          delivery_method: 'email',
          include_qr_code: true,
          include_instructions: true
        })
      })

      const data = await response.json()

      if (data.success) {
        const emailResult = {
          sent: true,
          timestamp: new Date().toISOString(),
          recipient: workflowData.userData.email,
          delivery_id: data.data.delivery_id,
          tracking_id: data.data.tracking_id
        }

        setWorkflowData(prev => ({ ...prev, deliveryData: emailResult }))
        setEmailDeliveryStatus(emailResult)
        hideStepLoading(5)
        toast.success('eSIM details sent to client email successfully!')
      } else {
        throw new Error(data.message || 'Email delivery failed')
      }

    } catch (error) {
      console.error('❌ Email sending failed:', error)
      hideStepLoading(5)
      toast.error('Email sending failed: ' + error.message)
    }
  }

  // Save to database function from HTML file
  const saveToDatabase = async () => {
    if (!workflowData.esimData) {
      toast.error('Please complete all previous steps first.')
      return
    }

    if (!isAuthenticated) {
      toast.error('Please login first.')
      return
    }

    showStepLoading(6, 'Saving data to database...')

    try {
      console.log('💾 Saving workflow data to database...')

      // Save all data to database (from HTML file)
      const response = await makeAuthenticatedRequest('/api/v1/workflow/save-complete/', {
        method: 'POST',
        body: JSON.stringify({
          user_data: workflowData.userData,
          bundle_data: workflowData.selectedBundle,
          esim_data: workflowData.esimData,
          payment_data: workflowData.paymentData,
          delivery_data: workflowData.deliveryData,
          workflow_metadata: {
            reseller_markup: resellerMarkup,
            completed_at: new Date().toISOString(),
            workflow_version: '6-step-v1'
          }
        })
      })

      const data = await response.json()

      if (data.success) {
        const saveResult = {
          saved: true,
          timestamp: new Date().toISOString(),
          record_id: data.data.workflow_id,
          database_id: data.data.id
        }

        setWorkflowData(prev => ({ ...prev, savedData: saveResult }))
        setDatabaseSaveStatus(saveResult)
        hideStepLoading(6)
        toast.success('All workflow data saved to database successfully!')
      } else {
        throw new Error(data.message || 'Database save failed')
      }

    } catch (error) {
      console.error('❌ Database save failed:', error)
      hideStepLoading(6)
      toast.error('Database save failed: ' + error.message)
    }
  }

  // Payment status check function (from HTML file)
  const checkPaymentStatus = async (sessionId) => {
    if (!sessionId) return null

    try {
      const response = await makeAuthenticatedRequest(`/api/v1/stripe/retrieve-checkout-session/?session_id=${sessionId}`, {
        method: 'GET'
      })

      const data = await response.json()

      if (data.success && data.data) {
        return data.data
      }

      return null
    } catch (error) {
      console.error('Payment status check failed:', error)
      return null
    }
  }

  // Handle payment return from Stripe (from HTML file)
  const handlePaymentReturn = async () => {
    const urlParams = new URLSearchParams(window.location.search)
    const paymentStatus = urlParams.get('payment')
    const sessionId = localStorage.getItem('stripeSessionId')

    if (paymentStatus === 'success' && sessionId) {
      showStepLoading(3, 'Verifying payment status...')

      try {
        const paymentData = await checkPaymentStatus(sessionId)

        if (paymentData && paymentData.payment_status === 'complete') {
          setWorkflowData(prev => ({
            ...prev,
            paymentData: {
              amount: paymentData.amount_total / 100, // Convert from cents
              currency: paymentData.currency,
              status: 'completed',
              session_id: sessionId,
              timestamp: paymentData.created
            }
          }))

          // Move to next step
          setCurrentStep(4)
          hideStepLoading(3)
          toast.success('Payment verified successfully!')

          // Clear URL parameters
          window.history.replaceState({}, document.title, window.location.pathname)
          localStorage.removeItem('stripeSessionId')
        }
      } catch (error) {
        console.error('Payment verification failed:', error)
        hideStepLoading(3)
        toast.error('Payment verification failed')
      }
    } else if (paymentStatus === 'cancel') {
      toast.error('Payment was cancelled')
      // Clear URL parameters
      window.history.replaceState({}, document.title, window.location.pathname)
      localStorage.removeItem('stripeSessionId')
    }
  }

  // Check for payment return on component mount
  useEffect(() => {
    handlePaymentReturn()
  }, [])

  // Step navigation functions
  const nextStep = () => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1)
      setWorkflowData(prev => ({ ...prev, currentStep: currentStep + 1 }))
    }
  }

  const previousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      setWorkflowData(prev => ({ ...prev, currentStep: currentStep - 1 }))
    }
  }

  const resetWorkflow = () => {
    setWorkflowData({
      userData: null,
      selectedBundle: null,
      paymentData: null,
      esimData: null,
      currentStep: 1,
      availableBundles: []
    })
    setCurrentStep(1)
    setUserForm({
      fullName: '',
      phoneNumber: '',
      email: '',
      passportId: '',
      travelDate: ''
    })
    setCountryInfo(null)
    setValidationStatus(null)
    setResellerMarkup(0)
    toast.success('Workflow reset successfully!')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/reseller-dashboard')}
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">eSIM Assignment Workflow</h1>
            <p className="text-muted-foreground">Complete 6-step workflow for eSIM assignment via TraveRoam</p>
          </div>
        </div>
        <button
          onClick={resetWorkflow}
          className="flex items-center space-x-2 px-4 py-2 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Reset Workflow</span>
        </button>
      </div>

      {/* Progress Steps - 6 steps like HTML file */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex items-center justify-between">
          {[
            { step: 1, title: 'Add New User', icon: UserPlus },
            { step: 2, title: 'Fetch eSIM Plans', icon: Smartphone },
            { step: 3, title: 'Payment Processing', icon: CreditCard },
            { step: 4, title: 'Provision eSIM', icon: Wifi },
            { step: 5, title: 'QR & Email Delivery', icon: QrCode },
            { step: 6, title: 'Save to Database', icon: CheckCircle }
          ].map((item, index) => {
            const Icon = item.icon
            const isActive = currentStep === item.step
            const isCompleted = currentStep > item.step
            const isLast = index === 5

            return (
              <div key={item.step} className="flex items-center">
                <div className={`flex items-center space-x-3 ${
                  isActive ? 'text-primary' : 
                  isCompleted ? 'text-green-500' : 
                  'text-muted-foreground'
                }`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                    isActive ? 'border-primary bg-primary/10' :
                    isCompleted ? 'border-green-500 bg-green-500/10' :
                    'border-muted-foreground/30 bg-muted/30'
                  }`}>
                    {isCompleted ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Icon className="h-4 w-4" />
                    )}
                  </div>
                  <span className="text-sm font-medium">{item.title}</span>
                </div>
                {!isLast && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-muted-foreground/30'
                  }`} />
                )}
              </div>
            )
          })}
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-muted-foreground/20 rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / 6) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Step 1: Add New User */}
      {currentStep === 1 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <UserPlus className="h-5 w-5" />
              <span>Step 1: Add New User</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    👤 Full Name *
                  </label>
                  <input
                    type="text"
                    value={userForm.fullName}
                    onChange={(e) => setUserForm(prev => ({ ...prev, fullName: e.target.value }))}
                    placeholder="Enter full name"
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    📞 Phone Number (with country code) *
                  </label>
                  <input
                    type="tel"
                    value={userForm.phoneNumber}
                    onChange={(e) => setUserForm(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    placeholder="+91XXXXXXXXXX"
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                  {countryInfo && (
                    <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
                      🌍 Detected Country: {countryInfo.name} ({countryInfo.code}) - Region: {countryInfo.region}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    📧 Email *
                  </label>
                  <input
                    type="email"
                    value={userForm.email}
                    onChange={(e) => setUserForm(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    🛂 Passport Number / National ID *
                  </label>
                  <input
                    type="text"
                    value={userForm.passportId}
                    onChange={(e) => setUserForm(prev => ({ ...prev, passportId: e.target.value }))}
                    placeholder="Passport or ID number"
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    📅 Date of Travel (optional)
                  </label>
                  <input
                    type="date"
                    value={userForm.travelDate}
                    onChange={(e) => setUserForm(prev => ({ ...prev, travelDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div className="pt-4">
                  <button
                    onClick={validateUserData}
                    disabled={validationStatus === 'detecting'}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
                  >
                    {validationStatus === 'detecting' ? (
                      <>
                        <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                        <span>Validating...</span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4" />
                        <span>Validate User Data</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="flex justify-between pt-4 border-t border-border">
              <div></div>
              <button
                onClick={nextStep}
                disabled={!workflowData.userData}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                <span>Next: Fetch eSIM Plans</span>
                <ArrowLeft className="h-4 w-4 rotate-180" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 2: Fetch Available eSIM Plans */}
      {currentStep === 2 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <Smartphone className="h-5 w-5" />
              <span>Step 2: Available eSIM Plans</span>
            </h2>

            {workflowData.userData && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h5 className="font-medium text-blue-900">🌍 Destination: {workflowData.userData.countryOfTravel.name} ({workflowData.userData.countryOfTravel.code})</h5>
                <p className="text-blue-700">Region: {workflowData.userData.countryOfTravel.region}</p>
                <p className="text-blue-600 text-sm">Click "Fetch eSIM Plans" to see available bundles for this destination.</p>
              </div>
            )}

            <div className="flex items-center space-x-4">
              <button
                onClick={fetchAvailableBundles}
                disabled={isLoadingPlans || !workflowData.userData}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                {isLoadingPlans ? (
                  <>
                    <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                    <span>Fetching Plans...</span>
                  </>
                ) : (
                  <>
                    <Smartphone className="h-4 w-4" />
                    <span>Fetch eSIM Plans</span>
                  </>
                )}
              </button>
            </div>

            {workflowData.availableBundles.length > 0 && (
              <div className="space-y-4">
                <h3 className="font-medium text-foreground">Available Plans:</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {workflowData.availableBundles.map((bundle, index) => (
                    <button
                      key={bundle.bundle_id}
                      onClick={() => {
                        setWorkflowData(prev => ({ ...prev, selectedBundle: bundle }))
                        toast.success('Bundle selected!')
                      }}
                      className={`p-4 border rounded-lg text-left transition-all ${
                        workflowData.selectedBundle?.bundle_id === bundle.bundle_id
                          ? 'border-green-500 bg-green-50'
                          : 'border-border hover:border-primary/50 hover:bg-muted/50'
                      }`}
                    >
                      <div className="space-y-2">
                        <div className="flex justify-between items-start">
                          <h4 className="font-medium text-foreground">{bundle.name}</h4>
                          <span className="text-lg font-bold text-primary">${bundle.price}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">Data: {bundle.data_volume}</p>
                        <p className="text-sm text-muted-foreground">Validity: {bundle.validity_days} days</p>
                        <p className="text-sm text-muted-foreground">Network: {bundle.network}</p>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-between pt-4 border-t border-border">
              <button
                onClick={previousStep}
                className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Previous</span>
              </button>
              <button
                onClick={nextStep}
                disabled={!workflowData.selectedBundle}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                <span>Next: Payment Processing</span>
                <ArrowLeft className="h-4 w-4 rotate-180" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Payment Processing */}
      {currentStep === 3 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>Step 3: Payment Processing</span>
            </h2>

            {workflowData.selectedBundle && workflowData.userData && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">💳 Payment Summary</h4>
                <p className="text-blue-700"><strong>Client:</strong> {workflowData.userData.fullName} ({workflowData.userData.email})</p>
                <p className="text-blue-700"><strong>Plan:</strong> {workflowData.selectedBundle.name}</p>
                <p className="text-blue-700"><strong>Base Price:</strong> ${workflowData.selectedBundle.price} {workflowData.selectedBundle.currency}</p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Reseller Markup Percentage (0-50%)
              </label>
              <input
                type="number"
                min="0"
                max="50"
                value={resellerMarkup}
                onChange={(e) => setResellerMarkup(parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            {workflowData.selectedBundle && (
              <div className="bg-muted/30 p-4 rounded-lg">
                <h4 className="font-medium text-foreground mb-2">Pricing Breakdown:</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Base Price:</span>
                    <span>${workflowData.selectedBundle.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Markup ({resellerMarkup}%):</span>
                    <span>${((workflowData.selectedBundle.price * resellerMarkup) / 100).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between font-bold border-t pt-1">
                    <span>Total:</span>
                    <span>${(workflowData.selectedBundle.price + (workflowData.selectedBundle.price * resellerMarkup) / 100).toFixed(2)}</span>
                  </div>
                </div>
              </div>
            )}

            <button
              onClick={processPayment}
              disabled={isProcessingPayment || !workflowData.selectedBundle}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isProcessingPayment ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Processing Payment...</span>
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4" />
                  <span>Process Payment</span>
                </>
              )}
            </button>

            <div className="flex justify-between pt-4 border-t border-border">
              <button
                onClick={previousStep}
                className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Previous</span>
              </button>
              <button
                onClick={nextStep}
                disabled={!workflowData.paymentData}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                <span>Next: Provision eSIM</span>
                <ArrowLeft className="h-4 w-4 rotate-180" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 4: Provision eSIM */}
      {currentStep === 4 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <Wifi className="h-5 w-5" />
              <span>Step 4: Provision eSIM</span>
            </h2>

            {workflowData.paymentData && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700">✅ Payment completed successfully. Ready to provision eSIM via TraveRoam.</p>
              </div>
            )}

            <button
              onClick={provisionESIM}
              disabled={isProvisioning || !workflowData.paymentData}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
            >
              {isProvisioning ? (
                <>
                  <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                  <span>Provisioning eSIM...</span>
                </>
              ) : (
                <>
                  <Wifi className="h-4 w-4" />
                  <span>Provision eSIM via TraveRoam</span>
                </>
              )}
            </button>

            {workflowData.esimData && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">✅ eSIM Provisioned Successfully!</h4>
                <div className="text-sm text-green-700 space-y-1">
                  <p><strong>eSIM ID:</strong> {workflowData.esimData.esim_id}</p>
                  <p><strong>ICCID:</strong> {workflowData.esimData.iccid}</p>
                  <p><strong>Status:</strong> {workflowData.esimData.status}</p>
                </div>
              </div>
            )}

            <div className="flex justify-between pt-4 border-t border-border">
              <button
                onClick={previousStep}
                className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Previous</span>
              </button>
              <button
                onClick={nextStep}
                disabled={!workflowData.esimData}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                <span>Next: QR & Email Delivery</span>
                <ArrowLeft className="h-4 w-4 rotate-180" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 5: QR Code & Email Delivery */}
      {currentStep === 5 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <QrCode className="h-5 w-5" />
              <span>Step 5: QR Code & Email Delivery</span>
            </h2>

            {workflowData.esimData && (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <h4 className="font-medium text-blue-900 mb-2">📱 QR Code for eSIM Installation</h4>
                  {workflowData.esimData.qr_code && (
                    <div className="bg-white p-4 rounded-lg inline-block">
                      <div className="text-xs font-mono break-all text-gray-600 max-w-md">
                        {workflowData.esimData.qr_code}
                      </div>
                    </div>
                  )}
                  <p className="text-blue-600 text-sm mt-2">Scan this QR code with your device to install the eSIM</p>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">eSIM Details:</h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p><strong>Activation Code:</strong> {workflowData.esimData.activation_code}</p>
                    <p><strong>ICCID:</strong> {workflowData.esimData.iccid}</p>
                    <p><strong>Status:</strong> {workflowData.esimData.status}</p>
                    <p><strong>Expiry Date:</strong> {workflowData.esimData.expiry_date ? new Date(workflowData.esimData.expiry_date).toLocaleDateString() : 'N/A'}</p>
                  </div>
                </div>
              </div>
            )}

            <button
              onClick={sendESIMEmail}
              disabled={isSendingEmail || !workflowData.esimData}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {isSendingEmail ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Sending Email...</span>
                </>
              ) : (
                <>
                  <Mail className="h-4 w-4" />
                  <span>Send eSIM Details to Client</span>
                </>
              )}
            </button>

            <div className="flex justify-between pt-4 border-t border-border">
              <button
                onClick={previousStep}
                className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Previous</span>
              </button>
              <button
                onClick={nextStep}
                disabled={!workflowData.esimData}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                <span>Next: Save to Database</span>
                <ArrowLeft className="h-4 w-4 rotate-180" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 6: Save to Database */}
      {currentStep === 6 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <CheckCircle className="h-5 w-5" />
              <span>Step 6: Save to Database</span>
            </h2>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">💾 Database Operation Summary</h4>
              <p className="text-blue-700">All eSIM data will be saved to the database for future reference and management.</p>
            </div>

            <button
              onClick={saveToDatabase}
              disabled={isSavingToDb || !workflowData.esimData}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              {isSavingToDb ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Saving to Database...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4" />
                  <span>Save to Database</span>
                </>
              )}
            </button>

            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">🎉 Workflow Completed Successfully!</h3>
              <p className="text-muted-foreground mb-4">All data has been saved to the database and the eSIM has been delivered to the client.</p>

              <div className="flex items-center justify-center space-x-4">
                <button
                  onClick={resetWorkflow}
                  className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Start New Workflow</span>
                </button>
                <button
                  onClick={() => navigate('/reseller-dashboard')}
                  className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Dashboard</span>
                </button>
              </div>
            </div>

            <div className="flex justify-between pt-4 border-t border-border">
              <button
                onClick={previousStep}
                className="flex items-center space-x-2 px-4 py-2 border border-border rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Previous</span>
              </button>
              <div></div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AssignEsimPage
