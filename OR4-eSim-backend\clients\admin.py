from django.contrib import admin

from .models import Client, SupportTicket


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = (
        "full_name",
        "client_type",
        "reseller",
        "phone_number",
        "email",
        "status",
        "tier",
        "total_esims",
        "is_mobile_app_user",
    )
    list_filter = (
        "client_type",
        "status",
        "tier",
        "is_active",
        "is_blocked",
        "created_at",
        "country_of_travel",
        "country",
        "preferred_package",
    )
    search_fields = (
        "full_name",
        "phone_number",
        "email",
        "passport_number",
        "national_id",
        "city",
        "address",
    )
    readonly_fields = (
        "total_esims",
        "active_esims",
        "total_orders",
        "total_spent",
        "is_mobile_app_user",
    )
    raw_id_fields = ("reseller", "user", "admin_override_by")
    actions = [
        "block_selected_users",
        "unblock_selected_users",
        "upgrade_to_premium",
        "downgrade_to_basic",
        "export_public_users",
    ]

    fieldsets = (
        (
            "Basic Information",
            {
                "fields": (
                    "user",
                    "client_type",
                    "reseller",
                    "full_name",
                    "phone_number",
                    "email",
                )
            },
        ),
        (
            "Address Information",
            {
                "fields": ("address", "city", "country"),
                "description": "For direct users",
            },
        ),
        (
            "Travel Information",
            {
                "fields": (
                    "passport_number",
                    "national_id",
                    "country_of_travel",
                    "date_of_travel",
                ),
                "description": "For reseller clients",
            },
        ),
        (
            "Status & Tier",
            {"fields": ("status", "tier", "is_active", "is_blocked")},
        ),
        (
            "Activity & Plan",
            {
                "fields": (
                    "last_login",
                    "last_activity",
                    "total_logins",
                    "current_plan",
                    "plan_start_date",
                    "plan_end_date",
                    "auto_renewal",
                )
            },
        ),
        (
            "Preferences",
            {
                "fields": ("preferred_package", "preferred_network"),
                "description": "For direct users",
            },
        ),
        (
            "Admin Controls",
            {
                "fields": (
                    "admin_notes",
                    "admin_override_reason",
                    "admin_override_by",
                    "admin_override_at",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Statistics",
            {
                "fields": (
                    "total_esims",
                    "active_esims",
                    "total_orders",
                    "total_spent",
                    "is_mobile_app_user",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def is_mobile_app_user(self, obj):
        """Check if user has placed orders via mobile app"""
        from orders.models import Order

        return Order.objects.filter(client__user=obj.user, order_source="app").exists()

    is_mobile_app_user.boolean = True
    is_mobile_app_user.short_description = "Mobile App User"

    def get_queryset(self, request):
        """Optimize queryset with related fields"""
        return (
            super()
            .get_queryset(request)
            .select_related("user", "reseller", "reseller__user", "admin_override_by")
        )

    def block_selected_users(self, request, queryset):
        """Block selected users"""
        count = 0
        for client in queryset:
            client.block_client("Blocked via admin interface", request.user)
            count += 1
        self.message_user(request, f"Successfully blocked {count} user(s).")

    block_selected_users.short_description = "Block selected users"

    def unblock_selected_users(self, request, queryset):
        """Unblock selected users"""
        count = 0
        for client in queryset:
            client.unblock_client("Unblocked via admin interface", request.user)
            count += 1
        self.message_user(request, f"Successfully unblocked {count} user(s).")

    unblock_selected_users.short_description = "Unblock selected users"

    def upgrade_to_premium(self, request, queryset):
        """Upgrade selected users to premium tier"""
        count = 0
        for client in queryset:
            client.upgrade_tier("premium", "Upgraded via admin interface", request.user)
            count += 1
        self.message_user(
            request, f"Successfully upgraded {count} user(s) to premium tier."
        )

    upgrade_to_premium.short_description = "Upgrade to premium tier"

    def downgrade_to_basic(self, request, queryset):
        """Downgrade selected users to basic tier"""
        count = 0
        for client in queryset:
            client.upgrade_tier("basic", "Downgraded via admin interface", request.user)
            count += 1
        self.message_user(
            request, f"Successfully downgraded {count} user(s) to basic tier."
        )

    downgrade_to_basic.short_description = "Downgrade to basic tier"

    def export_public_users(self, request, queryset):
        """Export public users data"""
        import csv

        from django.http import HttpResponse

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = (
            'attachment; filename="public_users_export.csv"'
        )

        writer = csv.writer(response)
        writer.writerow(
            [
                "ID",
                "Full Name",
                "Email",
                "Phone",
                "City",
                "Country",
                "Status",
                "Tier",
                "Preferred Package",
                "Total Orders",
                "Total Spent",
                "Mobile App User",
                "Last Activity",
                "Created Date",
            ]
        )

        for client in queryset.filter(client_type="direct_user"):
            from orders.models import Order

            is_mobile_user = Order.objects.filter(
                client__user=client.user, order_source="app"
            ).exists()

            writer.writerow(
                [
                    client.id,
                    client.full_name,
                    client.email,
                    client.phone_number,
                    client.city or "",
                    client.country or "",
                    client.get_status_display(),
                    client.get_tier_display(),
                    client.preferred_package or "",
                    client.total_orders,
                    client.total_spent,
                    "Yes" if is_mobile_user else "No",
                    (
                        client.last_activity.strftime("%Y-%m-%d %H:%M")
                        if client.last_activity
                        else "Never"
                    ),
                    client.created_at.strftime("%Y-%m-%d"),
                ]
            )

        return response

    export_public_users.short_description = "Export public users to CSV"

    def get_list_filter(self, request):
        """Add mobile app user filter"""
        filters = list(super().get_list_filter(request))
        if "is_mobile_app_user" not in filters:
            filters.append("is_mobile_app_user")
        return filters

    def get_search_fields(self, request):
        """Add mobile app specific search fields"""
        fields = list(super().get_search_fields(request))
        if "preferred_package" not in fields:
            fields.append("preferred_package")
        return fields


@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "client",
        "subject",
        "status",
        "assigned_to",
        "created_at",
    )
    list_filter = ("status", "created_at")
    search_fields = (
        "subject",
        "description",
        "client__full_name",
    )
    readonly_fields = ("created_at", "updated_at")
    raw_id_fields = ("client", "assigned_to")

    fieldsets = (
        (
            "Ticket Information",
            {"fields": ("client", "subject", "description")},
        ),
        ("Status", {"fields": ("status", "assigned_to")}),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "resolved_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("client", "client__user", "assigned_to")
        )
