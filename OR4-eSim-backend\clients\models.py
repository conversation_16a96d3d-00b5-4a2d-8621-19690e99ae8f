from django.conf import settings
from django.db import models
from simple_history.models import HistoricalRecords


class Client(models.Model):
    """
    Comprehensive Client model that handles both reseller clients and direct public users
    """

    class ClientStatus(models.TextChoices):
        ACTIVE = "active", "Active"
        INACTIVE = "inactive", "Inactive"
        SUSPENDED = "suspended", "Suspended"
        BLOCKED = "blocked", "Blocked"
        PENDING_VERIFICATION = "pending_verification", "Pending Verification"

    class ClientTier(models.TextChoices):
        BASIC = "basic", "Basic"
        PREMIUM = "premium", "Premium"
        ENTERPRISE = "enterprise", "Enterprise"

    class ClientType(models.TextChoices):
        RESELLER_CLIENT = "reseller_client", "Reseller Client"
        DIRECT_USER = "direct_user", "Direct User"

    # User relationship
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="client_profile",
    )

    # Client type and reseller relationship
    client_type = models.CharField(
        max_length=20, choices=ClientType.choices, default=ClientType.DIRECT_USER
    )
    reseller = models.ForeignKey(
        "resellers.Reseller",
        on_delete=models.CASCADE,
        related_name="clients",
        null=True,
        blank=True,
        help_text="Required for reseller clients",
    )

    # Basic information
    full_name = models.CharField(max_length=200)
    email = models.EmailField()
    phone_number = models.CharField(max_length=15)

    # Address information (for direct users)
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)

    # Travel information (for reseller clients)
    passport_number = models.CharField(max_length=50, blank=True, null=True)
    national_id = models.CharField(max_length=50, blank=True, null=True)
    country_of_travel = models.CharField(max_length=100, blank=True, null=True)
    date_of_travel = models.DateField(blank=True, null=True)

    # Status and tier management
    status = models.CharField(
        max_length=25, choices=ClientStatus.choices, default=ClientStatus.ACTIVE
    )
    tier = models.CharField(
        max_length=20, choices=ClientTier.choices, default=ClientTier.BASIC
    )

    # Legacy fields for backward compatibility
    is_active = models.BooleanField(default=True)
    is_blocked = models.BooleanField(default=False)

    # Activity tracking
    last_login = models.DateTimeField(blank=True, null=True)
    last_activity = models.DateTimeField(blank=True, null=True)
    total_logins = models.PositiveIntegerField(default=0)

    # Plan and subscription details
    current_plan = models.CharField(max_length=100, blank=True, null=True)
    plan_start_date = models.DateField(blank=True, null=True)
    plan_end_date = models.DateField(blank=True, null=True)
    auto_renewal = models.BooleanField(default=True)

    # Preferences (for direct users)
    preferred_package = models.CharField(max_length=100, blank=True, null=True)
    preferred_network = models.CharField(max_length=100, blank=True, null=True)

    # Admin override controls
    admin_notes = models.TextField(blank=True, null=True)
    admin_override_reason = models.CharField(max_length=200, blank=True, null=True)
    admin_override_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="client_overrides",
    )
    admin_override_at = models.DateTimeField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "clients"
        verbose_name = "Client"
        verbose_name_plural = "Clients"
        ordering = ["-created_at"]

    def __str__(self):
        if self.client_type == self.ClientType.RESELLER_CLIENT:
            return f"{self.full_name} (Reseller: {self.reseller.user.email})"
        return f"{self.full_name} (Direct User)"

    def save(self, *args, **kwargs):
        # Validate reseller relationship
        if self.client_type == self.ClientType.RESELLER_CLIENT and not self.reseller:
            raise ValueError("Reseller is required for reseller clients")

        # Update legacy fields for backward compatibility
        if self.status == self.ClientStatus.ACTIVE:
            self.is_active = True
            self.is_blocked = False
        elif self.status == self.ClientStatus.BLOCKED:
            self.is_active = False
            self.is_blocked = True
        else:
            self.is_active = False
            self.is_blocked = False

        super().save(*args, **kwargs)

    @property
    def total_esims(self):
        return self.esims.count()

    @property
    def active_esims(self):
        return self.esims.filter(status="active").count()

    @property
    def total_orders(self):
        return self.orders.count()

    @property
    def total_spent(self):
        return (
            self.orders.filter(status__in=["completed", "delivered"]).aggregate(
                total=models.Sum("total_amount")
            )["total"]
            or 0
        )

    @property
    def is_plan_active(self):
        if not self.plan_end_date:
            return False
        from django.utils import timezone

        return self.plan_end_date >= timezone.now().date()

    @property
    def is_reseller_client(self):
        return self.client_type == self.ClientType.RESELLER_CLIENT

    @property
    def is_direct_user(self):
        return self.client_type == self.ClientType.DIRECT_USER

    def update_activity(self, ip_address=None, user_agent=None):
        """Update client activity"""
        from django.utils import timezone

        self.last_activity = timezone.now()
        self.total_logins += 1
        self.save()

    def block_client(self, reason, admin_user=None):
        """Block client with reason and admin tracking"""
        from django.utils import timezone

        self.status = self.ClientStatus.BLOCKED
        self.admin_override_reason = reason
        self.admin_override_by = admin_user
        self.admin_override_at = timezone.now()
        self.save()

    def unblock_client(self, reason, admin_user=None):
        """Unblock client with reason and admin tracking"""
        from django.utils import timezone

        self.status = self.ClientStatus.ACTIVE
        self.admin_override_reason = reason
        self.admin_override_by = admin_user
        self.admin_override_at = timezone.now()
        self.save()

    def upgrade_tier(self, new_tier, reason, admin_user=None):
        """Upgrade client tier with reason and admin tracking"""
        from django.utils import timezone

        self.tier = new_tier
        self.admin_override_reason = reason
        self.admin_override_by = admin_user
        self.admin_override_at = timezone.now()
        self.save()


class SupportTicket(models.Model):
    """
    Support tickets for clients
    """

    class TicketStatus(models.TextChoices):
        OPEN = "open", "Open"
        IN_PROGRESS = "in_progress", "In Progress"
        RESOLVED = "resolved", "Resolved"
        CLOSED = "closed", "Closed"

    # Client relationship
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name="support_tickets",
    )

    subject = models.CharField(max_length=200)
    description = models.TextField()
    status = models.CharField(
        max_length=20, choices=TicketStatus.choices, default=TicketStatus.OPEN
    )

    # Admin assignment
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_tickets",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "support_tickets"
        ordering = ["-created_at"]

    def __str__(self):
        return f"Ticket #{self.id} - {self.client.full_name} - {self.subject}"

    @property
    def user(self):
        return self.client
