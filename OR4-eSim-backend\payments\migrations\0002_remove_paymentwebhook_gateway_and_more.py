# Generated by Django 4.2.7 on 2025-08-08 06:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='paymentwebhook',
            name='gateway',
        ),
        migrations.RemoveField(
            model_name='paymentwebhook',
            name='payment',
        ),
        migrations.RemoveField(
            model_name='refund',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='refund',
            name='payment',
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='refund_amount',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='refund_approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='refund_approved_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='refund_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='refund_amount',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
        ),
        migrations.AddField(
            model_name='payment',
            name='refund_approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='refund_approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_refunds', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='payment',
            name='refund_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpayment',
            name='payment_method',
            field=models.CharField(default='stripe', max_length=50),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payment_method',
            field=models.CharField(default='stripe', max_length=50),
        ),
        migrations.DeleteModel(
            name='Invoice',
        ),
        migrations.DeleteModel(
            name='PaymentGateway',
        ),
        migrations.DeleteModel(
            name='PaymentWebhook',
        ),
        migrations.DeleteModel(
            name='Refund',
        ),
    ]
