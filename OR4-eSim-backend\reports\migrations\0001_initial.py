# Generated by Django 4.2.7 on 2025-08-04 09:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('metric_type', models.CharField(choices=[('counter', 'Counter'), ('percentage', 'Percentage'), ('currency', 'Currency'), ('chart', 'Chart')], max_length=20)),
                ('value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('unit', models.CharField(blank=True, max_length=20, null=True)),
                ('calculation_query', models.TextField(blank=True, null=True)),
                ('refresh_interval', models.PositiveIntegerField(default=3600)),
                ('is_active', models.BooleanField(default=True)),
                ('display_order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_calculated', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'dashboard_metrics',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('category', models.CharField(choices=[('system', 'System'), ('api', 'API'), ('database', 'Database'), ('external', 'External Service')], max_length=20)),
                ('value', models.FloatField()),
                ('unit', models.CharField(max_length=20)),
                ('context', models.JSONField(default=dict)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'performance_metrics',
                'ordering': ['-recorded_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('report_type', models.CharField(choices=[('sales', 'Sales Report'), ('revenue', 'Revenue Report'), ('user_growth', 'User Growth Report'), ('esim_usage', 'eSIM Usage Report'), ('reseller_performance', 'Reseller Performance Report'), ('custom', 'Custom Report')], max_length=30)),
                ('frequency', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('last_run', models.DateTimeField(blank=True, null=True)),
                ('next_run', models.DateTimeField(blank=True, null=True)),
                ('recipients', models.JSONField(default=list)),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('json', 'JSON')], default='pdf', max_length=10)),
                ('parameters', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'report_schedules',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('report_type', models.CharField(choices=[('sales', 'Sales Report'), ('revenue', 'Revenue Report'), ('user_growth', 'User Growth Report'), ('esim_usage', 'eSIM Usage Report'), ('reseller_performance', 'Reseller Performance Report'), ('custom', 'Custom Report')], max_length=30)),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('json', 'JSON')], default='pdf', max_length=10)),
                ('date_from', models.DateField()),
                ('date_to', models.DateField()),
                ('parameters', models.JSONField(default=dict)),
                ('file_path', models.CharField(blank=True, max_length=500, null=True)),
                ('file_size', models.PositiveIntegerField(blank=True, null=True)),
                ('generated_at', models.DateTimeField(blank=True, null=True)),
                ('is_generated', models.BooleanField(default=False)),
                ('is_downloaded', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AnalyticsEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('user_registration', 'User Registration'), ('order_created', 'Order Created'), ('payment_completed', 'Payment Completed'), ('esim_assigned', 'eSIM Assigned'), ('esim_activated', 'eSIM Activated'), ('reseller_activity', 'Reseller Activity')], max_length=30)),
                ('event_data', models.JSONField(default=dict)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'analytics_events',
                'ordering': ['-created_at'],
            },
        ),
    ]
