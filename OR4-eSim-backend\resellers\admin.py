from django.conf import settings
from django.contrib import admin
from django.core.mail import send_mail
from django.http import JsonResponse
from django.urls import path, reverse
from django.utils import timezone
from django.utils.html import escape
from django.utils.safestring import mark_safe

from .models import Reseller, ResellerActivationRequest


@admin.register(ResellerActivationRequest)
class ResellerActivationRequestAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "email",
        "status",
        "max_clients",
        "max_sims",
        "credit_limit",
        "created_at",
        "action_buttons",
    )
    list_filter = ("status", "created_at")
    search_fields = ("user__email", "user__first_name", "user__last_name")
    readonly_fields = ("created_at", "updated_at")
    actions = ["approve_requests", "reject_requests"]

    fieldsets = (
        ("User Information", {"fields": ("user",)}),
        (
            "Reseller Configuration",
            {"fields": ("max_clients", "max_sims", "credit_limit")},
        ),
        ("Request Status", {"fields": ("status", "admin_notes")}),
        (
            "Admin Approval",
            {
                "fields": ("approved_by", "approved_at", "rejected_at"),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def email(self, obj):
        return obj.user.email

    email.short_description = "Email"

    def get_urls(self):
        urls = super().get_urls()
        custom = [
            path(
                "<int:pk>/approve-json/",
                self.admin_site.admin_view(self.approve_single_json),
                name="resellers_reselleractivationrequest_approve_json",
            ),
            path(
                "<int:pk>/reject-json/",
                self.admin_site.admin_view(self.reject_single_json),
                name="resellers_reselleractivationrequest_reject_json",
            ),
        ]
        return custom + urls

    def approve_single_json(self, request, pk: int):
        if request.method != "POST":
            return JsonResponse({"ok": False, "error": "Invalid method"}, status=405)
        try:
            activation_request = ResellerActivationRequest.objects.get(pk=pk)
            if activation_request.status != "pending":
                return JsonResponse({"ok": False, "error": "Not pending"}, status=400)
            Reseller.objects.get_or_create(
                user=activation_request.user,
                defaults={
                    "max_clients": activation_request.max_clients,
                    "max_sims": activation_request.max_sims,
                    "credit_limit": activation_request.credit_limit,
                },
            )
            activation_request.status = "approved"
            activation_request.approved_by = request.user
            activation_request.approved_at = timezone.now()
            activation_request.save()
            try:
                send_mail(
                    subject="Reseller Account Approved - eSIM Management System",
                    message=(
                        f"Dear {activation_request.user.first_name},\n\n"
                        f"Your reseller account has been approved by the administrator.\n\n"
                        f"Your account details:\n"
                        f"- Email: {activation_request.user.email}\n"
                        f"- Max Clients: {activation_request.max_clients}\n"
                        f"- Max SIMs: {activation_request.max_sims}\n"
                        f"- Credit Limit: ${activation_request.credit_limit}\n\n"
                        f"You can now log in to your reseller dashboard and start managing clients.\n\n"
                        f"Best regards,\n"
                        f"eSIM Management System Team"
                    ),
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[activation_request.user.email],
                    fail_silently=True,
                )
            except Exception:
                pass
            return JsonResponse({"ok": True, "status": "approved"})
        except ResellerActivationRequest.DoesNotExist:
            return JsonResponse({"ok": False, "error": "Not found"}, status=404)
        except Exception as e:
            return JsonResponse({"ok": False, "error": str(e)}, status=500)

    def reject_single_json(self, request, pk: int):
        if request.method != "POST":
            return JsonResponse({"ok": False, "error": "Invalid method"}, status=405)
        try:
            activation_request = ResellerActivationRequest.objects.get(pk=pk)
            if activation_request.status != "pending":
                return JsonResponse({"ok": False, "error": "Not pending"}, status=400)
            activation_request.status = "rejected"
            activation_request.rejected_at = timezone.now()
            activation_request.save()
            try:
                send_mail(
                    subject="Reseller Account Application Status - eSIM Management System",
                    message=(
                        f"Dear {activation_request.user.first_name},\n\n"
                        f"Your reseller account application has been reviewed by the administrator.\n\n"
                        f"Status: REJECTED\n\n"
                        f"If you have any questions, please contact our support team.\n\n"
                        f"Best regards,\n"
                        f"eSIM Management System Team"
                    ),
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[activation_request.user.email],
                    fail_silently=True,
                )
            except Exception:
                pass
            return JsonResponse({"ok": True, "status": "rejected"})
        except ResellerActivationRequest.DoesNotExist:
            return JsonResponse({"ok": False, "error": "Not found"}, status=404)
        except Exception as e:
            return JsonResponse({"ok": False, "error": str(e)}, status=500)

    def action_buttons(self, obj):
        if obj.status == "pending":
            approve_url = reverse(
                "admin:resellers_reselleractivationrequest_approve_json", args=[obj.id]
            )
            reject_url = reverse(
                "admin:resellers_reselleractivationrequest_reject_json", args=[obj.id]
            )
            buttons = (
                '<div style="display:flex;gap:6px;align-items:center;">'
                f'<button onclick="approveReq(this,{obj.id})" style="background:#28a745;color:#fff;border:none;padding:3px 8px;border-radius:3px;cursor:pointer;font-size:11px;">Approve</button>'
                f'<button onclick="rejectReq(this,{obj.id})" style="background:#dc3545;color:#fff;border:none;padding:3px 8px;border-radius:3px;cursor:pointer;font-size:11px;">Reject</button>'
                "</div>"
            )
            script = (
                "<script>"
                'function getCSRF(){ const m=document.cookie.match(/csrftoken=([^;]+)/); return m?m[1]:""; }'
                "async function approveReq(btn,id){"
                '  const row=btn.closest("tr");'
                '  const resp=await fetch("__APPROVE_URL__", {method:"POST", headers: {"X-CSRFToken": getCSRF()}});'
                "  const data=await resp.json();"
                '  if(data.ok){ const cell=row.querySelector("td.field-status"); if(cell) cell.innerHTML="approved"; btn.parentElement.innerHTML = "<span style=\\"color:#28a745;font-weight:600;\\">✓ Approved</span>"; } else { alert(data.error||"Failed"); }'
                "}"
                "async function rejectReq(btn,id){"
                '  const row=btn.closest("tr");'
                '  const resp=await fetch("__REJECT_URL__", {method:"POST", headers: {"X-CSRFToken": getCSRF()}});'
                "  const data=await resp.json();"
                '  if(data.ok){ const cell=row.querySelector("td.field-status"); if(cell) cell.innerHTML="rejected"; btn.parentElement.innerHTML = "<span style=\\"color:#dc3545;font-weight:600;\\">✗ Rejected</span>"; } else { alert(data.error||"Failed"); }'
                "}"
                "</script>"
            )
            script = script.replace("__APPROVE_URL__", approve_url).replace(
                "__REJECT_URL__", reject_url
            )
            return mark_safe(buttons + script)
        elif obj.status == "approved":
            return mark_safe(
                '<span style="color:#28a745;font-weight:600;">✓ Approved</span>'
            )
        elif obj.status == "rejected":
            return mark_safe(
                '<span style="color:#dc3545;font-weight:600;">✗ Rejected</span>'
            )
        return "-"

    action_buttons.short_description = "Actions"

    def approve_requests(self, request, queryset):
        approved_count = 0
        email_success_count = 0
        email_failed_count = 0

        for activation_request in queryset.filter(status="pending"):
            reseller, created = Reseller.objects.get_or_create(
                user=activation_request.user,
                defaults={
                    "max_clients": activation_request.max_clients,
                    "max_sims": activation_request.max_sims,
                    "credit_limit": activation_request.credit_limit,
                },
            )
            activation_request.status = "approved"
            activation_request.approved_by = request.user
            activation_request.approved_at = timezone.now()
            activation_request.save()
            try:
                email_sent = send_mail(
                    subject="Reseller Account Approved - eSIM Management System",
                    message=f"""
Dear {activation_request.user.first_name},

Your reseller account has been approved by the administrator.

Your account details:
- Email: {activation_request.user.email}
- Max Clients: {activation_request.max_clients}
- Max SIMs: {activation_request.max_sims}
- Credit Limit: ${activation_request.credit_limit}

You can now log in to your reseller dashboard and start managing clients.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[activation_request.user.email],
                    fail_silently=False,
                )
                if email_sent:
                    email_success_count += 1
                else:
                    email_failed_count += 1
                    self.message_user(
                        request,
                        f"Email failed to send to {activation_request.user.email}",
                        level="WARNING",
                    )
            except Exception as e:
                email_failed_count += 1
                self.message_user(
                    request,
                    f"Email failed to send to {activation_request.user.email}: {str(e)}",
                    level="ERROR",
                )
            approved_count += 1
        if approved_count > 0:
            message = f"{approved_count} reseller request(s) approved successfully."
            if email_success_count > 0:
                message += f" {email_success_count} email(s) sent successfully."
            if email_failed_count > 0:
                message += f" {email_failed_count} email(s) failed to send."
            self.message_user(request, message)
        else:
            self.message_user(request, "No pending requests to approve.")

    approve_requests.short_description = "Approve selected requests"

    def reject_requests(self, request, queryset):
        rejected_count = 0
        email_success_count = 0
        email_failed_count = 0
        for activation_request in queryset.filter(status="pending"):
            activation_request.status = "rejected"
            activation_request.rejected_at = timezone.now()
            activation_request.save()
            try:
                email_sent = send_mail(
                    subject="Reseller Account Application Status - eSIM Management System",
                    message=f"""
Dear {activation_request.user.first_name},

Your reseller account application has been reviewed by the administrator.

Status: REJECTED

If you have any questions, please contact our support team.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[activation_request.user.email],
                    fail_silently=False,
                )
                if email_sent:
                    email_success_count += 1
                else:
                    email_failed_count += 1
                    self.message_user(
                        request,
                        f"Email failed to send to {activation_request.user.email}",
                        level="WARNING",
                    )
            except Exception as e:
                email_failed_count += 1
                self.message_user(
                    request,
                    f"Email failed to send to {activation_request.user.email}: {str(e)}",
                    level="ERROR",
                )
            rejected_count += 1
        if rejected_count > 0:
            message = f"{rejected_count} reseller request(s) rejected successfully."
            if email_success_count > 0:
                message += f" {email_success_count} email(s) sent successfully."
            if email_failed_count > 0:
                message += f" {email_failed_count} email(s) failed to send."
            self.message_user(request, message)
        else:
            self.message_user(request, "No pending requests to reject.")

    reject_requests.short_description = "Reject selected requests"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")


@admin.register(Reseller)
class ResellerAdmin(admin.ModelAdmin):
    list_display = (
        "user_image",
        "user",
        "email",
        "is_suspended",
        "total_clients",
        "total_revenue",
        "available_credit",
        "status_actions",
    )
    list_filter = ("is_suspended", "created_at")
    search_fields = ("user__email",)
    readonly_fields = (
        "total_clients",
        "total_revenue",
        "total_sims_used",
        "available_credit",
    )
    actions = ["send_credentials_email", "activate_resellers", "suspend_resellers"]

    fieldsets = (
        ("Basic Information", {"fields": ("user",)}),
        (
            "Account Limits",
            {"fields": ("max_clients", "max_sims", "credit_limit", "current_credit")},
        ),
        ("Settings", {"fields": ("is_suspended", "suspension_reason")}),
        (
            "Statistics",
            {
                "fields": (
                    "total_clients",
                    "total_revenue",
                    "total_sims_used",
                    "available_credit",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def get_urls(self):
        urls = super().get_urls()
        custom = [
            path(
                "<int:pk>/suspend-json/",
                self.admin_site.admin_view(self.suspend_single_json),
                name="resellers_reseller_suspend_json",
            ),
            path(
                "<int:pk>/activate-json/",
                self.admin_site.admin_view(self.activate_single_json),
                name="resellers_reseller_activate_json",
            ),
        ]
        return custom + urls

    def suspend_single_json(self, request, pk: int):
        if request.method != "POST":
            return JsonResponse({"ok": False, "error": "Invalid method"}, status=405)
        try:
            reseller = Reseller.objects.get(pk=pk)
            if reseller.is_suspended:
                return JsonResponse(
                    {"ok": False, "error": "Already suspended"}, status=400
                )
            reseller.is_suspended = True
            reseller.suspension_reason = "Suspended by administrator"
            reseller.save()
            try:
                send_mail(
                    subject="Reseller Account Suspended - eSIM Management System",
                    message=(
                        f"Dear {reseller.user.first_name},\n\n"
                        f"Your reseller account has been suspended by the administrator.\n\n"
                        f"Reason: Suspended by administrator\n\n"
                        f"If you have any questions, please contact our support team.\n\n"
                        f"Best regards,\n"
                        f"eSIM Management System Team"
                    ),
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[reseller.user.email],
                    fail_silently=True,
                )
            except Exception:
                pass
            return JsonResponse({"ok": True, "is_suspended": True})
        except Reseller.DoesNotExist:
            return JsonResponse({"ok": False, "error": "Not found"}, status=404)
        except Exception as e:
            return JsonResponse({"ok": False, "error": str(e)}, status=500)

    def activate_single_json(self, request, pk: int):
        if request.method != "POST":
            return JsonResponse({"ok": False, "error": "Invalid method"}, status=405)
        try:
            reseller = Reseller.objects.get(pk=pk)
            if not reseller.is_suspended:
                return JsonResponse(
                    {"ok": False, "error": "Already active"}, status=400
                )
            reseller.is_suspended = False
            reseller.suspension_reason = ""
            reseller.save()
            try:
                send_mail(
                    subject="Reseller Account Activated - eSIM Management System",
                    message=(
                        f"Dear {reseller.user.first_name},\n\n"
                        f"Your reseller account has been activated by the administrator.\n\n"
                        f"Your account is now active and you can continue using the system.\n\n"
                        f"Best regards,\n"
                        f"eSIM Management System Team"
                    ),
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[reseller.user.email],
                    fail_silently=True,
                )
            except Exception:
                pass
            return JsonResponse({"ok": True, "is_suspended": False})
        except Reseller.DoesNotExist:
            return JsonResponse({"ok": False, "error": "Not found"}, status=404)
        except Exception as e:
            return JsonResponse({"ok": False, "error": str(e)}, status=500)

    def email(self, obj):
        return obj.user.email

    email.short_description = "Email"

    def user_image(self, obj):
        """Display user profile image"""
        try:
            if hasattr(obj.user, "profile") and obj.user.profile.profile_image_url:
                return mark_safe(
                    f'<img src="{obj.user.profile.profile_image_url}" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;" alt="Profile Image">'
                )
            else:
                initials = f"{obj.user.first_name[0] if obj.user.first_name else ''}{obj.user.last_name[0] if obj.user.last_name else ''}".upper()
                return mark_safe(
                    f'<div style="width: 40px; height: 40px; border-radius: 50%; background: #007bff; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 14px;">{initials}</div>'
                )
        except Exception:
            return mark_safe(
                '<div style="width: 40px; height: 40px; border-radius: 50%; background: #6c757d; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 14px;">?</div>'
            )

    user_image.short_description = "Image"

    def status_actions(self, obj):
        suspend_url = reverse("admin:resellers_reseller_suspend_json", args=[obj.id])
        activate_url = reverse("admin:resellers_reseller_activate_json", args=[obj.id])
        if obj.is_suspended:
            buttons = f'<button onclick="activateRes(this,{obj.id})" style="background:#28a745;color:#fff;border:none;padding:3px 8px;border-radius:3px;cursor:pointer;font-size:11px;">Activate</button>'
        else:
            buttons = f'<button onclick="suspendRes(this,{obj.id})" style="background:#dc3545;color:#fff;border:none;padding:3px 8px;border-radius:3px;cursor:pointer;font-size:11px;">Suspend</button>'
        script = (
            "<script>"
            'function getCSRF(){ const m=document.cookie.match(/csrftoken=([^;]+)/); return m?m[1]:""; }'
            'async function suspendRes(btn,id){ const row=btn.closest("tr"); const r=await fetch("__SUSPEND_URL__", {method:"POST", headers: {"X-CSRFToken": getCSRF()}}); const d=await r.json(); if(d.ok){ row.querySelector("td.field-is_suspended").innerHTML="True"; btn.outerHTML = "<button onclick=\\"activateRes(this,"+id+")\\" style=\\"background:#28a745;color:#fff;border:none;padding:3px 8px;border-radius:3px;cursor:pointer;font-size:11px;\\">Activate</button>"; } else { alert(d.error||"Failed"); } }'
            'async function activateRes(btn,id){ const row=btn.closest("tr"); const r=await fetch("__ACTIVATE_URL__", {method:"POST", headers: {"X-CSRFToken": getCSRF()}}); const d=await r.json(); if(d.ok){ row.querySelector("td.field-is_suspended").innerHTML="False"; btn.outerHTML = "<button onclick=\\"suspendRes(this,"+id+")\\" style=\\"background:#dc3545;color:#fff;border:none;padding:3px 8px;border-radius:3px;cursor:pointer;font-size:11px;\\">Suspend</button>"; } else { alert(d.error||"Failed"); } }'
            "</script>"
        )
        script = script.replace("__SUSPEND_URL__", suspend_url).replace(
            "__ACTIVATE_URL__", activate_url
        )
        return mark_safe(buttons + script)

    status_actions.short_description = "Actions"

    def save_model(self, request, obj, form, change):
        is_new = obj.pk is None
        super().save_model(request, obj, form, change)
        if is_new and obj.user:
            try:
                send_mail(
                    subject="Reseller Account Created - eSIM Management System",
                    message=f"""
Dear {obj.user.first_name},

Your reseller account has been created by the administrator.

Your account details:
- Email: {obj.user.email}
- Max Clients: {obj.max_clients}
- Max SIMs: {obj.max_sims}
- Credit Limit: ${obj.credit_limit}

You can now log in to your reseller dashboard using your credentials.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[obj.user.email],
                    fail_silently=True,
                )
                self.message_user(
                    request, f"Credentials email sent to {obj.user.email}"
                )
            except Exception as e:
                self.message_user(
                    request, f"Failed to send credentials email: {str(e)}"
                )

    def send_credentials_email(self, request, queryset):
        sent_count = 0
        for reseller in queryset:
            try:
                send_mail(
                    subject="Reseller Account Credentials - eSIM Management System",
                    message=f"""
Dear {reseller.user.first_name},

Your reseller account credentials:

Email: {reseller.user.email}

Your account details:
- Max Clients: {reseller.max_clients}
- Max SIMs: {reseller.max_sims}
- Credit Limit: ${reseller.credit_limit}

You can log in to your reseller dashboard using these credentials.

Best regards,
eSIM Management System Team
                    """,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[reseller.user.email],
                    fail_silently=True,
                )
                sent_count += 1
            except Exception as e:
                self.message_user(
                    request, f"Failed to send email to {reseller.user.email}: {str(e)}"
                )
        self.message_user(request, f"Credentials email sent to {sent_count} resellers.")

    send_credentials_email.short_description = (
        "Send credentials email to selected resellers"
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")
