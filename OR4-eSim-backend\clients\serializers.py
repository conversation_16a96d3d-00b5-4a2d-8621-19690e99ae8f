from django.db.models import Count, Sum
from django.utils import timezone
from rest_framework import serializers

from .models import Client, SupportTicket


class ClientActivitySerializer(serializers.Serializer):
    """Serializer for client activity information"""

    last_activity = serializers.DateTimeField()
    total_logins = serializers.IntegerField()
    days_since_last_activity = serializers.SerializerMethodField()
    activity_status = serializers.SerializerMethodField()

    def get_days_since_last_activity(self, obj):
        if obj.last_activity:
            return (timezone.now().date() - obj.last_activity.date()).days
        return None

    def get_activity_status(self, obj):
        if not obj.last_activity:
            return "never_active"

        days_since = (timezone.now().date() - obj.last_activity.date()).days
        if days_since == 0:
            return "active_today"
        elif days_since <= 7:
            return "active_this_week"
        elif days_since <= 30:
            return "active_this_month"
        else:
            return "inactive"


class ClientDetailSerializer(serializers.ModelSerializer):
    """Detailed client serializer with comprehensive information"""

    user = serializers.SerializerMethodField()
    reseller = serializers.SerializerMethodField()
    statistics = serializers.SerializerMethodField()
    admin_override_by = serializers.SerializerMethodField()
    client_type_display = serializers.CharField(
        source="get_client_type_display", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    tier_display = serializers.CharField(source="get_tier_display", read_only=True)
    activity_info = serializers.SerializerMethodField()

    class Meta:
        model = Client
        fields = [
            "id",
            "user",
            "reseller",
            "client_type",
            "client_type_display",
            "full_name",
            "email",
            "phone_number",
            "address",
            "city",
            "country",
            "passport_number",
            "national_id",
            "country_of_travel",
            "date_of_travel",
            "status",
            "status_display",
            "tier",
            "tier_display",
            "is_active",
            "is_blocked",
            "last_login",
            "last_activity",
            "total_logins",
            "current_plan",
            "plan_start_date",
            "plan_end_date",
            "auto_renewal",
            "is_plan_active",
            "preferred_package",
            "preferred_network",
            "admin_notes",
            "admin_override_reason",
            "admin_override_by",
            "admin_override_at",
            "created_at",
            "updated_at",
            "statistics",
            "activity_info",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "is_plan_active"]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data

    def get_reseller(self, obj):
        if obj.reseller:
            from resellers.serializers import ResellerSerializer

            return ResellerSerializer(obj.reseller).data
        return None

    def get_admin_override_by(self, obj):
        if obj.admin_override_by:
            return {
                "id": obj.admin_override_by.id,
                "email": obj.admin_override_by.email,
                "full_name": f"{obj.admin_override_by.first_name} {obj.admin_override_by.last_name}",
            }
        return None

    def get_activity_info(self, obj):
        """Get comprehensive activity information"""
        return ClientActivitySerializer(obj).data

    def get_statistics(self, obj):
        """Get client statistics"""
        return {
            "total_orders": obj.total_orders,
            "total_spent": float(obj.total_spent),
            "total_esims": obj.total_esims,
            "active_esims": obj.active_esims,
            "days_since_registration": (
                timezone.now().date() - obj.created_at.date()
            ).days,
            "days_since_last_activity": (
                (timezone.now().date() - obj.last_activity.date())
                if obj.last_activity
                else None
            ),
        }


class ResellerClientListSerializer(serializers.ModelSerializer):
    """Serializer for listing clients under a reseller with activity info"""

    user = serializers.SerializerMethodField()
    statistics = serializers.SerializerMethodField()
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    tier_display = serializers.CharField(source="get_tier_display", read_only=True)
    client_type_display = serializers.CharField(
        source="get_client_type_display", read_only=True
    )
    activity_info = serializers.SerializerMethodField()
    plan_info = serializers.SerializerMethodField()

    class Meta:
        model = Client
        fields = [
            "id",
            "user",
            "full_name",
            "email",
            "phone_number",
            "client_type",
            "client_type_display",
            "status",
            "status_display",
            "tier",
            "tier_display",
            "last_activity",
            "total_logins",
            "current_plan",
            "is_plan_active",
            "created_at",
            "statistics",
            "activity_info",
            "plan_info",
        ]
        read_only_fields = ["id", "created_at", "is_plan_active"]

    def get_user(self, obj):
        return {
            "id": obj.user.id,
            "email": obj.user.email,
            "first_name": obj.user.first_name,
            "last_name": obj.user.last_name,
        }

    def get_statistics(self, obj):
        """Get basic client statistics"""
        return {
            "total_orders": obj.total_orders,
            "total_spent": float(obj.total_spent),
            "total_esims": obj.total_esims,
            "active_esims": obj.active_esims,
        }

    def get_activity_info(self, obj):
        """Get activity information for list view"""
        return ClientActivitySerializer(obj).data

    def get_plan_info(self, obj):
        """Get plan information"""
        return {
            "current_plan": obj.current_plan,
            "is_plan_active": obj.is_plan_active,
            "plan_start_date": obj.plan_start_date,
            "plan_end_date": obj.plan_end_date,
            "auto_renewal": obj.auto_renewal,
        }


class ClientCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating clients"""

    user_id = serializers.IntegerField(write_only=True)
    reseller_id = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = Client
        fields = [
            "user_id",
            "reseller_id",
            "client_type",
            "full_name",
            "email",
            "phone_number",
            "address",
            "city",
            "country",
            "passport_number",
            "national_id",
            "country_of_travel",
            "date_of_travel",
            "preferred_package",
            "preferred_network",
        ]

    def validate_user_id(self, value):
        from accounts.models import User

        try:
            user = User.objects.get(id=value, role__in=["client", "public_user"])
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or not a client")
        return value

    def validate_reseller_id(self, value):
        if value:
            from resellers.models import Reseller

            try:
                reseller = Reseller.objects.get(id=value)
            except Reseller.DoesNotExist:
                raise serializers.ValidationError("Reseller not found")
        return value

    def validate(self, data):
        client_type = data.get("client_type")
        reseller_id = data.get("reseller_id")

        if client_type == Client.ClientType.RESELLER_CLIENT and not reseller_id:
            raise serializers.ValidationError(
                "Reseller is required for reseller clients"
            )

        if client_type == Client.ClientType.DIRECT_USER and reseller_id:
            raise serializers.ValidationError(
                "Reseller should not be set for direct users"
            )

        return data

    def create(self, validated_data):
        user_id = validated_data.pop("user_id")
        reseller_id = validated_data.pop("reseller_id", None)

        from accounts.models import User
        from resellers.models import Reseller

        user = User.objects.get(id=user_id)

        if reseller_id:
            reseller = Reseller.objects.get(id=reseller_id)
            validated_data["reseller"] = reseller

        validated_data["user"] = user
        return super().create(validated_data)


class ClientUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating client data"""

    class Meta:
        model = Client
        fields = [
            "full_name",
            "email",
            "phone_number",
            "address",
            "city",
            "country",
            "passport_number",
            "national_id",
            "country_of_travel",
            "date_of_travel",
            "preferred_package",
            "preferred_network",
        ]


class ClientAdminControlSerializer(serializers.Serializer):
    """Serializer for admin control actions on clients"""

    action = serializers.ChoiceField(
        choices=[
            "block",
            "unblock",
            "upgrade_tier",
            "downgrade_tier",
            "suspend",
            "activate",
            "add_notes",
        ]
    )
    reason = serializers.CharField(max_length=200, required=False)
    new_tier = serializers.ChoiceField(
        choices=Client.ClientTier.choices, required=False
    )
    notes = serializers.CharField(required=False)

    def validate(self, data):
        action = data.get("action")

        if action in ["upgrade_tier", "downgrade_tier"] and not data.get("new_tier"):
            raise serializers.ValidationError("new_tier is required for tier changes")

        if action in ["block", "unblock", "suspend", "activate"] and not data.get(
            "reason"
        ):
            raise serializers.ValidationError("reason is required for status changes")

        return data


class ClientStatisticsSerializer(serializers.Serializer):
    """Serializer for client statistics"""

    total_clients = serializers.IntegerField()
    active_clients = serializers.IntegerField()
    blocked_clients = serializers.IntegerField()
    suspended_clients = serializers.IntegerField()
    new_clients_this_month = serializers.IntegerField()
    clients_by_tier = serializers.DictField()
    clients_by_status = serializers.DictField()
    clients_by_type = serializers.DictField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_orders_per_client = serializers.FloatField()
    top_clients = serializers.ListField()


class ClientSerializer(serializers.ModelSerializer):
    """Client serializer"""

    user = serializers.SerializerMethodField()
    reseller = serializers.SerializerMethodField()
    total_orders = serializers.SerializerMethodField()
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    tier_display = serializers.CharField(source="get_tier_display", read_only=True)
    client_type_display = serializers.CharField(
        source="get_client_type_display", read_only=True
    )

    class Meta:
        model = Client
        fields = [
            "id",
            "user",
            "reseller",
            "client_type",
            "client_type_display",
            "full_name",
            "email",
            "phone_number",
            "address",
            "city",
            "country",
            "passport_number",
            "national_id",
            "country_of_travel",
            "date_of_travel",
            "status",
            "status_display",
            "tier",
            "tier_display",
            "is_active",
            "is_blocked",
            "last_activity",
            "current_plan",
            "is_plan_active",
            "preferred_package",
            "preferred_network",
            "created_at",
            "updated_at",
            "total_orders",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "is_plan_active"]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data

    def get_reseller(self, obj):
        if obj.reseller:
            from resellers.serializers import ResellerSerializer

            return ResellerSerializer(obj.reseller).data
        return None

    def get_total_orders(self, obj):
        return obj.total_orders


class SupportTicketSerializer(serializers.ModelSerializer):
    """Support ticket serializer"""

    client = serializers.SerializerMethodField()

    class Meta:
        model = SupportTicket
        fields = [
            "id",
            "client",
            "subject",
            "description",
            "status",
            "assigned_to",
            "created_at",
            "updated_at",
            "resolved_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "resolved_at"]

    def get_client(self, obj):
        return ClientSerializer(obj.client).data


class SupportTicketCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating support tickets"""

    class Meta:
        model = SupportTicket
        fields = ["subject", "description"]

    def create(self, validated_data):
        request = self.context["request"]
        validated_data["client"] = getattr(request.user, "client_profile", None)
        return super().create(validated_data)
