import re
import uuid
from datetime import datetime

from django.core.exceptions import ValidationError
from django.http import Http404
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import exception_handler


def custom_exception_handler(exc, context):
    """
    Custom exception handler to return consistent API responses
    """
    # Skip custom handling for Swagger schema generation
    request = context.get("request")
    if request and "swagger" in request.path or "api-docs" in request.path:
        return exception_handler(exc, context)

    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)

    if response is not None:
        # Customize the response format
        response.data = {
            "success": False,
            "message": response.data.get("detail", "An error occurred"),
            "data": None,
            "errors": response.data,
        }
        return response

    # Handle other exceptions
    if isinstance(exc, ValidationError):
        return Response(
            {
                "success": False,
                "message": "Validation error",
                "data": None,
                "errors": (
                    exc.message_dict if hasattr(exc, "message_dict") else str(exc)
                ),
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    if isinstance(exc, Http404):
        return Response(
            {
                "success": False,
                "message": "Resource not found",
                "data": None,
                "errors": None,
            },
            status=status.HTTP_404_NOT_FOUND,
        )

    # Handle unexpected exceptions
    return Response(
        {
            "success": False,
            "message": "An unexpected error occurred",
            "data": None,
            "errors": None,
        },
        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )


def create_success_response(data=None, message="Success"):
    """
    Create a standardized success response
    """
    return {"success": True, "message": message, "data": data, "errors": None}


def create_error_response(message="An error occurred", errors=None, status_code=400):
    """
    Create a standardized error response
    """
    return {"success": False, "message": message, "data": None, "errors": errors}


def get_user_role(user):
    """
    Get the role of a user
    """
    if user.is_authenticated:
        return user.role
    return None


def is_admin(user):
    """
    Check if user is admin
    """
    return user.is_authenticated and user.role == "admin"


def is_reseller(user):
    """
    Check if user is reseller
    """
    return user.is_authenticated and user.role == "reseller"


def is_client(user):
    """
    Check if user is client
    """
    return user.is_authenticated and user.role == "client"


def is_public_user(user):
    """
    Check if user is public user
    """
    return user.is_authenticated and user.role == "public_user"


def format_currency(amount, currency="USD"):
    """
    Format currency amount
    """
    if currency == "USD":
        return f"${amount:.2f}"
    return f"{amount:.2f} {currency}"


def validate_phone_number(phone_number):
    """
    Validate phone number format
    """
    # Basic phone number validation
    pattern = r"^\+?1?\d{9,15}$"
    return re.match(pattern, phone_number) is not None


def generate_order_number():
    """
    Generate unique order number
    """
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = str(uuid.uuid4())[:8].upper()
    return f"ORD-{timestamp}-{unique_id}"


def generate_transaction_id():
    """
    Generate unique transaction ID
    """
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = str(uuid.uuid4())[:8].upper()
    return f"TXN-{timestamp}-{unique_id}"


def calculate_tax(amount, tax_rate=0.0):
    """
    Calculate tax amount
    """
    return amount * (tax_rate / 100)


def calculate_delivery_fee(base_amount, delivery_fee=0.0):
    """
    Calculate delivery fee
    """
    return delivery_fee


def calculate_total_amount(
    subtotal, tax_amount=0.0, delivery_fee=0.0, discount_amount=0.0
):
    """
    Calculate total amount
    """
    return subtotal + tax_amount + delivery_fee - discount_amount
