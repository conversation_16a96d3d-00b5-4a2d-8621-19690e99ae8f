import random
from datetime import date, timedelta
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.db import models, transaction
from django.utils import timezone

from accounts.models import User, UserProfile
from clients.models import Client, SupportTicket
from orders.models import (
    DeliveryTracking,
    Order,
    OrderItem,
    OrderNotification,
    OrderStatusHistory,
)
from resellers.models import Reseller


class Command(BaseCommand):
    help = "Create order history and support tickets for existing dummy public users"

    def add_arguments(self, parser):
        parser.add_argument(
            "--orders-per-user",
            type=int,
            default=3,
            help="Number of orders to create per user (default: 3)",
        )
        parser.add_argument(
            "--tickets-per-user",
            type=int,
            default=2,
            help="Number of support tickets to create per user (default: 2)",
        )
        parser.add_argument(
            "--clear-existing",
            action="store_true",
            help="Clear existing orders and tickets before creating new ones",
        )
        parser.add_argument(
            "--users-only",
            action="store_true",
            help="Create orders only for users without existing orders",
        )

    def handle(self, *args, **options):
        orders_per_user = options["orders_per_user"]
        tickets_per_user = options["tickets_per_user"]
        clear_existing = options["clear_existing"]
        users_only = options["users_only"]

        if clear_existing:
            self.stdout.write("Clearing existing orders and support tickets...")
            Order.objects.filter(
                client__client_type=Client.ClientType.DIRECT_USER
            ).delete()
            SupportTicket.objects.filter(
                client__client_type=Client.ClientType.DIRECT_USER
            ).delete()
            self.stdout.write(self.style.SUCCESS("Existing orders and tickets cleared"))

        # Get existing public users
        public_users = Client.objects.filter(client_type=Client.ClientType.DIRECT_USER)
        admin_users = list(User.objects.filter(role="admin"))
        resellers = list(Reseller.objects.all())

        if not public_users.exists():
            self.stdout.write(
                self.style.ERROR(
                    "No public users found. Please create public users first."
                )
            )
            return

        if not admin_users:
            self.stdout.write(
                self.style.ERROR(
                    "No admin users found. Please create admin users first."
                )
            )
            return

        # Product data for orders
        products = [
            {
                "name": "Global eSIM Plan Basic",
                "description": "Basic global eSIM with 1GB data for 30 days",
                "price": Decimal("19.99"),
                "type": "esim",
            },
            {
                "name": "Global eSIM Plan Premium",
                "description": "Premium global eSIM with 5GB data for 30 days",
                "price": Decimal("49.99"),
                "type": "esim",
            },
            {
                "name": "Europe eSIM Plan",
                "description": "Europe-specific eSIM with 3GB data for 15 days",
                "price": Decimal("29.99"),
                "type": "esim",
            },
            {
                "name": "Asia eSIM Plan",
                "description": "Asia-specific eSIM with 2GB data for 10 days",
                "price": Decimal("24.99"),
                "type": "esim",
            },
            {
                "name": "Physical SIM Card Global",
                "description": "Physical SIM card with global coverage",
                "price": Decimal("15.99"),
                "type": "sim",
            },
            {
                "name": "Physical SIM Card Europe",
                "description": "Physical SIM card for Europe",
                "price": Decimal("12.99"),
                "type": "sim",
            },
            {
                "name": "Business eSIM Plan",
                "description": "Business eSIM with 10GB data and priority support",
                "price": Decimal("89.99"),
                "type": "esim",
            },
            {
                "name": "Student eSIM Plan",
                "description": "Student discount eSIM with 2GB data",
                "price": Decimal("14.99"),
                "type": "esim",
            },
        ]

        # Support ticket data
        ticket_subjects = [
            "SIM activation issue",
            "Network connectivity problem",
            "Billing inquiry",
            "Data usage question",
            "Travel plan activation",
            "Account access issue",
            "Payment method update",
            "Plan upgrade request",
            "Delivery tracking issue",
            "Service coverage question",
            "Data speed problem",
            "Account verification needed",
            "Plan cancellation request",
            "Technical support needed",
            "Billing dispute",
            "Service outage report",
            "Device compatibility issue",
            "International roaming question",
        ]

        ticket_descriptions = [
            "I am having trouble activating my SIM card. Please help.",
            "My network connection is not working properly.",
            "I have a question about my recent bill.",
            "How can I check my data usage?",
            "I need help activating my travel plan.",
            "I cannot access my account.",
            "I want to update my payment method.",
            "I would like to upgrade my current plan.",
            "I cannot track my order delivery.",
            "What countries are covered by my plan?",
            "My data speed is very slow.",
            "I need to verify my account.",
            "I want to cancel my current plan.",
            "I need technical assistance.",
            "I dispute the charges on my bill.",
            "I cannot connect to the network.",
            "Will this work with my device?",
            "How does international roaming work?",
        ]

        # Status progression with probabilities
        status_progression = [
            ("pending", 0.1),
            ("confirmed", 0.15),
            ("processing", 0.2),
            ("dispatched", 0.25),
            ("delivered", 0.2),
            ("activated", 0.05),
            ("completed", 0.03),
            ("cancelled", 0.02),
        ]

        # Ticket status distribution
        ticket_status_distribution = [
            ("open", 0.3),
            ("in_progress", 0.25),
            ("resolved", 0.3),
            ("closed", 0.15),
        ]

        self.stdout.write(
            f"Creating orders and tickets for {public_users.count()} public users..."
        )

        total_orders_created = 0
        total_tickets_created = 0

        with transaction.atomic():
            for client in public_users:
                # Check if user already has orders (if users_only flag is set)
                if users_only and client.orders.exists():
                    continue

                # Create orders for this user
                for i in range(orders_per_user):
                    # Select random product
                    product = random.choice(products)

                    # Generate order number
                    order_number = f"ORD-{timezone.now().strftime('%Y%m%d')}-{client.id:03d}-{i+1:02d}"

                    # Calculate pricing
                    quantity = random.randint(1, 3)
                    unit_price = product["price"]
                    subtotal = unit_price * quantity
                    tax_amount = subtotal * Decimal("0.08")  # 8% tax
                    delivery_fee = (
                        Decimal("5.99") if product["type"] == "sim" else Decimal("0.00")
                    )
                    total_amount = subtotal + tax_amount + delivery_fee

                    # Select order source
                    order_source = random.choice(["app", "reseller", "admin"])

                    # Select status based on probabilities
                    status = random.choices(
                        [s[0] for s in status_progression],
                        weights=[s[1] for s in status_progression],
                    )[0]

                    # Generate timestamps based on status
                    created_at = timezone.now() - timedelta(days=random.randint(1, 90))
                    confirmed_at = None
                    dispatched_at = None
                    delivered_at = None
                    completed_at = None
                    cancelled_at = None

                    if status in [
                        "confirmed",
                        "processing",
                        "dispatched",
                        "delivered",
                        "activated",
                        "completed",
                    ]:
                        confirmed_at = created_at + timedelta(
                            hours=random.randint(1, 24)
                        )

                    if status in ["dispatched", "delivered", "activated", "completed"]:
                        dispatched_at = confirmed_at + timedelta(
                            hours=random.randint(2, 48)
                        )

                    if status in ["delivered", "activated", "completed"]:
                        delivered_at = dispatched_at + timedelta(
                            hours=random.randint(1, 72)
                        )

                    if status == "completed":
                        completed_at = delivered_at + timedelta(
                            hours=random.randint(1, 24)
                        )

                    if status == "cancelled":
                        cancelled_at = created_at + timedelta(
                            hours=random.randint(1, 12)
                        )

                    # Create order
                    order = Order.objects.create(
                        order_number=order_number,
                        order_type=product["type"],
                        order_source=order_source,
                        status=status,
                        reseller=(
                            random.choice(resellers)
                            if resellers and random.random() < 0.3
                            else None
                        ),
                        client=client,
                        product_name=product["name"],
                        product_description=product["description"],
                        quantity=quantity,
                        unit_price=unit_price,
                        subtotal=subtotal,
                        tax_amount=tax_amount,
                        delivery_fee=delivery_fee,
                        total_amount=total_amount,
                        delivery_address=client.address,
                        delivery_city=client.city,
                        delivery_country=client.country,
                        delivery_phone=client.phone_number,
                        created_at=created_at,
                        confirmed_at=confirmed_at,
                        dispatched_at=dispatched_at,
                        delivered_at=delivered_at,
                        completed_at=completed_at,
                        cancelled_at=cancelled_at,
                    )

                    # Create order item
                    OrderItem.objects.create(
                        order=order,
                        product_name=product["name"],
                        product_description=product["description"],
                        quantity=quantity,
                        unit_price=unit_price,
                        total_price=subtotal,
                    )

                    # Create status history
                    statuses = ["pending"]
                    if confirmed_at:
                        statuses.append("confirmed")
                    if dispatched_at:
                        statuses.append("dispatched")
                    if delivered_at:
                        statuses.append("delivered")
                    if completed_at:
                        statuses.append("completed")
                    if cancelled_at:
                        statuses.append("cancelled")

                    for j in range(len(statuses) - 1):
                        OrderStatusHistory.objects.create(
                            order=order,
                            old_status=statuses[j],
                            new_status=statuses[j + 1],
                            changed_by=random.choice(admin_users),
                            notes=f"Status changed from {statuses[j]} to {statuses[j + 1]}",
                        )

                    # Create delivery tracking for dispatched/delivered orders
                    if status in ["dispatched", "delivered", "activated", "completed"]:
                        couriers = [
                            "FedEx",
                            "UPS",
                            "DHL",
                            "USPS",
                            "Royal Mail",
                            "Canada Post",
                            "Australia Post",
                        ]
                        courier = random.choice(couriers)
                        tracking_number = f"{courier[:3].upper()}{random.randint(*********, *********)}"

                        tracking_status = "in_transit"
                        if status in ["delivered", "activated", "completed"]:
                            tracking_status = "delivered"

                        DeliveryTracking.objects.create(
                            order=order,
                            tracking_number=tracking_number,
                            courier_name=courier,
                            status=tracking_status,
                            current_location=client.city,
                            estimated_delivery=(
                                dispatched_at + timedelta(days=random.randint(1, 5))
                                if dispatched_at
                                else None
                            ),
                            actual_delivery=delivered_at if delivered_at else None,
                        )

                    # Create notifications
                    notification_types = {
                        "confirmed": "ORDER_CONFIRMED",
                        "dispatched": "ORDER_DISPATCHED",
                        "delivered": "ORDER_DELIVERED",
                        "activated": "ORDER_ACTIVATED",
                        "cancelled": "ORDER_CANCELLED",
                    }

                    if status in notification_types:
                        notification_type = notification_types[status]
                        recipient = client.email or client.phone_number

                        if recipient:
                            OrderNotification.objects.create(
                                order=order,
                                notification_type=notification_type,
                                notification_method=(
                                    "email" if "@" in recipient else "sms"
                                ),
                                recipient=recipient,
                                message=f"Your order {order_number} has been {status}",
                                delivered=True,
                                delivered_at=timezone.now(),
                            )

                    total_orders_created += 1

                # Create support tickets for this user
                for i in range(tickets_per_user):
                    # Select ticket status based on probabilities
                    ticket_status = random.choices(
                        [s[0] for s in ticket_status_distribution],
                        weights=[s[1] for s in ticket_status_distribution],
                    )[0]

                    # Generate ticket timestamps
                    ticket_created_at = timezone.now() - timedelta(
                        days=random.randint(1, 60)
                    )
                    ticket_resolved_at = None

                    if ticket_status in ["resolved", "closed"]:
                        ticket_resolved_at = ticket_created_at + timedelta(
                            days=random.randint(1, 14)
                        )

                    SupportTicket.objects.create(
                        client=client,
                        subject=random.choice(ticket_subjects),
                        description=random.choice(ticket_descriptions),
                        status=ticket_status,
                        assigned_to=(
                            random.choice(admin_users)
                            if ticket_status in ["in_progress", "resolved"]
                            else None
                        ),
                        created_at=ticket_created_at,
                        resolved_at=ticket_resolved_at,
                    )

                    total_tickets_created += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {total_orders_created} orders and {total_tickets_created} support tickets!"
            )
        )

        # Print summary
        total_orders = Order.objects.filter(
            client__client_type=Client.ClientType.DIRECT_USER
        ).count()
        total_tickets = SupportTicket.objects.filter(
            client__client_type=Client.ClientType.DIRECT_USER
        ).count()

        self.stdout.write("\nSummary:")
        self.stdout.write(f"Total Orders for Public Users: {total_orders}")
        self.stdout.write(f"Total Support Tickets for Public Users: {total_tickets}")

        # Orders by status
        orders_by_status = (
            Order.objects.filter(client__client_type=Client.ClientType.DIRECT_USER)
            .values("status")
            .annotate(count=models.Count("id"))
            .order_by("status")
        )

        self.stdout.write("\nOrders by Status:")
        for status_count in orders_by_status:
            self.stdout.write(f'{status_count["status"]}: {status_count["count"]}')

        # Tickets by status
        tickets_by_status = (
            SupportTicket.objects.filter(
                client__client_type=Client.ClientType.DIRECT_USER
            )
            .values("status")
            .annotate(count=models.Count("id"))
            .order_by("status")
        )

        self.stdout.write("\nSupport Tickets by Status:")
        for status_count in tickets_by_status:
            self.stdout.write(f'{status_count["status"]}: {status_count["count"]}')

        # Orders by source
        orders_by_source = (
            Order.objects.filter(client__client_type=Client.ClientType.DIRECT_USER)
            .values("order_source")
            .annotate(count=models.Count("id"))
            .order_by("order_source")
        )

        self.stdout.write("\nOrders by Source:")
        for source_count in orders_by_source:
            self.stdout.write(
                f'{source_count["order_source"]}: {source_count["count"]}'
            )

        # Orders by type
        orders_by_type = (
            Order.objects.filter(client__client_type=Client.ClientType.DIRECT_USER)
            .values("order_type")
            .annotate(count=models.Count("id"))
            .order_by("order_type")
        )

        self.stdout.write("\nOrders by Type:")
        for type_count in orders_by_type:
            self.stdout.write(f'{type_count["order_type"]}: {type_count["count"]}')
