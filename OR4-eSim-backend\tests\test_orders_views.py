import uuid
from unittest.mock import MagicMock, patch

import pytest
from django.urls import reverse
from rest_framework import status

from accounts.models import User
from clients.models import Client
from orders.models import (
    DeliveryTracking,
    Order,
    OrderItem,
    OrderNotification,
    OrderStatusHistory,
)
from resellers.models import Reseller


class TestOrderViews:
    """Test cases for order views"""

    @pytest.fixture
    def order_factory(self, user_factory, reseller_user):
        """Factory for creating test orders"""

        def _create_order(**kwargs):
            user, reseller = reseller_user
            client_user = user_factory()
            client = Client.objects.create(
                user=client_user,
                reseller=reseller,
                full_name="Test Client",
                phone_number="+**********",
                email="<EMAIL>",
                client_type="reseller_client",
            )

            # Generate unique order number using timestamp and random string
            import time

            order_number = f"ORDER-{int(time.time())}-{uuid.uuid4().hex[:6].upper()}"

            defaults = {
                "order_number": order_number,
                "order_type": "sim",
                "order_source": "reseller",
                "status": "pending",
                "reseller": reseller,
                "client": client,
                "product_name": "Test SIM Card",
                "product_description": "Test SIM card for testing",
                "quantity": 1,
                "unit_price": "10.00",
                "subtotal": "10.00",
                "tax_amount": "0.80",
                "delivery_fee": "2.00",
                "total_amount": "12.80",
            }
            defaults.update(kwargs)

            return Order.objects.create(**defaults)

        return _create_order

    @pytest.mark.django_db
    def test_order_list(self, authenticated_client):
        """Test order list view"""
        client, user = authenticated_client
        url = reverse("order-list")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_order_create(self, reseller_client, order_factory, user_factory):
        """Test order creation"""
        api_client, user, reseller = reseller_client

        # Create a client for the order
        from clients.models import Client

        client_user = user_factory()
        client = Client.objects.create(
            user=client_user,
            reseller=reseller,
            full_name="Test Client",
            phone_number="+**********",
            email="<EMAIL>",
            client_type="reseller_client",
        )

        url = reverse("order-list")

        data = {
            "order_type": "sim",
            "order_source": "reseller",
            "reseller": reseller.pk,
            "client": client.pk,
            "product_name": "Test SIM Card",
            "product_description": "Test SIM card for testing",
            "quantity": 1,
            "unit_price": "10.00",
            "subtotal": "10.00",
            "tax_amount": "0.80",
            "delivery_fee": "2.00",
            "total_amount": "12.80",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["product_name"] == "Test SIM Card"

    @pytest.mark.django_db
    def test_order_detail(self, admin_client, order_factory):
        """Test order detail view"""
        client, user = admin_client
        test_order = order_factory()

        url = reverse("order-detail", kwargs={"pk": test_order.pk})
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["order_number"] == test_order.order_number

    @pytest.mark.django_db
    def test_order_update(self, reseller_client, order_factory):
        """Test order update"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("order-detail", kwargs={"pk": test_order.pk})
        data = {"status": "confirmed"}

        response = api_client.patch(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["status"] == "confirmed"

    @pytest.mark.django_db
    def test_order_delete(self, reseller_client, order_factory):
        """Test order deletion"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("order-detail", kwargs={"pk": test_order.pk})
        response = api_client.delete(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT

    @pytest.mark.django_db
    def test_order_filter_by_status(self, admin_client, order_factory):
        """Test order filtering by status"""
        client, user = admin_client
        order_factory(status="pending")
        order_factory(status="confirmed")

        url = reverse("order-list")
        response = client.get(url, {"status": "pending"})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) >= 1

    @pytest.mark.django_db
    def test_order_filter_by_date_range(self, admin_client, order_factory):
        """Test order filtering by date range"""
        client, user = admin_client
        order_factory()

        url = reverse("order-list")
        response = client.get(url, {"date_from": "2024-01-01", "date_to": "2024-12-31"})

        assert response.status_code == status.HTTP_200_OK


class TestOrderItemViews:
    """Test cases for order item views"""

    @pytest.mark.django_db
    def test_order_item_list(self, admin_client, order_factory):
        """Test order item list view"""
        client, user = admin_client
        test_order = order_factory()

        url = reverse("orderitem-list")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_order_item_create(self, reseller_client, order_factory):
        """Test order item creation"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("orderitem-list")
        data = {
            "order": test_order.pk,
            "product_name": "Test Product",
            "quantity": 1,
            "unit_price": "10.00",
            "total_price": "10.00",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED


class TestOrderStatusHistoryViews:
    """Test cases for order status history views"""

    @pytest.mark.django_db
    def test_order_status_history_list(self, admin_client, order_factory):
        """Test order status history list view"""
        client, user = admin_client
        test_order = order_factory()

        url = reverse("orderstatushistory-list")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_order_status_history_create(self, reseller_client, order_factory):
        """Test order status history creation"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("orderstatushistory-list")
        data = {
            "order": test_order.pk,
            "old_status": "pending",
            "new_status": "confirmed",
            "changed_by": user.pk,
            "notes": "Order confirmed",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED


class TestDeliveryTrackingViews:
    """Test cases for delivery tracking views"""

    @pytest.mark.django_db
    def test_delivery_tracking_list(self, admin_client, order_factory):
        """Test delivery tracking list view"""
        client, user = admin_client
        test_order = order_factory()

        url = reverse("deliverytracking-list")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_delivery_tracking_create(self, reseller_client, order_factory):
        """Test delivery tracking creation"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("deliverytracking-list")
        data = {
            "order": test_order.pk,
            "tracking_number": "TRACK123",
            "courier_name": "Test Carrier",
            "status": "in_transit",
            "estimated_delivery": "2024-12-31",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.django_db
    def test_delivery_tracking_update(self, reseller_client, order_factory):
        """Test delivery tracking update"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)
        tracking = DeliveryTracking.objects.create(
            order=test_order,
            tracking_number="TRACK123",
            courier_name="Test Carrier",
            status="in_transit",
        )

        url = reverse("deliverytracking-detail", kwargs={"pk": tracking.pk})
        data = {"status": "delivered"}

        response = api_client.patch(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["status"] == "delivered"

    @pytest.mark.django_db
    def test_delivery_tracking_by_order(self, reseller_client, order_factory):
        """Test delivery tracking by order"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("deliverytracking-by-order", kwargs={"order_id": test_order.pk})
        response = api_client.get(url)

        assert (
            response.status_code == status.HTTP_404_NOT_FOUND
        )  # No tracking exists yet


class TestOrderNotificationViews:
    """Test cases for order notification views"""

    @pytest.mark.django_db
    def test_order_notification_list(self, admin_client, order_factory):
        """Test order notification list view"""
        client, user = admin_client
        test_order = order_factory()

        url = reverse("ordernotification-list")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_order_notification_create(self, reseller_client, order_factory):
        """Test order notification creation"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("ordernotification-list")
        data = {
            "order": test_order.pk,
            "notification_type": "order_confirmed",
            "notification_method": "email",
            "recipient": "<EMAIL>",
            "message": "Your order has been confirmed",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.django_db
    def test_order_notification_by_order(self, reseller_client, order_factory):
        """Test order notifications by order"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse("ordernotification-by-order", kwargs={"order_id": test_order.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.django_db
    def test_send_order_notification(self, reseller_client, order_factory):
        """Test sending order notification"""
        api_client, user, reseller = reseller_client
        test_order = order_factory(reseller=reseller)

        url = reverse(
            "ordernotification-send-notification", kwargs={"order_id": test_order.pk}
        )
        data = {
            "notification_type": "order_confirmed",
            "subject": "Order Update",
            "message": "Your order has been updated",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK


class TestOrderAnalyticsViews:
    """Test cases for order analytics views"""

    @pytest.mark.django_db
    def test_order_analytics(self, admin_client, order_factory):
        """Test order analytics"""
        client, user = admin_client
        order_factory(status="completed")
        order_factory(status="pending")

        url = reverse("order-analytics")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "total_orders" in response.data["data"]

    @pytest.mark.django_db
    def test_order_revenue_analytics(self, admin_client, order_factory):
        """Test order revenue analytics"""
        client, user = admin_client
        order_factory(total_amount="100.00", status="completed")
        order_factory(total_amount="50.00", status="completed")

        url = reverse("order-revenue-analytics")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "total_revenue" in response.data["data"]

    @pytest.mark.django_db
    def test_order_status_analytics(self, admin_client, order_factory):
        """Test order status analytics"""
        client, user = admin_client
        order_factory(status="pending")
        order_factory(status="confirmed")
        order_factory(status="completed")

        url = reverse("order-status-analytics")
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "status_distribution" in response.data["data"]
