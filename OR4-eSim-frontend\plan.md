# Frontend Development Plan - Admin Panel

> **Note**: Please update the backend from here

## 🛠️ Technology Stack
- **Framework**: React.js (functional components + hooks)
- **Styling**: TailwindCSS (modern, responsive UI)
- **Routing**: React Router DOM (v6)
- **Authentication**: Route protection (PrivateRoutes)
- **Theme**: Dark/Light Theme Toggle
- **Architecture**: Clean and scalable folder structure
- **Design**: Responsive design with mobile/tablet support

## 📁 Project Structure
```
src/
├── components/
│   ├── common/
│   │   ├── Layout/
│   │   ├── Sidebar/
│   │   ├── Header/
│   │   ├── ThemeToggle/
│   │   ├── LoadingSpinner/
│   │   └── Modal/
│   ├── auth/
│   │   ├── LoginForm/
│   │   ├── ForgotPassword/
│   │   └── ResetPassword/
│   ├── dashboard/
│   ├── resellers/
│   ├── users/
│   ├── orders/
│   ├── payments/
│   ├── reports/
│   └── settings/
├── pages/
├── hooks/
├── context/
├── utils/
├── services/
└── assets/
```

## 🔐 Authentication Module

### Screens:
1. **Login Page** (`/login`)
   - Email/username and password fields
   - Remember me checkbox
   - Forgot password link
   - Secure authentication with JWT

2. **Forgot Password** (`/forgot-password`)
   - Email input for password reset
   - Success message display

3. **Reset Password** (`/reset-password/:token`)
   - New password and confirm password fields
   - Token validation

### Components:
- `LoginForm.jsx`
- `ForgotPasswordForm.jsx`
- `ResetPasswordForm.jsx`
- `PrivateRoute.jsx` (Route protection)

## 🧭 Dashboard Module

### Main Dashboard (`/dashboard`)
**Key Metrics Cards:**
- Total Users (App-based SIM buyers)
- Total Resellers
- Total Clients (under resellers)
- Daily/Monthly SIM Orders
- Revenue Generated

**Charts & Graphs:**
- Sales trends (Line/Bar chart)
- Top-performing resellers (Bar chart)
- Revenue distribution (Pie chart)

**Activity Feed:**
- Latest orders
- Recent user registrations
- Reseller activities

### Components:
- `DashboardOverview.jsx`
- `MetricsCard.jsx`
- `SalesChart.jsx`
- `ActivityFeed.jsx`
- `TopPerformers.jsx`

## 👥 Reseller Management Module

### Screens:
1. **Resellers List** (`/resellers`)
   - Data table with search, filter, pagination
   - Actions: Add, Edit, Suspend, Delete
   - Status indicators

2. **Add Reseller** (`/resellers/add`)
   - Form with reseller details
   - Account limits configuration

3. **Edit Reseller** (`/resellers/:id/edit`)
   - Pre-filled form with current data
   - Update limits and status

4. **Reseller Details** (`/resellers/:id`)
   - Detailed view with revenue, logs, activity
   - Client list under this reseller

### Components:
- `ResellersList.jsx`
- `ResellerForm.jsx`
- `ResellerDetails.jsx`
- `ResellerClients.jsx`

## 👤 Reseller Clients Module

### Screens:
1. **Clients List** (`/clients`)
   - All clients across resellers
   - Filter by reseller, status, plan
   - Admin override controls

2. **Client Details** (`/clients/:id`)
   - Client information and activity
   - Plan details and status
   - Action buttons (block, upgrade, etc.)

### Components:
- `ClientsList.jsx`
- `ClientDetails.jsx`
- `ClientActions.jsx`

## 📱 Public Users Module

### Screens:
1. **Users List** (`/users`)
   - App-based SIM buyers
   - Filter by city, status, package
   - Search functionality

2. **User Details** (`/users/:id`)
   - Personal information
   - Order history
   - Payment history
   - Support tickets
   - Action controls (block/unblock)

### Components:
- `UsersList.jsx`
- `UserDetails.jsx`
- `UserOrderHistory.jsx`
- `UserSupportTickets.jsx`

## 📦 SIM Order Management Module

### Screens:
1. **Orders List** (`/orders`)
   - Central order management
   - Filter by source (app/reseller), status, date
   - Bulk actions

2. **Order Details** (`/orders/:id`)
   - Complete order information
   - Status update controls
   - Delivery assignment
   - Notification triggers

### Components:
- `OrdersList.jsx`
- `OrderDetails.jsx`
- `OrderStatusUpdate.jsx`
- `DeliveryAssignment.jsx`

## 💳 Payments & Transactions Module

### Screens:
1. **Transactions List** (`/transactions`)
   - All payments (app users + resellers)
   - Filter by type, status, date range
   - Export functionality

2. **Transaction Details** (`/transactions/:id`)
   - Detailed transaction view
   - Invoice download
   - Refund management

### Components:
- `TransactionsList.jsx`
- `TransactionDetails.jsx`
- `RefundManagement.jsx`
- `InvoiceGenerator.jsx`

## 📊 Reports & Analytics Module

### Screens:
1. **Reports Dashboard** (`/reports`)
   - Performance metrics
   - Revenue analytics
   - User growth charts
   - Export options (PDF/Excel)

2. **Custom Reports** (`/reports/custom`)
   - Date range selection
   - Custom filters
   - Report generation

### Components:
- `ReportsDashboard.jsx`
- `CustomReports.jsx`
- `ExportOptions.jsx`
- `AnalyticsCharts.jsx`

## ⚙️ Settings & Configuration Module

### Screens:
1. **General Settings** (`/settings`)
   - Admin account settings
   - System configurations

2. **Pricing Settings** (`/settings/pricing`)
   - Delivery fees, tax, service charges

3. **Notifications** (`/settings/notifications`)
   - SMS/Email templates
   - Notification preferences

4. **API Configuration** (`/settings/api`)
   - API keys management
   - Webhook configuration

5. **Branding** (`/settings/branding`)
   - Logo upload
   - Theme customization

### Components:
- `GeneralSettings.jsx`
- `PricingSettings.jsx`
- `NotificationSettings.jsx`
- `APISettings.jsx`
- `BrandingSettings.jsx`

## 🎨 Common Components

### Layout Components:
- `Layout.jsx` - Main layout wrapper
- `Sidebar.jsx` - Navigation sidebar
- `Header.jsx` - Top header with user menu
- `Breadcrumb.jsx` - Navigation breadcrumbs

### UI Components:
- `DataTable.jsx` - Reusable data table
- `SearchFilter.jsx` - Search and filter component
- `Pagination.jsx` - Pagination component
- `Modal.jsx` - Modal dialogs
- `ConfirmDialog.jsx` - Confirmation dialogs
- `StatusBadge.jsx` - Status indicators
- `ActionButton.jsx` - Action buttons
- `FormField.jsx` - Form input components

### Chart Components:
- `LineChart.jsx`
- `BarChart.jsx`
- `PieChart.jsx`
- `AreaChart.jsx`

## 🔄 State Management

### Context Providers:
- `AuthContext.jsx` - Authentication state
- `ThemeContext.jsx` - Theme management
- `NotificationContext.jsx` - Toast notifications

### Custom Hooks:
- `useAuth.js` - Authentication logic
- `useApi.js` - API calls
- `useLocalStorage.js` - Local storage management
- `usePagination.js` - Pagination logic
- `useDebounce.js` - Debounced search

## 📱 Responsive Design Strategy

### Breakpoints:
- Mobile: 320px - 768px
- Tablet: 768px - 1024px
- Desktop: 1024px+

### Mobile Adaptations:
- Collapsible sidebar
- Stacked cards on mobile
- Touch-friendly buttons
- Simplified tables with horizontal scroll

## 🚀 Implementation Phases

### Phase 1: Foundation
- Project setup with Vite/Create React App
- Basic routing structure
- Authentication system
- Layout components

### Phase 2: Core Modules
- Dashboard implementation
- User management
- Order management

### Phase 3: Advanced Features
- Reports and analytics
- Settings and configuration
- Advanced filtering and search

### Phase 4: Polish
- Performance optimization
- Accessibility improvements
- Testing and bug fixes

## 📋 Development Checklist

- [ ] Project initialization
- [ ] Routing setup
- [ ] Authentication implementation
- [ ] Layout and navigation
- [ ] Dashboard with metrics
- [ ] User management
- [ ] Reseller management
- [ ] Order management
- [ ] Payment management
- [ ] Reports and analytics
- [ ] Settings and configuration
- [ ] Responsive design
- [ ] Theme toggle
- [ ] Testing
- [ ] Performance optimization

---

# Backend Requirements & API Specifications

## 🔧 Backend Technology Stack

### Django Stack:
- **Framework**: Django 4.2+ with Django REST Framework (DRF)
- **Database**: PostgreSQL (primary) + Redis (caching/sessions)
- **Authentication**: Django REST Framework JWT / Simple JWT
- **Admin Interface**: Django Admin (customized for admin panel)
- **File Storage**: Django Storages with AWS S3 / Google Cloud Storage
- **Email Service**: Django Email Backend with SendGrid / AWS SES
- **SMS Service**: Django integration with Twilio / AWS SNS
- **Payment Gateway**: Django integrations for Stripe / PayPal
- **API Documentation**: DRF Spectacular (OpenAPI/Swagger)
- **Task Queue**: Celery with Redis/RabbitMQ
- **Deployment**: Docker containers with Gunicorn + Nginx

## 📊 Django Models & Database Schema

### Django Apps Structure:
```
backend/
├── accounts/          # User authentication & admin management
├── resellers/         # Reseller management
├── users/            # Public users (app SIM buyers)
├── orders/           # SIM order management
├── payments/         # Transaction & payment handling
├── support/          # Support ticket system
├── core/             # System settings & utilities
├── analytics/        # Reports & analytics
└── notifications/    # Email/SMS notifications
```

### Core Django Models:

#### 1. **accounts/models.py - Admin**
```python
from django.contrib.auth.models import AbstractUser
from django.db import models

class Admin(AbstractUser):
    ROLE_CHOICES = [
        ('super_admin', 'Super Admin'),
        ('admin', 'Admin'),
        ('moderator', 'Moderator'),
    ]

    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='admin')
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    is_active = models.BooleanField(default=True)
    last_login_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'admins'
```

#### 2. **resellers/models.py - Reseller**
```python
from django.db import models
from django.conf import settings

class Reseller(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('pending', 'Pending'),
    ]

    business_name = models.CharField(max_length=200)
    contact_person = models.CharField(max_length=100)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20)
    address = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    credit_limit = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    current_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    commission_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'resellers'
```

#### 3. **resellers/models.py - ResellerClient**
```python
class ResellerClient(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('suspended', 'Suspended'),
    ]

    reseller = models.ForeignKey(Reseller, on_delete=models.CASCADE, related_name='clients')
    client_name = models.CharField(max_length=100)
    client_email = models.EmailField()
    client_phone = models.CharField(max_length=20)
    plan_type = models.CharField(max_length=50)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    monthly_limit = models.IntegerField(default=0)
    current_usage = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'reseller_clients'
```

#### 4. **users/models.py - PublicUser**
```python
class PublicUser(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('blocked', 'Blocked'),
        ('pending_verification', 'Pending Verification'),
    ]

    full_name = models.CharField(max_length=100)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20, unique=True)
    address = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    date_of_birth = models.DateField()
    id_document_type = models.CharField(max_length=50)
    id_document_number = models.CharField(max_length=100)
    status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='pending_verification')
    email_verified_at = models.DateTimeField(null=True, blank=True)
    phone_verified_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'public_users'
```

#### 5. **orders/models.py - SimOrder**
```python
class SimOrder(models.Model):
    ORDER_TYPE_CHOICES = [
        ('app_user', 'App User'),
        ('reseller_client', 'Reseller Client'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('dispatched', 'Dispatched'),
        ('delivered', 'Delivered'),
        ('activated', 'Activated'),
        ('cancelled', 'Cancelled'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]

    order_number = models.CharField(max_length=50, unique=True)
    user = models.ForeignKey('users.PublicUser', on_delete=models.CASCADE, null=True, blank=True)
    reseller = models.ForeignKey('resellers.Reseller', on_delete=models.CASCADE, null=True, blank=True)
    client = models.ForeignKey('resellers.ResellerClient', on_delete=models.CASCADE, null=True, blank=True)
    order_type = models.CharField(max_length=20, choices=ORDER_TYPE_CHOICES)
    sim_type = models.CharField(max_length=20)
    network_provider = models.CharField(max_length=50)
    plan_name = models.CharField(max_length=100)
    plan_price = models.DecimalField(max_digits=10, decimal_places=2)
    delivery_address = models.TextField()
    delivery_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    delivery_tracking_number = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    activated_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'sim_orders'
```

#### 6. **payments/models.py - Transaction**
```python
class Transaction(models.Model):
    TRANSACTION_TYPE_CHOICES = [
        ('payment', 'Payment'),
        ('refund', 'Refund'),
        ('commission', 'Commission'),
        ('credit', 'Credit'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    transaction_id = models.CharField(max_length=100, unique=True)
    order = models.ForeignKey('orders.SimOrder', on_delete=models.CASCADE, null=True, blank=True)
    user = models.ForeignKey('users.PublicUser', on_delete=models.CASCADE, null=True, blank=True)
    reseller = models.ForeignKey('resellers.Reseller', on_delete=models.CASCADE, null=True, blank=True)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    payment_method = models.CharField(max_length=50)
    payment_gateway_response = models.JSONField(default=dict, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    gateway_transaction_id = models.CharField(max_length=200, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'transactions'
```

#### 7. **support/models.py - SupportTicket**
```python
class SupportTicket(models.Model):
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    ticket_number = models.CharField(max_length=50, unique=True)
    user = models.ForeignKey('users.PublicUser', on_delete=models.CASCADE, null=True, blank=True)
    reseller = models.ForeignKey('resellers.Reseller', on_delete=models.CASCADE, null=True, blank=True)
    subject = models.CharField(max_length=200)
    description = models.TextField()
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    assigned_to = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'support_tickets'
```

#### 8. **core/models.py - SystemSetting & ActivityLog**
```python
class SystemSetting(models.Model):
    SETTING_TYPE_CHOICES = [
        ('string', 'String'),
        ('number', 'Number'),
        ('boolean', 'Boolean'),
        ('json', 'JSON'),
    ]

    setting_key = models.CharField(max_length=100, unique=True)
    setting_value = models.TextField()
    setting_type = models.CharField(max_length=20, choices=SETTING_TYPE_CHOICES, default='string')
    description = models.TextField(blank=True)
    is_public = models.BooleanField(default=False)
    updated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'system_settings'

class ActivityLog(models.Model):
    USER_TYPE_CHOICES = [
        ('admin', 'Admin'),
        ('reseller', 'Reseller'),
        ('public_user', 'Public User'),
    ]

    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES)
    user_id = models.PositiveIntegerField()
    action = models.CharField(max_length=100)
    description = models.TextField()
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'activity_logs'
```

## 🔌 Django REST Framework API Endpoints

### URL Configuration Structure:
```python
# main/urls.py
urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/auth/', include('accounts.urls')),
    path('api/dashboard/', include('analytics.urls')),
    path('api/resellers/', include('resellers.urls')),
    path('api/users/', include('users.urls')),
    path('api/orders/', include('orders.urls')),
    path('api/transactions/', include('payments.urls')),
    path('api/support/', include('support.urls')),
    path('api/settings/', include('core.urls')),
    path('api/docs/', SpectacularSwaggerView.as_view()),
]
```

### Authentication APIs (accounts/urls.py)
```python
# Using Django REST Framework Simple JWT
POST /api/auth/login/
POST /api/auth/logout/
POST /api/auth/token/refresh/
POST /api/auth/password/reset/
POST /api/auth/password/reset/confirm/
GET  /api/auth/user/
```

### Dashboard APIs (analytics/urls.py)
```python
GET /api/dashboard/metrics/
GET /api/dashboard/sales-trends/
GET /api/dashboard/top-resellers/
GET /api/dashboard/recent-activities/
```

### Reseller Management APIs (resellers/urls.py)
```python
GET    /api/resellers/
POST   /api/resellers/
GET    /api/resellers/{id}/
PUT    /api/resellers/{id}/
PATCH  /api/resellers/{id}/
DELETE /api/resellers/{id}/
PATCH  /api/resellers/{id}/status/
GET    /api/resellers/{id}/clients/
GET    /api/resellers/{id}/orders/
GET    /api/resellers/{id}/transactions/
```

### User Management APIs (users/urls.py)
```python
GET    /api/users/
GET    /api/users/{id}/
PUT    /api/users/{id}/
PATCH  /api/users/{id}/status/
GET    /api/users/{id}/orders/
GET    /api/users/{id}/transactions/
GET    /api/users/{id}/support-tickets/
```

### Order Management APIs (orders/urls.py)
```python
GET    /api/orders/
POST   /api/orders/
GET    /api/orders/{id}/
PUT    /api/orders/{id}/
PATCH  /api/orders/{id}/status/
POST   /api/orders/{id}/assign-delivery/
GET    /api/orders/statistics/
```

### Payment & Transaction APIs (payments/urls.py)
```python
GET    /api/transactions/
GET    /api/transactions/{id}/
POST   /api/transactions/{id}/refund/
GET    /api/transactions/export/
POST   /api/payments/process/
GET    /api/payments/gateways/
```

### Support APIs (support/urls.py)
```python
GET    /api/support/tickets/
POST   /api/support/tickets/
GET    /api/support/tickets/{id}/
PUT    /api/support/tickets/{id}/
PATCH  /api/support/tickets/{id}/assign/
POST   /api/support/tickets/{id}/reply/
```

## 🔐 Django Security Implementation

### Authentication & Authorization:
- **Django REST Framework Simple JWT** for token-based authentication
- **Django Permissions & Groups** for role-based access control
- **Django's built-in password hashing** (PBKDF2/Argon2)
- **Django Rate Limiting** with django-ratelimit
- **Django Axes** for account lockout after failed attempts

### Django Security Features:
- **Django's built-in CSRF protection**
- **Django ORM** prevents SQL injection
- **Django's XSS protection** with template auto-escaping
- **Django Security Middleware** for HTTPS enforcement
- **Django Logging** for API request/response tracking
- **Django Validators** for input validation

### Django-Specific Security Settings:
```python
# settings.py
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = ********
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_SECURE = True
```

### Privacy & Compliance:
- **Django GDPR** package for compliance
- **Django Model Audit** for tracking changes
- **Django Encrypted Fields** for sensitive data
- **Django File Upload Security** with validation

## 📧 Django Notification System

### Django Email Backend:
```python
# Email notifications using Django's email system
from django.core.mail import send_mail
from django.template.loader import render_to_string

# Email templates in templates/emails/
- welcome_user.html
- order_confirmation.html
- payment_receipt.html
- password_reset.html
- admin_alerts.html
```

### Django SMS Integration:
```python
# Using django-sms or Twilio integration
from twilio.rest import Client

# SMS notifications for:
- OTP verification
- Order delivery updates
- Payment confirmations
- Emergency alerts
```

### Django Channels for Real-time:
```python
# WebSocket support for real-time notifications
- Dashboard live updates
- Order status changes
- System alerts
- Chat support
```

## 🔄 Django Integration Packages

### Payment Gateway Integration:
```python
# Django packages for payments
- django-stripe (Stripe integration)
- django-paypal (PayPal integration)
- django-payments (Multiple gateways)
- django-merchant (Payment processing)
```

### File Storage Integration:
```python
# Django Storages for cloud storage
- django-storages[boto3] (AWS S3)
- django-storages[google] (Google Cloud)
- django-storages[azure] (Azure Storage)
```

### Third-Party Django Packages:
```python
# Essential Django packages
- celery (Background tasks)
- redis (Caching & message broker)
- django-cors-headers (CORS handling)
- django-filter (Advanced filtering)
- django-import-export (Data import/export)
- django-extensions (Development tools)
```

## 📈 Django Performance & Scalability

### Django Database Optimization:
```python
# Django ORM optimization
- select_related() for foreign keys
- prefetch_related() for many-to-many
- Database indexes in Meta class
- Django Debug Toolbar for query analysis
- django-silk for profiling
```

### Django Caching Strategy:
```python
# Django caching framework
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}

# Cache decorators and template tags
- @cache_page decorator
- {% cache %} template tag
- Low-level cache API
```

### Django Monitoring & Logging:
```python
# Django logging configuration
LOGGING = {
    'version': 1,
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
        },
    },
}

# Monitoring packages
- django-health-check
- sentry-sdk (Error tracking)
- django-prometheus (Metrics)
```

## 🚀 Django Deployment & DevOps

### Django Production Setup:
```python
# Production settings
DEBUG = False
ALLOWED_HOSTS = ['yourdomain.com']
STATIC_ROOT = '/var/www/static/'
MEDIA_ROOT = '/var/www/media/'

# WSGI server
- Gunicorn with Nginx
- uWSGI alternative
- Docker containerization
```

### Django Project Structure:
```
backend/
├── manage.py
├── requirements/
│   ├── base.txt
│   ├── development.txt
│   └── production.txt
├── config/
│   ├── settings/
│   │   ├── base.py
│   │   ├── development.py
│   │   └── production.py
│   ├── urls.py
│   └── wsgi.py
├── apps/
│   ├── accounts/
│   ├── resellers/
│   ├── users/
│   ├── orders/
│   ├── payments/
│   ├── support/
│   ├── core/
│   └── analytics/
├── static/
├── media/
├── templates/
├── locale/
└── tests/
```

### Django CI/CD Pipeline:
```yaml
# GitHub Actions / GitLab CI
- Django tests (pytest-django)
- Code quality (flake8, black)
- Security checks (bandit)
- Coverage reports
- Automated deployment
```

## 📋 Django Backend Development Checklist

### Setup & Configuration:
- [ ] Django project initialization
- [ ] Virtual environment setup
- [ ] Database configuration (PostgreSQL)
- [ ] Redis setup for caching
- [ ] Environment variables configuration

### Django Apps Development:
- [ ] accounts app (Admin authentication)
- [ ] resellers app (Reseller management)
- [ ] users app (Public users)
- [ ] orders app (SIM order management)
- [ ] payments app (Transaction handling)
- [ ] support app (Support tickets)
- [ ] core app (Settings & utilities)
- [ ] analytics app (Dashboard & reports)

### Django Models & Database:
- [ ] Model design and relationships
- [ ] Database migrations
- [ ] Model admin interfaces
- [ ] Data fixtures for testing

### Django REST Framework APIs:
- [ ] Serializers for all models
- [ ] ViewSets and API views
- [ ] URL routing configuration
- [ ] API permissions and authentication
- [ ] API documentation with DRF Spectacular

### Django Security & Authentication:
- [ ] JWT authentication setup
- [ ] Permission classes
- [ ] Rate limiting implementation
- [ ] Security middleware configuration

### Django Integrations:
- [ ] Payment gateway integration
- [ ] Email backend configuration
- [ ] SMS service integration
- [ ] File storage setup (AWS S3)
- [ ] Celery task queue setup

### Testing & Quality:
- [ ] Unit tests for models
- [ ] API endpoint tests
- [ ] Integration tests
- [ ] Code coverage setup
- [ ] Performance testing

### Deployment & Production:
- [ ] Production settings configuration
- [ ] Docker containerization
- [ ] Static files handling
- [ ] Database backup strategy
- [ ] Monitoring and logging setup
- [ ] CI/CD pipeline configuration
