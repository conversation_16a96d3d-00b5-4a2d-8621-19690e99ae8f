# Generated by Django 4.2.7 on 2025-08-04 09:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Reseller',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('max_clients', models.PositiveIntegerField(default=100)),
                ('max_sims', models.PositiveIntegerField(default=1000)),
                ('credit_limit', models.DecimalField(decimal_places=2, default=1000.0, max_digits=10)),
                ('current_credit', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('is_suspended', models.BooleanField(default=False)),
                ('commission_rate', models.DecimalField(decimal_places=2, default=10.0, max_digits=5)),
                ('markup_percentage', models.DecimalField(decimal_places=2, default=5.0, max_digits=5)),
                ('company_name', models.CharField(blank=True, max_length=200, null=True)),
                ('business_address', models.TextField(blank=True, null=True)),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='reseller_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Reseller',
                'verbose_name_plural': 'Resellers',
                'db_table': 'resellers',
            },
        ),
        migrations.CreateModel(
            name='ResellerPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(choices=[('bank_transfer', 'Bank Transfer'), ('cash', 'Cash'), ('credit_card', 'Credit Card'), ('digital_wallet', 'Digital Wallet')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('reference_number', models.CharField(max_length=100, unique=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_payments', to=settings.AUTH_USER_MODEL)),
                ('reseller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='resellers.reseller')),
            ],
            options={
                'db_table': 'reseller_payments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ResellerLimit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('limit_type', models.CharField(max_length=20)),
                ('old_value', models.DecimalField(decimal_places=2, max_digits=10)),
                ('new_value', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('reseller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='limit_changes', to='resellers.reseller')),
            ],
            options={
                'db_table': 'reseller_limits',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ResellerActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reseller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='resellers.reseller')),
            ],
            options={
                'db_table': 'reseller_activities',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalReseller',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('max_clients', models.PositiveIntegerField(default=100)),
                ('max_sims', models.PositiveIntegerField(default=1000)),
                ('credit_limit', models.DecimalField(decimal_places=2, default=1000.0, max_digits=10)),
                ('current_credit', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('is_suspended', models.BooleanField(default=False)),
                ('commission_rate', models.DecimalField(decimal_places=2, default=10.0, max_digits=5)),
                ('markup_percentage', models.DecimalField(decimal_places=2, default=5.0, max_digits=5)),
                ('company_name', models.CharField(blank=True, max_length=200, null=True)),
                ('business_address', models.TextField(blank=True, null=True)),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical Reseller',
                'verbose_name_plural': 'historical Resellers',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
