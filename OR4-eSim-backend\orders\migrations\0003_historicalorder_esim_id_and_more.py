# Generated by Django 4.2.7 on 2025-08-23 10:18

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0002_remove_historicalorder_public_user_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalorder",
            name="esim_id",
            field=models.PositiveIntegerField(
                blank=True, help_text="Link to eSIM record", null=True
            ),
        ),
        migrations.AddField(
            model_name="historicalorder",
            name="traveroam_order_reference",
            field=models.Char<PERSON>ield(
                blank=True,
                help_text="TraveRoam order reference",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="esim_id",
            field=models.PositiveIntegerField(
                blank=True, help_text="Link to eSIM record", null=True
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="traveroam_order_reference",
            field=models.Char<PERSON>ield(
                blank=True,
                help_text="TraveRoam order reference",
                max_length=100,
                null=True,
            ),
        ),
    ]
