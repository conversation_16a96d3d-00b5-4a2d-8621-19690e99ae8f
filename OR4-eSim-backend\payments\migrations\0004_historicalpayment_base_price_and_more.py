# Generated by Django 4.2.7 on 2025-08-23 10:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payments", "0003_enhance_payment_admin_functionality"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalpayment",
            name="base_price",
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="bundle_details",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="bundle_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="client_secret",
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="reseller_markup_amount",
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="reseller_markup_percent",
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="stripe_checkout_session_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="stripe_customer_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="historicalpayment",
            name="stripe_payment_intent_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="base_price",
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
        ),
        migrations.AddField(
            model_name="payment",
            name="bundle_details",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="bundle_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="client_secret",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="reseller_markup_amount",
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
        ),
        migrations.AddField(
            model_name="payment",
            name="reseller_markup_percent",
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
        ),
        migrations.AddField(
            model_name="payment",
            name="stripe_checkout_session_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="stripe_customer_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="payment",
            name="stripe_payment_intent_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.CreateModel(
            name="StripeWebhook",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("stripe_event_id", models.CharField(max_length=100, unique=True)),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("payment_intent.succeeded", "Payment Intent Succeeded"),
                            ("payment_intent.payment_failed", "Payment Intent Failed"),
                            (
                                "checkout.session.completed",
                                "Checkout Session Completed",
                            ),
                            ("checkout.session.expired", "Checkout Session Expired"),
                            ("invoice.payment_succeeded", "Invoice Payment Succeeded"),
                            ("invoice.payment_failed", "Invoice Payment Failed"),
                            ("customer.created", "Customer Created"),
                            ("charge.dispute.created", "Charge Dispute Created"),
                        ],
                        max_length=50,
                    ),
                ),
                ("event_data", models.JSONField()),
                ("processed", models.BooleanField(default=False)),
                ("processing_attempts", models.PositiveIntegerField(default=0)),
                ("last_processing_error", models.TextField(blank=True, null=True)),
                ("stripe_created_at", models.DateTimeField()),
                ("received_at", models.DateTimeField(auto_now_add=True)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "payment",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stripe_webhooks",
                        to="payments.payment",
                    ),
                ),
            ],
            options={
                "db_table": "stripe_webhooks",
                "ordering": ["-received_at"],
                "indexes": [
                    models.Index(
                        fields=["stripe_event_id"],
                        name="stripe_webh_stripe__1949b4_idx",
                    ),
                    models.Index(
                        fields=["event_type"], name="stripe_webh_event_t_9143c8_idx"
                    ),
                    models.Index(
                        fields=["processed"], name="stripe_webh_process_bb2506_idx"
                    ),
                ],
            },
        ),
    ]
