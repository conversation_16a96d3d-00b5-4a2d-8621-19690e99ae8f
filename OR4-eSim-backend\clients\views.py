from django.db.models import Count, Q, Sum
from django.utils import timezone
from django_filters import rest_framework as filters
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import Client, SupportTicket
from .serializers import (
    ClientActivitySerializer,
    ClientAdminControlSerializer,
    ClientCreateSerializer,
    ClientDetailSerializer,
    ClientSerializer,
    ClientStatisticsSerializer,
    ClientUpdateSerializer,
    ResellerClientListSerializer,
    SupportTicketCreateSerializer,
    SupportTicketSerializer,
)


class ClientPagination(PageNumberPagination):
    """Custom pagination for client lists"""

    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 100


class ClientFilter(filters.FilterSet):
    """Filter for client queries"""

    status = filters.ChoiceFilter(choices=Client.ClientStatus.choices)
    tier = filters.ChoiceFilter(choices=Client.ClientTier.choices)
    client_type = filters.ChoiceFilter(choices=Client.ClientType.choices)
    search = filters.CharFilter(method="search_filter")
    date_created = filters.DateFromToRangeFilter(field_name="created_at")
    last_activity = filters.DateFromToRangeFilter(field_name="last_activity")
    activity_status = filters.ChoiceFilter(
        choices=[
            ("never_active", "Never Active"),
            ("active_today", "Active Today"),
            ("active_this_week", "Active This Week"),
            ("active_this_month", "Active This Month"),
            ("inactive", "Inactive"),
        ],
        method="filter_activity_status",
    )

    class Meta:
        model = Client
        fields = ["status", "tier", "client_type", "country_of_travel", "country"]

    def search_filter(self, queryset, name, value):
        return queryset.filter(
            Q(full_name__icontains=value)
            | Q(email__icontains=value)
            | Q(phone_number__icontains=value)
        )

    def filter_activity_status(self, queryset, name, value):
        """Filter by activity status"""
        now = timezone.now().date()

        if value == "never_active":
            return queryset.filter(last_activity__isnull=True)
        elif value == "active_today":
            return queryset.filter(last_activity__date=now)
        elif value == "active_this_week":
            week_ago = now - timezone.timedelta(days=7)
            return queryset.filter(last_activity__date__gte=week_ago)
        elif value == "active_this_month":
            month_ago = now - timezone.timedelta(days=30)
            return queryset.filter(last_activity__date__gte=month_ago)
        elif value == "inactive":
            month_ago = now - timezone.timedelta(days=30)
            return queryset.filter(
                Q(last_activity__date__lt=month_ago) | Q(last_activity__isnull=True)
            )

        return queryset


class ClientViewSet(viewsets.ModelViewSet):
    """Enhanced client management API with reseller functionality"""

    queryset = Client.objects.select_related(
        "user", "reseller", "reseller__user", "admin_override_by"
    )
    serializer_class = ClientSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = ClientPagination
    filterset_class = ClientFilter

    def get_queryset(self):
        """Filter clients based on user role with optimization"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Client.objects.none()

        if not self.request.user.is_authenticated:
            return Client.objects.none()

        queryset = Client.objects.select_related(
            "user", "reseller", "reseller__user", "admin_override_by"
        )

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return queryset
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return queryset.filter(reseller__user=self.request.user)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return queryset.filter(user=self.request.user)
        return Client.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ClientCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return ClientUpdateSerializer
        elif self.action == "retrieve":
            return ClientDetailSerializer
        elif self.action in ["list", "my_clients"]:
            return ResellerClientListSerializer
        return ClientSerializer

    @action(detail=False, methods=["get"])
    def my_clients(self, request):
        """Get clients for current reseller with enhanced filtering and activity info"""
        queryset = self.get_queryset()

        # Apply filters
        queryset = self.filterset_class(request.GET, queryset=queryset).qs

        # Apply ordering
        ordering = request.GET.get("ordering", "-created_at")
        if ordering in [
            "full_name",
            "-full_name",
            "created_at",
            "-created_at",
            "last_activity",
            "-last_activity",
        ]:
            queryset = queryset.order_by(ordering)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return create_success_response(
            "Clients retrieved successfully", data=serializer.data
        )

    @action(detail=False, methods=["get"])
    def public_users(self, request):
        """Get public users (mobile app buyers) with enhanced filtering"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return create_error_response(
                "Access denied. Admin privileges required.",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Filter for direct users (public users)
        queryset = self.get_queryset().filter(client_type=Client.ClientType.DIRECT_USER)

        # Apply additional filters
        city = request.GET.get("city")
        if city:
            queryset = queryset.filter(city__icontains=city)

        status_filter = request.GET.get("status")
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        package = request.GET.get("package")
        if package:
            queryset = queryset.filter(preferred_package__icontains=package)

        # Filter by mobile app orders
        has_mobile_orders = request.GET.get("has_mobile_orders")
        if has_mobile_orders:
            from orders.models import Order

            if has_mobile_orders.lower() == "true":
                mobile_order_users = (
                    Order.objects.filter(order_source="app")
                    .values_list("client__user_id", flat=True)
                    .distinct()
                )
                queryset = queryset.filter(user_id__in=mobile_order_users)
            elif has_mobile_orders.lower() == "false":
                mobile_order_users = (
                    Order.objects.filter(order_source="app")
                    .values_list("client__user_id", flat=True)
                    .distinct()
                )
                queryset = queryset.exclude(user_id__in=mobile_order_users)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        # Check if no users found
        if not queryset.exists():
            return Response(
                {"success": True, "message": "No public users found", "data": []},
                status=status.HTTP_204_NO_CONTENT,
            )

        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {
                "success": True,
                "message": "Public users retrieved successfully",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def mobile_app_users(self, request):
        """Get users who have placed orders via mobile app"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        from orders.models import Order

        # Get users who have mobile app orders
        mobile_order_users = (
            Order.objects.filter(order_source="app")
            .values_list("client__user_id", flat=True)
            .distinct()
        )

        queryset = self.get_queryset().filter(
            user_id__in=mobile_order_users, client_type=Client.ClientType.DIRECT_USER
        )

        # Apply filters
        city = request.GET.get("city")
        if city:
            queryset = queryset.filter(city__icontains=city)

        status_filter = request.GET.get("status")
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        # Check if no users found
        if not queryset.exists():
            return Response(
                {"success": True, "message": "No mobile app users found", "data": []},
                status=status.HTTP_204_NO_CONTENT,
            )

        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {
                "success": True,
                "message": "Mobile app users retrieved successfully",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def block_user(self, request, pk=None):
        """Block a public user (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        client = self.get_object()
        reason = request.data.get("reason", "Blocked by administrator")

        try:
            client.block_client(reason, request.user)
            return Response(
                {
                    "success": True,
                    "message": "User blocked successfully",
                    "data": {
                        "client_id": client.id,
                        "status": client.status,
                        "block_reason": reason,
                        "blocked_by": request.user.email,
                        "blocked_at": (
                            client.admin_override_at.isoformat()
                            if client.admin_override_at
                            else None
                        ),
                    },
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to block user: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def unblock_user(self, request, pk=None):
        """Unblock a public user (admin only)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        client = self.get_object()
        reason = request.data.get("reason", "Unblocked by administrator")

        try:
            client.unblock_client(reason, request.user)
            return Response(
                {
                    "success": True,
                    "message": "User unblocked successfully",
                    "data": {
                        "client_id": client.id,
                        "status": client.status,
                        "unblock_reason": reason,
                        "unblocked_by": request.user.email,
                        "unblocked_at": (
                            client.admin_override_at.isoformat()
                            if client.admin_override_at
                            else None
                        ),
                    },
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"success": False, "error": f"Failed to unblock user: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["get"])
    def user_details(self, request, pk=None):
        """Get detailed user information including orders and payments"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            client = self.get_object()

            # Get user details
            user_data = self.get_serializer(client).data

            detailed_data = {
                "user_info": user_data,
                "client_id": client.id,
                "full_name": client.full_name,
                "email": client.email,
                "phone_number": client.phone_number,
                "status": client.status,
                "client_type": client.client_type,
                "created_at": (
                    client.created_at.isoformat() if client.created_at else None
                ),
            }

            return Response(
                {
                    "success": True,
                    "message": "User details retrieved successfully",
                    "data": detailed_data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"success": False, "error": f"Error retrieving user details: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def order_history(self, request, pk=None):
        """Get user's order history"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            client = self.get_object()

            from orders.serializers import OrderSerializer

            orders = client.orders.all().order_by("-created_at")

            # Check if no orders found
            if not orders.exists():
                return Response(
                    {
                        "success": True,
                        "message": "No orders found for this user",
                        "data": [],
                    },
                    status=status.HTTP_204_NO_CONTENT,
                )

            # Apply pagination
            page = self.paginate_queryset(orders)
            if page is not None:
                serializer = OrderSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = OrderSerializer(orders, many=True)
            return Response(
                {
                    "success": True,
                    "message": "Order history retrieved successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "success": False,
                    "error": f"Error retrieving order history: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def payment_history(self, request, pk=None):
        """Get user's payment history"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            client = self.get_object()

            from payments.serializers import PaymentSerializer

            payments = []
            for order in client.orders.all():
                payments.extend(order.payments.all())

            # Check if no payments found
            if not payments:
                return Response(
                    {
                        "success": True,
                        "message": "No payments found for this user",
                        "data": [],
                    },
                    status=status.HTTP_204_NO_CONTENT,
                )

            # Apply pagination
            page = self.paginate_queryset(payments)
            if page is not None:
                serializer = PaymentSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = PaymentSerializer(payments, many=True)
            return Response(
                {
                    "success": True,
                    "message": "Payment history retrieved successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "success": False,
                    "error": f"Error retrieving payment history: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def support_tickets(self, request, pk=None):
        """Get user's support tickets"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            client = self.get_object()
            tickets = client.support_tickets.all().order_by("-created_at")

            # Check if no tickets found
            if not tickets.exists():
                return Response(
                    {
                        "success": True,
                        "message": "No support tickets found for this user",
                        "data": [],
                    },
                    status=status.HTTP_204_NO_CONTENT,
                )

            # Apply pagination
            page = self.paginate_queryset(tickets)
            if page is not None:
                serializer = SupportTicketSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = SupportTicketSerializer(tickets, many=True)
            return Response(
                {
                    "success": True,
                    "message": "Support tickets retrieved successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "success": False,
                    "error": f"Error retrieving support tickets: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def public_user_statistics(self, request):
        """Get statistics for public users (mobile app buyers)"""
        if not (hasattr(request.user, "is_admin") and request.user.is_admin):
            return Response(
                {"error": "Access denied. Admin privileges required."},
                status=status.HTTP_403_FORBIDDEN,
            )

        from orders.models import Order
        from payments.models import Payment

        # Get public users
        public_users = self.get_queryset().filter(
            client_type=Client.ClientType.DIRECT_USER
        )

        # Get mobile app orders
        mobile_orders = Order.objects.filter(order_source="app")

        # Basic statistics
        total_public_users = public_users.count()
        active_public_users = public_users.filter(
            status=Client.ClientStatus.ACTIVE
        ).count()
        blocked_public_users = public_users.filter(
            status=Client.ClientStatus.BLOCKED
        ).count()
        suspended_public_users = public_users.filter(
            status=Client.ClientStatus.SUSPENDED
        ).count()

        # Mobile app statistics
        mobile_app_users = public_users.filter(
            user_id__in=mobile_orders.values_list(
                "client__user_id", flat=True
            ).distinct()
        ).count()

        total_mobile_orders = mobile_orders.count()
        total_mobile_revenue = (
            mobile_orders.filter(status__in=["completed", "delivered"]).aggregate(
                total=Sum("total_amount")
            )["total"]
            or 0
        )

        # City distribution
        city_distribution = dict(
            public_users.values("city")
            .exclude(city__isnull=True)
            .exclude(city="")
            .annotate(count=Count("id"))
            .values_list("city", "count")
        )

        # Package preferences
        package_distribution = dict(
            public_users.values("preferred_package")
            .exclude(preferred_package__isnull=True)
            .exclude(preferred_package="")
            .annotate(count=Count("id"))
            .values_list("preferred_package", "count")
        )

        # Recent activity
        recent_users = public_users.order_by("-created_at")[:10]
        recent_orders = mobile_orders.order_by("-created_at")[:10]

        statistics = {
            "user_statistics": {
                "total_public_users": total_public_users,
                "active_public_users": active_public_users,
                "blocked_public_users": blocked_public_users,
                "suspended_public_users": suspended_public_users,
                "mobile_app_users": mobile_app_users,
                "users_with_mobile_orders": mobile_app_users,
            },
            "order_statistics": {
                "total_mobile_orders": total_mobile_orders,
                "total_mobile_revenue": float(total_mobile_revenue),
                "average_order_value": (
                    float(total_mobile_revenue / total_mobile_orders)
                    if total_mobile_orders > 0
                    else 0
                ),
            },
            "geographic_distribution": {
                "city_distribution": city_distribution,
                "top_cities": sorted(
                    city_distribution.items(), key=lambda x: x[1], reverse=True
                )[:5],
            },
            "preferences": {
                "package_distribution": package_distribution,
                "top_packages": sorted(
                    package_distribution.items(), key=lambda x: x[1], reverse=True
                )[:5],
            },
            "recent_activity": {
                "recent_users": self.get_serializer(recent_users, many=True).data,
                "recent_orders": OrderSerializer(recent_orders, many=True).data,
            },
        }

        return create_success_response(
            "Public user statistics retrieved successfully", data=statistics
        )

    @action(detail=True, methods=["post"])
    def admin_control(self, request, pk=None):
        """Admin control actions on clients"""
        if not (request.user.is_admin or request.user.is_staff):
            return create_error_response(
                "Admin privileges required", status_code=status.HTTP_403_FORBIDDEN
            )

        client = self.get_object()
        serializer = ClientAdminControlSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data
        action = data["action"]

        try:
            if action == "block":
                client.block_client(data["reason"], request.user)
                message = "Client blocked successfully"

            elif action == "unblock":
                client.unblock_client(data["reason"], request.user)
                message = "Client unblocked successfully"

            elif action == "upgrade_tier":
                client.upgrade_tier(data["new_tier"], data["reason"], request.user)
                message = f"Client tier upgraded to {data['new_tier']}"

            elif action == "downgrade_tier":
                client.upgrade_tier(data["new_tier"], data["reason"], request.user)
                message = f"Client tier downgraded to {data['new_tier']}"

            elif action == "suspend":
                client.status = Client.ClientStatus.SUSPENDED
                client.admin_override_reason = data["reason"]
                client.admin_override_by = request.user
                client.admin_override_at = timezone.now()
                client.save()
                message = "Client suspended successfully"

            elif action == "activate":
                client.status = Client.ClientStatus.ACTIVE
                client.admin_override_reason = data["reason"]
                client.admin_override_by = request.user
                client.admin_override_at = timezone.now()
                client.save()
                message = "Client activated successfully"

            elif action == "add_notes":
                client.admin_notes = data.get("notes", "")
                client.save()
                message = "Admin notes updated successfully"

            return Response(
                create_success_response(
                    data={
                        "client_id": client.id,
                        "action": action,
                        "status": client.status,
                        "tier": client.tier,
                    },
                    message=message,
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(
                    message=f"Failed to perform action: {str(e)}", errors=str(e)
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get comprehensive client statistics"""
        queryset = self.get_queryset()

        # Basic counts
        total_clients = queryset.count()
        active_clients = queryset.filter(status=Client.ClientStatus.ACTIVE).count()
        blocked_clients = queryset.filter(status=Client.ClientStatus.BLOCKED).count()
        suspended_clients = queryset.filter(
            status=Client.ClientStatus.SUSPENDED
        ).count()

        # Monthly new clients
        this_month = timezone.now().replace(day=1)
        new_clients_this_month = queryset.filter(created_at__gte=this_month).count()

        # Clients by tier
        clients_by_tier = dict(
            queryset.values("tier")
            .annotate(count=Count("id"))
            .values_list("tier", "count")
        )

        # Clients by status
        clients_by_status = dict(
            queryset.values("status")
            .annotate(count=Count("id"))
            .values_list("status", "count")
        )

        # Clients by type
        clients_by_type = dict(
            queryset.values("client_type")
            .annotate(count=Count("id"))
            .values_list("client_type", "count")
        )

        # Revenue calculation
        total_revenue = (
            queryset.aggregate(total=Sum("orders__total_amount"))["total"] or 0
        )

        # Average orders per client
        total_orders = queryset.aggregate(total=Count("orders"))["total"] or 0
        average_orders_per_client = (
            total_orders / total_clients if total_clients > 0 else 0
        )

        # Top clients by revenue
        top_clients = (
            queryset.annotate(revenue=Sum("orders__total_amount"))
            .order_by("-revenue")[:5]
            .values("id", "full_name", "email", "revenue")
        )

        statistics = {
            "total_clients": total_clients,
            "active_clients": active_clients,
            "blocked_clients": blocked_clients,
            "suspended_clients": suspended_clients,
            "new_clients_this_month": new_clients_this_month,
            "clients_by_tier": clients_by_tier,
            "clients_by_status": clients_by_status,
            "clients_by_type": clients_by_type,
            "total_revenue": float(total_revenue),
            "average_orders_per_client": round(average_orders_per_client, 2),
            "top_clients": list(top_clients),
        }

        return Response(
            create_success_response(
                data=statistics, message="Client statistics retrieved"
            ),
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def update_activity(self, request, pk=None):
        """Update client activity (for login tracking)"""
        client = self.get_object()

        # Get IP address and user agent
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        client.update_activity(ip_address, user_agent)

        return create_success_response(
            "Client activity updated",
            data={
                "client_id": client.id,
                "last_activity": client.last_activity,
                "total_logins": client.total_logins,
            },
        )

    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class ResellerClientViewSet(viewsets.ReadOnlyModelViewSet):
    """Dedicated viewset for reseller client management - accessible by resellers and admins"""

    serializer_class = ResellerClientListSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = ClientPagination
    filterset_class = ClientFilter

    def get_queryset(self):
        """Get clients based on user role - resellers see their own, admins see all"""
        if not self.request.user.is_authenticated:
            return Client.objects.none()

        queryset = Client.objects.select_related("user", "reseller", "reseller__user")

        # Admin can see all clients
        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return queryset

        # Reseller can only see their own clients
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return queryset.filter(reseller__user=self.request.user)

        # Other users (clients, public users) cannot access this endpoint
        return Client.objects.none()

    def get_serializer_class(self):
        if self.action == "retrieve":
            return ClientDetailSerializer
        return ResellerClientListSerializer

    @action(detail=False, methods=["get"])
    def dashboard(self, request):
        """Get reseller client dashboard data with activity summary"""
        queryset = self.get_queryset()

        # Basic statistics
        total_clients = queryset.count()
        active_clients = queryset.filter(status=Client.ClientStatus.ACTIVE).count()
        blocked_clients = queryset.filter(status=Client.ClientStatus.BLOCKED).count()

        # Activity statistics
        now = timezone.now().date()
        active_today = queryset.filter(last_activity__date=now).count()
        active_this_week = queryset.filter(
            last_activity__date__gte=now - timezone.timedelta(days=7)
        ).count()
        active_this_month = queryset.filter(
            last_activity__date__gte=now - timezone.timedelta(days=30)
        ).count()
        inactive_clients = queryset.filter(
            Q(last_activity__date__lt=now - timezone.timedelta(days=30))
            | Q(last_activity__isnull=True)
        ).count()

        # Top performing clients
        top_clients = queryset.annotate(
            total_revenue=Sum("orders__total_amount"), total_orders=Count("orders")
        ).order_by("-total_revenue")[:5]

        # Clients by tier
        clients_by_tier = dict(
            queryset.values("tier")
            .annotate(count=Count("id"))
            .values_list("tier", "count")
        )

        # Recent new clients
        recent_clients = queryset.order_by("-created_at")[:5]

        # Most active clients
        most_active_clients = queryset.filter(last_activity__isnull=False).order_by(
            "-last_activity"
        )[:5]

        # Add reseller information for admin users
        reseller_info = None
        if hasattr(request.user, "is_admin") and request.user.is_admin:
            # For admin, show reseller distribution
            reseller_distribution = (
                queryset.values("reseller__user__email")
                .annotate(client_count=Count("id"))
                .order_by("-client_count")[:5]
            )
            reseller_info = {
                "reseller_distribution": list(reseller_distribution),
                "total_resellers": queryset.values("reseller").distinct().count(),
            }

        dashboard_data = {
            "statistics": {
                "total_clients": total_clients,
                "active_clients": active_clients,
                "blocked_clients": blocked_clients,
                "clients_by_tier": clients_by_tier,
            },
            "activity_summary": {
                "active_today": active_today,
                "active_this_week": active_this_week,
                "active_this_month": active_this_month,
                "inactive_clients": inactive_clients,
            },
            "top_clients": ResellerClientListSerializer(top_clients, many=True).data,
            "recent_clients": ResellerClientListSerializer(
                recent_clients, many=True
            ).data,
            "most_active_clients": ResellerClientListSerializer(
                most_active_clients, many=True
            ).data,
        }

        # Add reseller info for admin users
        if reseller_info:
            dashboard_data["reseller_info"] = reseller_info

        return create_success_response("Dashboard data retrieved", data=dashboard_data)

    @action(detail=True, methods=["get"])
    def client_details(self, request, pk=None):
        """Get detailed client information with activity"""
        client = self.get_object()
        serializer = ClientDetailSerializer(client)
        return create_success_response("Client details retrieved", data=serializer.data)

    @action(detail=True, methods=["get"])
    def client_orders(self, request, pk=None):
        """Get client orders"""
        client = self.get_object()

        from orders.serializers import OrderSerializer

        orders = client.orders.all().order_by("-created_at")

        # Apply pagination
        page = self.paginate_queryset(orders)
        if page is not None:
            serializer = OrderSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = OrderSerializer(orders, many=True)
        return create_success_response("Client orders retrieved", data=serializer.data)

    @action(detail=True, methods=["get"])
    def client_esims(self, request, pk=None):
        """Get client eSIMs"""
        client = self.get_object()

        from esim_management.serializers import ESIMSerializer

        esims = client.esims.all().order_by("-created_at")

        # Apply pagination
        page = self.paginate_queryset(esims)
        if page is not None:
            serializer = ESIMSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ESIMSerializer(esims, many=True)
        return create_success_response("Client eSIMs retrieved", data=serializer.data)

    @action(detail=False, methods=["get"])
    def export_clients(self, request):
        """Export clients data (CSV format)"""
        queryset = self.get_queryset()

        # Apply filters
        queryset = self.filterset_class(request.GET, queryset=queryset).qs

        import csv

        from django.http import HttpResponse

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = 'attachment; filename="clients_export.csv"'

        writer = csv.writer(response)

        # Add reseller column for admin users
        if hasattr(request.user, "is_admin") and request.user.is_admin:
            writer.writerow(
                [
                    "ID",
                    "Full Name",
                    "Email",
                    "Phone",
                    "Client Type",
                    "Status",
                    "Tier",
                    "Reseller",
                    "Country",
                    "Total Orders",
                    "Total Spent",
                    "Last Activity",
                    "Total Logins",
                    "Created Date",
                ]
            )

            for client in queryset:
                writer.writerow(
                    [
                        client.id,
                        client.full_name,
                        client.email,
                        client.phone_number,
                        client.get_client_type_display(),
                        client.get_status_display(),
                        client.get_tier_display(),
                        (
                            client.reseller.user.email
                            if client.reseller
                            else "Direct User"
                        ),
                        client.country or client.country_of_travel or "",
                        client.total_orders,
                        client.total_spent,
                        (
                            client.last_activity.strftime("%Y-%m-%d %H:%M")
                            if client.last_activity
                            else "Never"
                        ),
                        client.total_logins,
                        client.created_at.strftime("%Y-%m-%d"),
                    ]
                )
        else:
            # For resellers, don't include reseller column
            writer.writerow(
                [
                    "ID",
                    "Full Name",
                    "Email",
                    "Phone",
                    "Client Type",
                    "Status",
                    "Tier",
                    "Country",
                    "Total Orders",
                    "Total Spent",
                    "Last Activity",
                    "Total Logins",
                    "Created Date",
                ]
            )

            for client in queryset:
                writer.writerow(
                    [
                        client.id,
                        client.full_name,
                        client.email,
                        client.phone_number,
                        client.get_client_type_display(),
                        client.get_status_display(),
                        client.get_tier_display(),
                        client.country or client.country_of_travel or "",
                        client.total_orders,
                        client.total_spent,
                        (
                            client.last_activity.strftime("%Y-%m-%d %H:%M")
                            if client.last_activity
                            else "Never"
                        ),
                        client.total_logins,
                        client.created_at.strftime("%Y-%m-%d"),
                    ]
                )

        return response


class SupportTicketViewSet(viewsets.ModelViewSet):
    """Support ticket management API"""

    queryset = SupportTicket.objects.all()
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter tickets based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return SupportTicket.objects.none()

        if not self.request.user.is_authenticated:
            return SupportTicket.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return SupportTicket.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return SupportTicket.objects.filter(
                client__reseller__user=self.request.user
            )
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return SupportTicket.objects.filter(client__user=self.request.user)
        return SupportTicket.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return SupportTicketCreateSerializer
        return SupportTicketSerializer

    @action(detail=False, methods=["get"])
    def my_tickets(self, request):
        """Get tickets for current user"""
        tickets = self.get_queryset()
        serializer = self.get_serializer(tickets, many=True)
        return Response(
            create_success_response(data=serializer.data, message="Tickets retrieved"),
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"])
    def close_ticket(self, request, pk=None):
        """Close a support ticket"""
        try:
            ticket = self.get_object()
            ticket.status = "closed"
            ticket.resolved_at = timezone.now()
            ticket.save()
            return Response(
                create_success_response(
                    data={"status": "closed"}, message="Ticket closed successfully"
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(
                    message=f"Failed to close ticket: {str(e)}", errors=str(e)
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )
