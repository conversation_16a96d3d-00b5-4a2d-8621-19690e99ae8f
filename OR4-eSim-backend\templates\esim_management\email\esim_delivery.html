<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your eSIM is Ready!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .email-container {
            max-width: 650px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.15"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .company-logo {
            max-width: 180px;
            height: auto;
            margin-bottom: 20px;
            filter: brightness(0) invert(1);
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 18px;
            opacity: 0.95;
            font-weight: 300;
        }

        .content {
            padding: 40px 30px;
        }

        .section {
            margin-bottom: 35px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .details-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 15px;
            align-items: center;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .detail-value {
            color: #2c3e50;
            font-weight: 500;
            word-break: break-all;
        }

        .qr-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 20px;
            padding: 35px;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .qr-code-container {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin: 25px auto;
            max-width: 280px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 3px solid #f8f9fa;
        }

        .qr-code {
            width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .qr-instructions {
            color: #495057;
            font-size: 16px;
            margin-top: 15px;
            font-weight: 500;
        }

        .steps-container {
            background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 193, 7, 0.1);
        }

        .steps-list {
            counter-reset: step-counter;
            list-style: none;
        }

        .step-item {
            counter-increment: step-counter;
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        .step-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 16px;
            flex-shrink: 0;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .step-number::before {
            content: counter(step-counter);
        }

        .step-content {
            flex: 1;
            padding-top: 5px;
        }

        .step-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 16px;
        }

        .step-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-card {
            background: linear-gradient(135deg, #fff8e1 0%, #f3e5f5 100%);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #ffc107;
            margin-bottom: 25px;
        }

        .info-title {
            color: #e65100;
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .info-item {
            margin-bottom: 8px;
            color: #bf360c;
            font-size: 14px;
        }

        .info-label {
            font-weight: 600;
        }

        .contact-card {
            background: linear-gradient(135deg, #e8f5e8 0%, #f3e5f5 100%);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #28a745;
            text-align: center;
        }

        .contact-title {
            color: #155724;
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .contact-info {
            color: #155724;
            margin-bottom: 8px;
            font-size: 15px;
        }

        .contact-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .contact-link:hover {
            text-decoration: underline;
        }

        .footer {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .footer-text {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .footer-copyright {
            color: #adb5bd;
            font-size: 12px;
        }

        @media (max-width: 600px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 26px;
            }

            .content {
                padding: 30px 20px;
            }

            .details-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .qr-section, .steps-container, .info-card, .contact-card {
                padding: 20px;
            }

            .step-item {
                gap: 15px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header Section -->
        <div class="header">
            <div class="header-content">
                {% if company_logo_url %}
                <img src="{{ company_logo_url }}" alt="{{ company_name }}" class="company-logo">
                {% endif %}
                <h1>🎉 Your eSIM is Ready!</h1>
                <p>Hello {{ client_name }}, your eSIM has been successfully provisioned and is ready for activation.</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- eSIM Details Section -->
            <div class="section">
                <h2 class="section-title">
                    <span>📱</span>
                    eSIM Details
                </h2>
                <div class="details-card">
                    <div class="details-grid">
                        <div class="detail-label">📦 Plan:</div>
                        <div class="detail-value">{{ bundle_name }}</div>

                        <div class="detail-label">🆔 eSIM ID:</div>
                        <div class="detail-value">{{ esim_id }}</div>

                        <div class="detail-label">🔢 ICCID:</div>
                        <div class="detail-value">{{ iccid }}</div>

                        <div class="detail-label">📅 Assigned:</div>
                        <div class="detail-value">{{ assigned_at|date:"F j, Y, g:i a" }}</div>

                        <div class="detail-label">👤 Reseller:</div>
                        <div class="detail-value">{{ reseller_name }}</div>
                    </div>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="section">
                <h2 class="section-title">
                    <span>📲</span>
                    Scan QR Code to Install eSIM
                </h2>
                <div class="qr-section">
                    <p class="qr-instructions">Use your phone's camera or eSIM settings to scan this QR code:</p>
                    <div class="qr-code-container">
                        {% if qr_code %}
                            {% if qr_code|slice:":11" == "data:image/" %}
                                <img src="{{ qr_code }}" alt="eSIM QR Code" class="qr-code">
                            {% else %}
                                <img src="data:image/png;base64,{{ qr_code }}" alt="eSIM QR Code" class="qr-code">
                            {% endif %}
                        {% else %}
                            <p style="color: #666; font-style: italic; padding: 40px;">QR Code will be available after activation</p>
                        {% endif %}
                    </div>
                    <p class="qr-instructions">📱 Point your camera at the QR code above to get started</p>
                </div>
            </div>

            <!-- Installation Steps -->
            <div class="section">
                <h2 class="section-title">
                    <span>🔧</span>
                    Installation Steps
                </h2>
                <div class="steps-container">
                    <ul class="steps-list">
                        <li class="step-item">
                            <div class="step-number"></div>
                            <div class="step-content">
                                <div class="step-title">Open Settings</div>
                                <div class="step-description">Go to your phone's Settings app and look for cellular or mobile data options</div>
                            </div>
                        </li>
                        <li class="step-item">
                            <div class="step-number"></div>
                            <div class="step-content">
                                <div class="step-title">Find eSIM Settings</div>
                                <div class="step-description">Navigate to "Cellular" or "Mobile Data" then tap "Add eSIM" or "Add Cellular Plan"</div>
                            </div>
                        </li>
                        <li class="step-item">
                            <div class="step-number"></div>
                            <div class="step-content">
                                <div class="step-title">Scan QR Code</div>
                                <div class="step-description">Point your camera at the QR code above or manually enter the activation details</div>
                            </div>
                        </li>
                        <li class="step-item">
                            <div class="step-number"></div>
                            <div class="step-content">
                                <div class="step-title">Complete Activation</div>
                                <div class="step-description">Follow the on-screen instructions to finish setting up your eSIM</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Important Information -->
            <div class="info-card">
                <div class="info-title">⚠️ Important Technical Details</div>
                <div class="info-item">
                    <span class="info-label">SMDP Address:</span> {{ smdp_address|default:"Will be provided during activation" }}
                </div>
                <div class="info-item">
                    <span class="info-label">Matching ID:</span> {{ matching_id|default:"Generated automatically" }}
                </div>
                <div class="info-item">
                    <span class="info-label">Activation:</span> Your eSIM will activate automatically when you arrive at your destination
                </div>
            </div>

            <!-- Contact Support -->
            <div class="contact-card">
                <div class="contact-title">🆘 Need Help?</div>
                <p style="margin-bottom: 15px; color: #155724;">Our support team is here to assist you with any questions or issues:</p>
                <div class="contact-info">
                    <strong>📧 Email:</strong> <a href="mailto:{{ support_email }}" class="contact-link">{{ support_email }}</a>
                </div>
                <div class="contact-info">
                    <strong>📞 Phone:</strong> {{ support_phone }}
                </div>
                <div class="contact-info">
                    <strong>🌐 Website:</strong> <a href="{{ company_website }}" class="contact-link">{{ company_website }}</a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">Thank you for choosing {{ company_name }}!</div>
            <div class="footer-text">This email was sent on {{ delivery_date|date:"F j, Y, g:i a" }} to {{ client_email }}</div>
            <div class="footer-copyright">© 2024 {{ company_name }}. All rights reserved.</div>
        </div>
    </div>
</body>
</html>
