import random
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.utils import timezone

from accounts.models import User
from clients.models import Client
from esim_management.models import ESIM
from orders.models import Order
from payments.models import Payment
from resellers.models import Reseller


class Command(BaseCommand):
    help = "Create sample data for testing the dashboard"

    def add_arguments(self, parser):
        parser.add_argument(
            "--users", type=int, default=50, help="Number of users to create"
        )
        parser.add_argument(
            "--orders", type=int, default=200, help="Number of orders to create"
        )

    def handle(self, *args, **options):
        self.stdout.write("Creating sample data...")

        # Create sample users
        self.create_sample_users(options["users"])

        # Create sample orders and payments
        self.create_sample_orders(options["orders"])

        self.stdout.write(self.style.SUCCESS("Successfully created sample data!"))

    def create_sample_users(self, num_users):
        """Create sample users with different roles"""
        self.stdout.write(f"Creating {num_users} sample users...")

        # Create admin users
        admin_users = [
            ("<EMAIL>", "<PERSON>zan", "<PERSON>", "+1", "5551234567"),
            ("<EMAIL>", "Super", "User", "+1", "5551234568"),
            ("<EMAIL>", "API", "User", "+1", "5551234569"),
            ("<EMAIL>", "Test", "Admin", "+1", "5551234570"),
        ]

        for email, first_name, last_name, country_code, phone_number in admin_users:
            if not User.objects.filter(email=email).exists():
                User.objects.create_superuser(
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    country_code=country_code,
                    phone_number=phone_number,
                    password="admin123",
                )

        # Create reseller users
        reseller_data = [
            ("John", "Smith", "<EMAIL>", "+1", "5551234571"),
            ("Sarah", "Johnson", "<EMAIL>", "+1", "5551234572"),
            ("Mike", "Davis", "<EMAIL>", "+1", "5551234573"),
            ("Lisa", "Wilson", "<EMAIL>", "+1", "5551234574"),
            ("David", "Brown", "<EMAIL>", "+1", "5551234575"),
        ]

        for first_name, last_name, email, country_code, phone_number in reseller_data:
            if not User.objects.filter(email=email).exists():
                user = User.objects.create_user(
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    country_code=country_code,
                    phone_number=phone_number,
                    password="reseller123",
                    role="reseller",
                )

                # Create reseller profile
                Reseller.objects.create(
                    user=user,
                    max_clients=random.randint(50, 200),
                    max_sims=random.randint(500, 2000),
                    credit_limit=Decimal(random.uniform(1000.0, 5000.0)).quantize(
                        Decimal("0.01")
                    ),
                    current_credit=Decimal(random.uniform(0.0, 1000.0)).quantize(
                        Decimal("0.01")
                    ),
                    is_suspended=False,
                )

        # Create client users
        client_data = [
            ("Alice", "Cooper", "<EMAIL>", "+1", "5551234576"),
            ("Bob", "Miller", "<EMAIL>", "+1", "5551234577"),
            ("Carol", "Taylor", "<EMAIL>", "+1", "5551234578"),
            ("Dan", "Anderson", "<EMAIL>", "+1", "5551234579"),
            ("Eva", "Thomas", "<EMAIL>", "+1", "5551234580"),
        ]

        # Get a reseller to assign clients to
        resellers_list = list(Reseller.objects.all())
        if not resellers_list:
            self.stdout.write("No resellers found. Please create resellers first.")
            return

        for first_name, last_name, email, country_code, phone_number in client_data:
            if not User.objects.filter(email=email).exists():
                user = User.objects.create_user(
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    country_code=country_code,
                    phone_number=phone_number,
                    password="client123",
                    role="client",
                )

                # Create client profile
                now = timezone.now()
                Client.objects.create(
                    user=user,
                    reseller=random.choice(resellers_list),
                    full_name=f"{first_name} {last_name}",
                    phone_number=f"+1{random.randint(1000000000, 9999999999)}",
                    email=email,
                    country_of_travel=random.choice(
                        ["USA", "UK", "Germany", "France", "Japan", "Australia"]
                    ),
                    date_of_travel=now.date() + timedelta(days=random.randint(30, 180)),
                    is_active=True,
                    is_blocked=False,
                )

        # Create public users
        for i in range(
            num_users - len(reseller_data) - len(client_data) - len(admin_users)
        ):
            first_name = random.choice(
                [
                    "Alex",
                    "Jordan",
                    "Casey",
                    "Morgan",
                    "Riley",
                    "Quinn",
                    "Avery",
                    "Blake",
                ]
            )
            last_name = random.choice(
                [
                    "Smith",
                    "Johnson",
                    "Williams",
                    "Brown",
                    "Jones",
                    "Garcia",
                    "Miller",
                    "Davis",
                ]
            )
            email = f"{first_name.lower()}.{last_name.lower()}{i}@example.com"

            # Generate random country code and phone number
            country_codes = ["+1", "+44", "+91", "+86", "+81", "+49", "+33", "+61"]
            country_code = random.choice(country_codes)
            phone_number = str(random.randint(100000000, 999999999))

            if not User.objects.filter(email=email).exists():
                user = User.objects.create_user(
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    country_code=country_code,
                    phone_number=phone_number,
                    password="user123",
                    role="public_user",
                )

                # Create client profile (direct user)
                Client.objects.create(
                    user=user,
                    client_type="direct_user",
                    full_name=f"{first_name} {last_name}",
                    phone_number=f"+1{random.randint(1000000000, 9999999999)}",
                    email=email,
                    address=f"{random.randint(100, 9999)} {first_name} Road",
                    city=random.choice(
                        ["Portland", "Austin", "Nashville", "Orlando", "San Diego"]
                    ),
                    country="USA",
                    status="active",
                    tier="basic",
                )

    def create_sample_orders(self, num_orders):
        """Create sample orders and payments"""
        self.stdout.write(f"Creating {num_orders} sample orders...")

        # Get existing users
        resellers = list(Reseller.objects.filter(is_suspended=False))
        clients = list(Client.objects.all())

        if not resellers or not clients:
            self.stdout.write("No users found. Please create users first.")
            return

        # Create orders over the last 6 months
        now = timezone.now()
        order_statuses = [
            "pending",
            "confirmed",
            "processing",
            "dispatched",
            "delivered",
            "activated",
            "completed",
            "cancelled",
            "refunded",
        ]
        payment_statuses = ["pending", "completed", "failed", "refunded"]

        for i in range(num_orders):
            # Random date within last 6 months
            days_ago = random.randint(0, 180)
            order_date = timezone.now() - timedelta(days=days_ago)

            # Random order details
            order_status = random.choice(order_statuses)
            unit_price = Decimal(random.uniform(20.0, 100.0)).quantize(Decimal("0.01"))
            quantity = random.randint(1, 3)
            subtotal = unit_price * quantity
            tax_amount = subtotal * Decimal("0.08")  # 8% tax
            delivery_fee = Decimal(random.uniform(5.0, 15.0)).quantize(Decimal("0.01"))
            total_amount = subtotal + tax_amount + delivery_fee

            # Random customer (reseller or client)
            customer_type = random.choice(["reseller", "client"])

            if customer_type == "reseller":
                reseller = random.choice(resellers)
                client = None
                order_source = "reseller"
            else:
                reseller = None
                client = random.choice(clients)
                order_source = "admin"

            # Create order
            order = Order.objects.create(
                order_number=f"ORD-{order_date.strftime('%Y%m%d')}-{i:04d}",
                order_type=random.choice(["sim", "esim"]),
                order_source=order_source,
                status=order_status,
                reseller=reseller,
                client=client,
                product_name=f"Global eSIM Plan {random.choice(['Basic', 'Premium', 'Business'])}",
                product_description=f"International roaming eSIM with {random.randint(1, 30)} days validity",
                quantity=quantity,
                unit_price=unit_price,
                subtotal=subtotal,
                tax_amount=tax_amount,
                delivery_fee=delivery_fee,
                total_amount=total_amount,
                delivery_address=f"{random.randint(100, 9999)} Sample Street",
                delivery_city=random.choice(
                    ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix"]
                ),
                delivery_country="USA",
                delivery_phone=f"+1{random.randint(1000000000, 9999999999)}",
                created_at=order_date,
                updated_at=order_date,
            )

            # Create payment for most orders (80% success rate)
            if random.random() < 0.8:
                payment_status = random.choice(payment_statuses)
                payment_date = order_date + timedelta(hours=random.randint(1, 24))

                Payment.objects.create(
                    order=order,
                    amount=total_amount,
                    currency="USD",
                    payment_method=random.choice(
                        ["credit_card", "paypal", "bank_transfer"]
                    ),
                    status=payment_status,
                    transaction_id=f"TXN-{payment_date.strftime('%Y%m%d%H%M%S')}-{i:04d}",
                    created_at=payment_date,
                    updated_at=payment_date,
                )

            # Create ESIM for delivered orders
            if order_status == "delivered":
                # Create a simple ESIM plan first
                from esim_management.models import ESIMPlan

                plan, created = ESIMPlan.objects.get_or_create(
                    name="Global eSIM Basic",
                    defaults={
                        "description": "International roaming eSIM with 30 days validity",
                        "country": "Global",
                        "region": "Worldwide",
                        "data_volume": "1GB",
                        "validity_days": 30,
                        "plan_type": "data_only",
                        "base_price": unit_price,
                        "reseller_price": unit_price * Decimal("0.8"),
                        "public_price": unit_price,
                        "traveroam_plan_id": f"plan_{i:04d}",
                        "is_active": True,
                    },
                )

                ESIM.objects.create(
                    plan=plan,
                    client=client,
                    public_user=public_user,
                    reseller=reseller,
                    status="activated",
                    qr_code=f"data:image/png;base64,QR_CODE_DATA_{i:04d}",
                    activation_code=f"ACTIVATION_{i:04d}",
                    traveroam_esim_id=f"esim_{i:04d}",
                    assigned_at=order_date,
                    activated_at=order_date + timedelta(days=random.randint(1, 3)),
                    expires_at=order_date + timedelta(days=30),
                    created_at=order_date,
                    updated_at=order_date,
                )

        self.stdout.write(f"Created {num_orders} orders with payments and ESIMs")
