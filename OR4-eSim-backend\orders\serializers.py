from rest_framework import serializers

from .models import (
    DeliveryTracking,
    Order,
    OrderItem,
    OrderNotification,
    OrderStatusHistory,
)


class OrderItemSerializer(serializers.ModelSerializer):
    """Order item serializer"""

    class Meta:
        model = OrderItem
        fields = [
            "id",
            "order",
            "esim",
            "product_name",
            "product_description",
            "quantity",
            "unit_price",
            "total_price",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class OrderItemCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating order items"""

    class Meta:
        model = OrderItem
        fields = [
            "esim",
            "product_name",
            "product_description",
            "quantity",
            "unit_price",
        ]


class OrderStatusHistorySerializer(serializers.ModelSerializer):
    """Order status history serializer"""

    class Meta:
        model = OrderStatusHistory
        fields = [
            "id",
            "order",
            "old_status",
            "new_status",
            "changed_by",
            "notes",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class OrderSerializer(serializers.ModelSerializer):
    """Order serializer"""

    reseller = serializers.SerializerMethodField()
    client = serializers.SerializerMethodField()
    items = serializers.SerializerMethodField()
    status_history = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            "id",
            "reseller",
            "client",
            "order_number",
            "order_type",
            "order_source",
            "status",
            "product_name",
            "product_description",
            "quantity",
            "unit_price",
            "subtotal",
            "tax_amount",
            "delivery_fee",
            "total_amount",
            "delivery_address",
            "delivery_city",
            "delivery_country",
            "delivery_phone",
            "created_at",
            "updated_at",
            "confirmed_at",
            "dispatched_at",
            "delivered_at",
            "completed_at",
            "cancelled_at",
            "items",
            "status_history",
        ]
        read_only_fields = ["id", "order_number", "created_at", "updated_at"]

    def get_reseller(self, obj):
        if obj.reseller:
            from resellers.serializers import ResellerSerializer

            return ResellerSerializer(obj.reseller).data
        return None

    def get_client(self, obj):
        if obj.client:
            from clients.serializers import ClientSerializer

            return ClientSerializer(obj.client).data
        return None

    def get_items(self, obj):
        items = obj.items.all().order_by("created_at")
        return OrderItemSerializer(items, many=True).data

    def get_status_history(self, obj):
        history = obj.status_history.all().order_by("-created_at")
        return OrderStatusHistorySerializer(history, many=True).data


class OrderCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating orders"""

    items = OrderItemCreateSerializer(many=True)

    class Meta:
        model = Order
        fields = [
            "reseller",
            "client",
            "order_type",
            "order_source",
            "product_name",
            "product_description",
            "quantity",
            "unit_price",
            "subtotal",
            "tax_amount",
            "delivery_fee",
            "total_amount",
            "delivery_address",
            "delivery_city",
            "delivery_country",
            "delivery_phone",
            "items",
        ]

    def create(self, validated_data):
        items_data = validated_data.pop("items")
        order = Order.objects.create(**validated_data)

        for item_data in items_data:
            OrderItem.objects.create(order=order, **item_data)

        return order


class OrderUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating orders"""

    class Meta:
        model = Order
        fields = [
            "status",
            "product_name",
            "product_description",
            "delivery_address",
            "delivery_city",
            "delivery_country",
            "delivery_phone",
        ]


class DeliveryTrackingSerializer(serializers.ModelSerializer):
    """Delivery tracking serializer"""

    order = serializers.SerializerMethodField()

    class Meta:
        model = DeliveryTracking
        fields = [
            "id",
            "order",
            "tracking_number",
            "courier_name",
            "status",
            "current_location",
            "estimated_delivery",
            "actual_delivery",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_order(self, obj):
        return OrderSerializer(obj.order).data


class DeliveryTrackingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating delivery tracking"""

    class Meta:
        model = DeliveryTracking
        fields = [
            "order",
            "tracking_number",
            "courier_name",
            "status",
            "current_location",
            "estimated_delivery",
        ]

    def validate_order(self, value):
        """Validate that the order exists and user has access"""
        request = self.context.get("request")
        if request and request.user:
            if request.user.is_admin:
                return value
            elif (
                request.user.is_reseller
                and value.reseller
                and value.reseller.user == request.user
            ):
                return value
            elif (
                request.user.is_client
                and value.client
                and value.client.user == request.user
            ):
                return value
            elif (
                request.user.is_public_user
                and value.client
                and value.client.user == request.user
            ):
                return value
            else:
                raise serializers.ValidationError(
                    "You don't have permission to access this order"
                )
        return value


class OrderNotificationSerializer(serializers.ModelSerializer):
    """Order notification serializer"""

    class Meta:
        model = OrderNotification
        fields = [
            "id",
            "order",
            "notification_type",
            "notification_method",
            "recipient",
            "message",
            "sent_at",
            "delivered",
            "delivered_at",
        ]
        read_only_fields = ["id", "sent_at", "delivered_at"]
