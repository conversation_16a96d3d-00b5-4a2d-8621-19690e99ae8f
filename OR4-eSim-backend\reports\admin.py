from django.contrib import admin

from .models import AnalyticsEvent, PerformanceMetric, Report, ReportSchedule


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "report_type",
        "format",
        "is_generated",
        "requested_by",
        "created_at",
    )
    list_filter = ("report_type", "format", "is_generated", "created_at")
    search_fields = ("name", "requested_by__email")
    readonly_fields = ("created_at", "updated_at", "generated_at")
    raw_id_fields = ("requested_by",)

    fieldsets = (
        ("Report Information", {"fields": ("name", "report_type", "format")}),
        ("Parameters", {"fields": ("date_from", "date_to", "parameters")}),
        (
            "Generated Report",
            {
                "fields": (
                    "file_path",
                    "file_size",
                    "generated_at",
                    "is_generated",
                    "is_downloaded",
                )
            },
        ),
        ("User", {"fields": ("requested_by",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(AnalyticsEvent)
class AnalyticsEventAdmin(admin.ModelAdmin):
    list_display = ("event_type", "user", "ip_address", "created_at")
    list_filter = ("event_type", "created_at")
    search_fields = ("event_type", "user__email", "ip_address")
    readonly_fields = ("created_at",)
    raw_id_fields = ("user",)
    ordering = ("-created_at",)


@admin.register(PerformanceMetric)
class PerformanceMetricAdmin(admin.ModelAdmin):
    list_display = ("name", "category", "value", "unit", "recorded_at")
    list_filter = ("category", "recorded_at")
    search_fields = ("name", "category")
    readonly_fields = ("recorded_at",)
    ordering = ("-recorded_at",)


@admin.register(ReportSchedule)
class ReportScheduleAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "report_type",
        "frequency",
        "is_active",
        "last_run",
        "next_run",
    )
    list_filter = ("report_type", "frequency", "is_active", "created_at")
    search_fields = ("name", "created_by__email")
    readonly_fields = ("created_at", "updated_at", "last_run", "next_run")
    raw_id_fields = ("created_by",)

    fieldsets = (
        ("Schedule Information", {"fields": ("name", "report_type", "frequency")}),
        ("Schedule Settings", {"fields": ("is_active", "last_run", "next_run")}),
        ("Recipients & Format", {"fields": ("recipients", "format")}),
        ("Parameters", {"fields": ("parameters",)}),
        ("Created By", {"fields": ("created_by",)}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )
