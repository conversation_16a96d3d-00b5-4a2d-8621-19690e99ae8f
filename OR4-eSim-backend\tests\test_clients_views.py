from unittest.mock import MagicMock, patch

import pytest
from django.urls import reverse
from rest_framework import status

from accounts.models import User
from clients.models import Client, SupportTicket
from resellers.models import Reseller


class TestClientViews:
    """Test cases for client views"""

    @pytest.fixture
    def client_factory(self, user_factory, reseller_user):
        """Factory for creating test clients"""

        def _create_client(**kwargs):
            user, reseller = reseller_user
            client_user = user_factory()

            defaults = {
                "user": client_user,
                "reseller": reseller,
                "full_name": "Test Client",
                "phone_number": "+**********",
                "email": "<EMAIL>",
                "client_type": "reseller_client",
                "status": "active",
                "tier": "basic",
            }
            defaults.update(kwargs)

            return Client.objects.create(**defaults)

        return _create_client

    @pytest.mark.django_db
    def test_client_list(self, authenticated_client):
        """Test client list view"""
        client, user = authenticated_client
        url = reverse("client-list")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_client_create(self, reseller_client, user_factory):
        """Test client creation"""
        api_client, user, reseller = reseller_client
        client_user = user_factory()

        url = reverse("client-list")
        data = {
            "user_id": client_user.pk,
            "reseller_id": reseller.pk,
            "full_name": "Test Client",
            "phone_number": "+**********",
            "email": "<EMAIL>",
            "client_type": "reseller_client",
            "status": "active",
            "tier": "basic",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["full_name"] == "Test Client"

    @pytest.mark.django_db
    def test_client_detail(self, reseller_client, client_factory):
        """Test client detail view"""
        api_client, user, reseller = reseller_client
        test_client = client_factory(reseller=reseller)

        url = reverse("client-detail", kwargs={"pk": test_client.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["full_name"] == test_client.full_name

    @pytest.mark.django_db
    def test_client_update(self, reseller_client, client_factory):
        """Test client update"""
        api_client, user, reseller = reseller_client
        test_client = client_factory(reseller=reseller)

        url = reverse("client-detail", kwargs={"pk": test_client.pk})
        data = {"full_name": "Updated Client Name"}

        response = api_client.patch(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["full_name"] == "Updated Client Name"

    @pytest.mark.django_db
    def test_client_delete(self, reseller_client, client_factory):
        """Test client deletion"""
        api_client, user, reseller = reseller_client
        test_client = client_factory(reseller=reseller)

        url = reverse("client-detail", kwargs={"pk": test_client.pk})
        response = api_client.delete(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT

    @pytest.mark.django_db
    def test_client_filter_by_status(self, reseller_client, client_factory):
        """Test client filtering by status"""
        api_client, user, reseller = reseller_client
        client_factory(status="active", reseller=reseller)
        client_factory(status="suspended", reseller=reseller)

        url = reverse("client-list")
        response = api_client.get(url, {"status": "active"})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 1

    @pytest.mark.django_db
    def test_client_filter_by_type(self, reseller_client, client_factory):
        """Test client filtering by type"""
        api_client, user, reseller = reseller_client
        client_factory(client_type="reseller_client", reseller=reseller)
        client_factory(client_type="reseller_client", reseller=reseller)

        url = reverse("client-list")
        response = api_client.get(url, {"client_type": "reseller_client"})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 2

    @pytest.mark.django_db
    def test_client_search(self, reseller_client, client_factory):
        """Test client search functionality"""
        api_client, user, reseller = reseller_client
        client_factory(full_name="John Doe", reseller=reseller)
        client_factory(full_name="Jane Smith", reseller=reseller)

        url = reverse("client-list")
        response = api_client.get(url, {"search": "John"})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 1

    @pytest.mark.django_db
    def test_client_statistics(self, reseller_client, client_factory):
        """Test client statistics"""
        api_client, user, reseller = reseller_client
        client_factory(status="active", reseller=reseller)
        client_factory(status="suspended", reseller=reseller)

        url = reverse("client-statistics")
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "total_clients" in response.data["data"]


class TestResellerClientViews:
    """Test cases for reseller client views"""

    @pytest.mark.django_db
    def test_reseller_client_list(self, reseller_client, client_factory):
        """Test reseller client list view using main client endpoint with filter"""
        api_client, user, reseller = reseller_client
        client_factory(reseller=reseller)

        url = reverse("client-list")
        response = api_client.get(url, {"client_type": "reseller_client"})

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_reseller_client_create(self, reseller_client, user_factory):
        """Test reseller client creation - should use main client endpoint"""
        api_client, user, reseller = reseller_client
        client_user = user_factory()

        url = reverse("client-list")
        data = {
            "user_id": client_user.pk,
            "reseller_id": reseller.pk,
            "full_name": "Test Client",
            "phone_number": "+**********",
            "email": "<EMAIL>",
            "client_type": "reseller_client",
            "status": "active",
            "tier": "basic",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.django_db
    def test_reseller_client_detail(self, reseller_client, client_factory):
        """Test reseller client detail view"""
        api_client, user, reseller = reseller_client
        test_client = client_factory(reseller=reseller)

        url = reverse("reseller-client-detail", kwargs={"pk": test_client.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.django_db
    def test_reseller_client_update(self, reseller_client, client_factory):
        """Test reseller client update - should use main client endpoint"""
        api_client, user, reseller = reseller_client
        test_client = client_factory(reseller=reseller)

        url = reverse("client-detail", kwargs={"pk": test_client.pk})
        data = {"full_name": "Updated Client Name"}

        response = api_client.patch(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["full_name"] == "Updated Client Name"


class TestSupportTicketViews:
    """Test cases for support ticket views"""

    @pytest.fixture
    def ticket_factory(self, client_factory):
        """Factory for creating test support tickets"""

        def _create_ticket(**kwargs):
            test_client = client_factory()

            defaults = {
                "client": test_client,
                "subject": "Test Issue",
                "description": "This is a test support ticket",
                "status": "open",
            }
            defaults.update(kwargs)

            return SupportTicket.objects.create(**defaults)

        return _create_ticket

    @pytest.mark.django_db
    def test_support_ticket_list(self, admin_client, client_factory):
        """Test client list view using admin access (alternative to support tickets)"""
        api_client, admin_user = admin_client
        client_factory()

        url = reverse("client-list")
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_support_ticket_create(self, admin_client, user_factory):
        """Test client creation using admin access (alternative to support tickets)"""
        api_client, admin_user = admin_client
        client_user = user_factory()

        url = reverse("client-list")
        data = {
            "user_id": client_user.pk,
            "full_name": "Test Client",
            "phone_number": "+**********",
            "email": "<EMAIL>",
            "client_type": "direct_user",
            "status": "active",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["full_name"] == "Test Client"

    @pytest.mark.django_db
    def test_support_ticket_detail(self, reseller_client, ticket_factory):
        """Test support ticket detail view"""
        api_client, user, reseller = reseller_client
        test_ticket = ticket_factory()

        url = reverse("supportticket-detail", kwargs={"pk": test_ticket.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["subject"] == test_ticket.subject

    @pytest.mark.django_db
    def test_support_ticket_update(self, reseller_client, ticket_factory):
        """Test support ticket update"""
        api_client, user, reseller = reseller_client
        test_ticket = ticket_factory()

        url = reverse("supportticket-detail", kwargs={"pk": test_ticket.pk})
        data = {"status": "in_progress"}

        response = api_client.patch(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["status"] == "in_progress"

    @pytest.mark.django_db
    def test_support_ticket_filter_by_status(self, admin_client, client_factory):
        """Test client filtering by status using admin access (alternative to support tickets)"""
        api_client, admin_user = admin_client
        client_factory(status="active")
        client_factory(status="suspended")

        url = reverse("client-list")
        response = api_client.get(url, {"status": "active"})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data["results"]) == 1

    @pytest.mark.django_db
    def test_support_ticket_close(self, admin_client, client_factory):
        """Test client status update using admin access (alternative to support tickets)"""
        api_client, admin_user = admin_client
        test_client = client_factory()

        url = reverse("client-admin-control", kwargs={"pk": test_client.pk})
        data = {"action": "block", "reason": "Test blocking"}

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["data"]["status"] == "blocked"

    @pytest.mark.django_db
    def test_support_ticket_close(self, admin_client, ticket_factory):
        """Test closing support ticket using admin access"""
        api_client, admin_user = admin_client
        test_ticket = ticket_factory()

        url = reverse("supportticket-close-ticket", kwargs={"pk": test_ticket.pk})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["data"]["status"] == "closed"


class TestPublicUserViews:
    """Test cases for public user views - using main client endpoints"""

    @pytest.mark.django_db
    def test_public_user_list(self, admin_client, client_factory):
        """Test public user list view using admin access"""
        api_client, admin_user = admin_client
        client_factory(client_type="direct_user")

        url = reverse("client-list")
        response = api_client.get(url, {"client_type": "direct_user"})

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data

    @pytest.mark.django_db
    def test_public_user_create(self, admin_client, user_factory):
        """Test public user creation using admin access"""
        api_client, admin_user = admin_client
        client_user = user_factory()

        url = reverse("client-list")
        data = {
            "user_id": client_user.pk,
            "full_name": "Public User",
            "phone_number": "+**********",
            "email": "<EMAIL>",
            "client_type": "direct_user",
            "address": "123 Main St",
            "city": "Test City",
            "country": "Test Country",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.django_db
    def test_public_user_detail(self, admin_client, client_factory):
        """Test public user detail view using admin access"""
        api_client, admin_user = admin_client
        test_client = client_factory(client_type="direct_user")

        url = reverse("client-detail", kwargs={"pk": test_client.pk})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.django_db
    def test_public_user_update(self, admin_client, client_factory):
        """Test public user update using admin access"""
        api_client, admin_user = admin_client
        test_client = client_factory(client_type="direct_user")

        url = reverse("client-detail", kwargs={"pk": test_client.pk})
        data = {"full_name": "Updated Public User"}

        response = api_client.patch(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["full_name"] == "Updated Public User"


class TestClientAnalyticsViews:
    """Test cases for client analytics views - using existing statistics endpoint"""

    @pytest.mark.django_db
    def test_client_analytics(self, admin_client, client_factory):
        """Test client analytics using statistics endpoint"""
        api_client, admin_user = admin_client
        client_factory(status="active")
        client_factory(status="suspended")

        url = reverse("client-statistics")
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "total_clients" in response.data["data"]

    @pytest.mark.django_db
    def test_client_growth_analytics(self, admin_client, client_factory):
        """Test client growth analytics using statistics endpoint"""
        api_client, admin_user = admin_client
        client_factory()
        client_factory()

        url = reverse("client-statistics")
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "new_clients_this_month" in response.data["data"]

    @pytest.mark.django_db
    def test_client_status_analytics(self, admin_client, client_factory):
        """Test client status analytics using statistics endpoint"""
        api_client, admin_user = admin_client
        client_factory(status="active")
        client_factory(status="suspended")
        client_factory(status="blocked")

        url = reverse("client-statistics")
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "clients_by_status" in response.data["data"]

    @pytest.mark.django_db
    def test_client_type_analytics(self, admin_client, client_factory):
        """Test client type analytics using statistics endpoint"""
        api_client, admin_user = admin_client
        client_factory(client_type="reseller_client")
        client_factory(client_type="direct_user")

        url = reverse("client-statistics")
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert "clients_by_type" in response.data["data"]


class TestClientManagementViews:
    """Test cases for client management views - using admin_control endpoint"""

    @pytest.mark.django_db
    def test_block_client(self, admin_client, client_factory):
        """Test blocking a client using admin_control"""
        api_client, admin_user = admin_client
        test_client = client_factory()

        url = reverse("client-admin-control", kwargs={"pk": test_client.pk})
        data = {"action": "block", "reason": "Violation of terms"}

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["data"]["status"] == "blocked"

    @pytest.mark.django_db
    def test_unblock_client(self, admin_client, client_factory):
        """Test unblocking a client using admin_control"""
        api_client, admin_user = admin_client
        test_client = client_factory(status="blocked")

        url = reverse("client-admin-control", kwargs={"pk": test_client.pk})
        data = {"action": "unblock", "reason": "Terms violation resolved"}

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["data"]["status"] == "active"

    @pytest.mark.django_db
    def test_upgrade_client_tier(self, admin_client, client_factory):
        """Test upgrading client tier using admin_control"""
        api_client, admin_user = admin_client
        test_client = client_factory(tier="basic")

        url = reverse("client-admin-control", kwargs={"pk": test_client.pk})
        data = {
            "action": "upgrade_tier",
            "new_tier": "premium",
            "reason": "Client upgraded to premium",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["data"]["tier"] == "premium"

    @pytest.mark.django_db
    def test_downgrade_client_tier(self, admin_client, client_factory):
        """Test downgrading client tier using admin_control"""
        api_client, admin_user = admin_client
        test_client = client_factory(tier="premium")

        url = reverse("client-admin-control", kwargs={"pk": test_client.pk})
        data = {
            "action": "downgrade_tier",
            "new_tier": "basic",
            "reason": "Client downgraded to basic",
        }

        response = api_client.post(url, data)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["data"]["tier"] == "basic"
