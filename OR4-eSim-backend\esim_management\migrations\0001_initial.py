# Generated by Django 4.2.7 on 2025-08-04 09:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('resellers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('clients', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ESIM',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('provisioned', 'Provisioned'), ('assigned', 'Assigned'), ('activated', 'Activated'), ('expired', 'Expired'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('qr_code', models.TextField(blank=True, null=True)),
                ('activation_code', models.CharField(blank=True, max_length=200, null=True)),
                ('traveroam_esim_id', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('traveroam_order_id', models.CharField(blank=True, max_length=100, null=True)),
                ('data_used', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('data_remaining', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('assigned_at', models.DateTimeField(blank=True, null=True)),
                ('activated_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='esims', to='clients.client')),
            ],
            options={
                'verbose_name': 'eSIM',
                'verbose_name_plural': 'eSIMs',
                'db_table': 'esims',
            },
        ),
        migrations.CreateModel(
            name='ESIMPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('country', models.CharField(max_length=100)),
                ('region', models.CharField(blank=True, max_length=100, null=True)),
                ('data_volume', models.CharField(max_length=50)),
                ('validity_days', models.PositiveIntegerField()),
                ('plan_type', models.CharField(choices=[('data_only', 'Data Only'), ('voice_data', 'Voice & Data'), ('unlimited', 'Unlimited')], default='data_only', max_length=20)),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reseller_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('public_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('traveroam_plan_id', models.CharField(max_length=100, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'eSIM Plan',
                'verbose_name_plural': 'eSIM Plans',
                'db_table': 'esim_plans',
            },
        ),
        migrations.CreateModel(
            name='TraveRoamWebhook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('webhook_type', models.CharField(choices=[('activation', 'Activation'), ('usage', 'Usage'), ('expiry', 'Expiry'), ('error', 'Error')], max_length=20)),
                ('payload', models.JSONField()),
                ('processed', models.BooleanField(default=False)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('esim', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='webhooks', to='esim_management.esim')),
            ],
            options={
                'db_table': 'traveroam_webhooks',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalESIMPlan',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('country', models.CharField(max_length=100)),
                ('region', models.CharField(blank=True, max_length=100, null=True)),
                ('data_volume', models.CharField(max_length=50)),
                ('validity_days', models.PositiveIntegerField()),
                ('plan_type', models.CharField(choices=[('data_only', 'Data Only'), ('voice_data', 'Voice & Data'), ('unlimited', 'Unlimited')], default='data_only', max_length=20)),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reseller_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('public_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('traveroam_plan_id', models.CharField(db_index=True, max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical eSIM Plan',
                'verbose_name_plural': 'historical eSIM Plans',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalESIM',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('provisioned', 'Provisioned'), ('assigned', 'Assigned'), ('activated', 'Activated'), ('expired', 'Expired'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('qr_code', models.TextField(blank=True, null=True)),
                ('activation_code', models.CharField(blank=True, max_length=200, null=True)),
                ('traveroam_esim_id', models.CharField(blank=True, db_index=True, max_length=100, null=True)),
                ('traveroam_order_id', models.CharField(blank=True, max_length=100, null=True)),
                ('data_used', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('data_remaining', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('assigned_at', models.DateTimeField(blank=True, null=True)),
                ('activated_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('client', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='clients.client')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('plan', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='esim_management.esimplan')),
                ('public_user', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='clients.publicuser')),
                ('reseller', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='resellers.reseller')),
            ],
            options={
                'verbose_name': 'historical eSIM',
                'verbose_name_plural': 'historical eSIMs',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='ESIMUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_used', models.DecimalField(decimal_places=2, max_digits=10)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('location', models.CharField(blank=True, max_length=100, null=True)),
                ('webhook_data', models.JSONField(blank=True, null=True)),
                ('esim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_logs', to='esim_management.esim')),
            ],
            options={
                'db_table': 'esim_usage',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ESIMDelivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delivery_method', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('in_app', 'In App'), ('pdf', 'PDF Download')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('recipient_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('recipient_phone', models.CharField(blank=True, max_length=15, null=True)),
                ('delivery_message', models.TextField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('esim', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery', to='esim_management.esim')),
            ],
            options={
                'verbose_name': 'eSIM Delivery',
                'verbose_name_plural': 'eSIM Deliveries',
                'db_table': 'esim_deliveries',
            },
        ),
        migrations.AddField(
            model_name='esim',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='esims', to='esim_management.esimplan'),
        ),
        migrations.AddField(
            model_name='esim',
            name='public_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='esims', to='clients.publicuser'),
        ),
        migrations.AddField(
            model_name='esim',
            name='reseller',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='esims', to='resellers.reseller'),
        ),
    ]
