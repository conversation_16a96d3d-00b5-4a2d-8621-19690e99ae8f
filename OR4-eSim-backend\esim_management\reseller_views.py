# -*- coding: utf-8 -*-
"""
Professional Reseller Views for eSIM Management with TraveRoam Integration
Complete API endpoints for reseller workflow management
"""

import logging

from django.core.cache import cache
from django.db import models
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from clients.models import Client
from orders.models import Order, OrderItem
from payments.models import Payment
from resellers.models import Reseller

from .models import ESIM, ESIMPlan
from .serializers import (
    ClientCreateSerializer,
    ClientSerializer,
    ESIMAssignSerializer,
    ESIMPlanSerializer,
    ESIMSerializer,
)
from .services import ESIMWorkflowService

logger = logging.getLogger(__name__)


class StandardPagination(PageNumberPagination):
    """Standard pagination for API responses"""

    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 100


class ResellerClientViewSet(viewsets.ModelViewSet):
    """Professional ViewSet for managing reseller clients - Step 1 of workflow"""

    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    serializer_class = ClientSerializer

    def get_queryset(self):
        """Get clients for the authenticated reseller"""
        return Client.objects.filter(reseller__user=self.request.user).order_by(
            "-created_at"
        )

    def get_serializer_class(self):
        """Use different serializers for different actions"""
        if self.action == "create":
            return ClientCreateSerializer
        return ClientSerializer

    def perform_create(self, serializer):
        """Create client for the authenticated reseller"""
        reseller = Reseller.objects.get(user=self.request.user)
        user_data = serializer.validated_data

        # Add country detection if not provided
        if not user_data.get("country_of_travel"):
            # Simple country detection from phone number
            phone = user_data.get("phone_number", "")
            if phone.startswith("+91"):
                user_data["country_of_travel"] = {
                    "name": "India",
                    "code": "IN",
                    "region": "Asia",
                }
            elif phone.startswith("+92"):
                user_data["country_of_travel"] = {
                    "name": "Pakistan",
                    "code": "PK",
                    "region": "Asia",
                }
            # Add more country mappings as needed

        # Use service to create client
        result = ESIMWorkflowService.create_client(user_data, reseller)

        if result["success"]:
            serializer.instance = Client.objects.get(id=result["client_id"])
        else:
            raise ValueError(result["error"])

    @action(detail=True, methods=["post"])
    def assign_esim(self, request, pk=None):
        """Assign eSIM to client - Step 2 of workflow"""
        try:
            client = self.get_object()
            reseller = Reseller.objects.get(user=request.user)

            # Validate request data
            serializer = ESIMAssignSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            bundle_name = serializer.validated_data["bundle_name"]

            # Validate bundle name
            if not ESIMWorkflowService.validate_bundle_name(bundle_name):
                return Response(
                    {"error": "Invalid bundle name format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Assign eSIM using service
            result = ESIMWorkflowService.assign_esim_to_client(
                client, bundle_name, reseller
            )

            if result["success"]:
                # Deliver eSIM to client
                esim = ESIM.objects.get(id=result["esim_id"])
                delivery_success = ESIMWorkflowService.deliver_esim_to_client(esim)

                return Response(
                    {
                        "success": True,
                        "esim_id": result["esim_id"],
                        "order_reference": result["order_reference"],
                        "iccid": result["iccid"],
                        "bundle_name": result["bundle_name"],
                        "bundle_details": result["bundle_details"],
                        "delivery_sent": delivery_success,
                        "qr_code_available": bool(result.get("qr_code")),
                        "message": "eSIM assigned and delivered successfully",
                    }
                )
            else:
                return Response(
                    {"error": result["error"]},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error assigning eSIM: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["get"])
    def esim_history(self, request, pk=None):
        """Get eSIM history for client"""
        try:
            client = self.get_object()
            history = ESIMWorkflowService.get_client_esim_history(client)

            return Response(
                {
                    "success": True,
                    "client_id": client.id,
                    "client_name": client.full_name,
                    "esim_history": history,
                    "total_esims": len(history),
                }
            )

        except Exception as e:
            logger.error(f"Error getting eSIM history: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def available_plans(self, request):
        """Get available eSIM plans for assignment"""
        try:
            country = request.query_params.get("country")
            region = request.query_params.get("region")

            plans = ESIMWorkflowService.get_available_plans(
                country=country, region=region
            )

            return Response(
                {
                    "success": True,
                    "plans": plans,
                    "count": len(plans),
                    "filters": {"country": country, "region": region},
                }
            )

        except Exception as e:
            logger.error(f"Error getting available plans: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def networks(self, request):
        """Get available networks"""
        try:
            country = request.query_params.get("country")
            networks = ESIMWorkflowService.get_networks(countries=country)

            return Response(
                {"success": True, "networks": networks, "count": len(networks)}
            )

        except Exception as e:
            logger.error(f"Error getting networks: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ResellerESIMViewSet(viewsets.ModelViewSet):
    """Professional ViewSet for managing eSIMs assigned by reseller"""

    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    serializer_class = ESIMSerializer

    def get_queryset(self):
        """Get eSIMs assigned by the authenticated reseller"""
        return ESIM.objects.filter(reseller__user=self.request.user).order_by(
            "-assigned_at"
        )

    @action(detail=True, methods=["get"])
    def status(self, request, pk=None):
        """Get eSIM status from TraveRoam"""
        try:
            esim = self.get_object()
            status_data = ESIMWorkflowService.get_esim_status(esim)

            return Response(
                {"success": True, "esim_id": esim.id, "status_data": status_data}
            )

        except Exception as e:
            logger.error(f"Error getting eSIM status: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["get"])
    def usage(self, request, pk=None):
        """Get eSIM usage data from TraveRoam"""
        try:
            esim = self.get_object()
            usage_data = ESIMWorkflowService.get_esim_usage(esim)

            return Response(
                {"success": True, "esim_id": esim.id, "usage_data": usage_data}
            )

        except Exception as e:
            logger.error(f"Error getting eSIM usage: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def cancel(self, request, pk=None):
        """Cancel eSIM"""
        try:
            esim = self.get_object()
            reason = request.data.get("reason", "Cancelled by reseller")

            success = ESIMWorkflowService.cancel_esim(esim, reason)

            if success:
                return Response(
                    {"success": True, "message": "eSIM cancelled successfully"}
                )
            else:
                return Response(
                    {"error": "Failed to cancel eSIM"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error cancelling eSIM: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def resend_delivery(self, request, pk=None):
        """Resend eSIM delivery"""
        try:
            esim = self.get_object()
            success = ESIMWorkflowService.deliver_esim_to_client(esim)

            if success:
                return Response(
                    {"success": True, "message": "eSIM delivery resent successfully"}
                )
            else:
                return Response(
                    {"error": "Failed to resend delivery"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error resending delivery: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["get"])
    def qr_code(self, request, pk=None):
        """Get eSIM QR code"""
        try:
            esim = self.get_object()

            if not esim.qr_code:
                return Response(
                    {"error": "QR code not available"}, status=status.HTTP_404_NOT_FOUND
                )

            return Response(
                {
                    "success": True,
                    "esim_id": esim.id,
                    "qr_code": esim.qr_code,
                    "activation_code": esim.activation_code,
                    "iccid": esim.iccid,
                }
            )

        except Exception as e:
            logger.error(f"Error getting QR code: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get comprehensive eSIM statistics for reseller with TraveRoam integration"""
        try:
            reseller = Reseller.objects.get(user=request.user)

            # Get date filters
            date_from = request.query_params.get("date_from")
            date_to = request.query_params.get("date_to")

            # Get comprehensive analytics from service
            analytics = ESIMWorkflowService.get_comprehensive_reseller_analytics(
                reseller=reseller, date_from=date_from, date_to=date_to
            )

            if not analytics.get("success"):
                return Response(
                    {"error": analytics.get("error", "Failed to get analytics")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            return Response(
                {"success": True, "analytics": analytics.get("analytics", {})}
            )

        except Exception as e:
            logger.error(f"Error getting statistics: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def packages_used(self, request):
        """Get packages/bundles used by reseller with TraveRoam data"""
        try:
            reseller = Reseller.objects.get(user=request.user)

            # Get all eSIMs for this reseller
            esims = ESIM.objects.filter(reseller=reseller)

            # Group by bundle name and get statistics
            bundle_stats = {}
            for esim in esims:
                bundle_name = esim.bundle_name or "Unknown"
                if bundle_name not in bundle_stats:
                    bundle_stats[bundle_name] = {
                        "bundle_name": bundle_name,
                        "total_assigned": 0,
                        "active": 0,
                        "expired": 0,
                        "cancelled": 0,
                        "total_revenue": 0,
                        "bundle_details": esim.bundle_details or {},
                    }

                bundle_stats[bundle_name]["total_assigned"] += 1

                if esim.status in ["provisioned", "assigned", "activated"]:
                    bundle_stats[bundle_name]["active"] += 1
                elif esim.status == "expired":
                    bundle_stats[bundle_name]["expired"] += 1
                elif esim.status in ["cancelled", "revoked"]:
                    bundle_stats[bundle_name]["cancelled"] += 1

                # Get revenue from related orders
                try:
                    order = Order.objects.get(esim_id=esim.id)
                    bundle_stats[bundle_name]["total_revenue"] += float(
                        order.total_amount or 0
                    )
                except Order.DoesNotExist:
                    pass

            # Convert to list and sort by total assigned
            packages_list = list(bundle_stats.values())
            packages_list.sort(key=lambda x: x["total_assigned"], reverse=True)

            return Response(
                {
                    "success": True,
                    "packages": packages_list,
                    "total_packages": len(packages_list),
                    "total_assigned": sum(p["total_assigned"] for p in packages_list),
                }
            )

        except Exception as e:
            logger.error(f"Error getting packages used: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def client_esim_history(self, request):
        """Get eSIM history for all clients of reseller"""
        try:
            reseller = Reseller.objects.get(user=request.user)
            client_id = request.query_params.get("client_id")

            # Get clients for this reseller
            clients = Client.objects.filter(reseller=reseller)
            if client_id:
                clients = clients.filter(id=client_id)

            client_history = []
            for client in clients:
                # Get all eSIMs for this client
                esims = ESIM.objects.filter(client=client).order_by("-assigned_at")

                client_data = {
                    "client_id": client.id,
                    "client_name": client.full_name,
                    "client_email": client.email,
                    "client_phone": client.phone_number,
                    "total_esims": esims.count(),
                    "active_esims": esims.filter(
                        status__in=["provisioned", "assigned", "activated"]
                    ).count(),
                    "esim_history": [],
                }

                for esim in esims:
                    esim_data = {
                        "esim_id": esim.id,
                        "bundle_name": esim.bundle_name,
                        "status": esim.status,
                        "assigned_at": (
                            esim.assigned_at.isoformat() if esim.assigned_at else None
                        ),
                        "iccid": esim.traveroam_esim_id,
                        "traveroam_order_ref": esim.traveroam_order_reference,
                        "bundle_details": esim.bundle_details or {},
                    }

                    # Get real-time status from TraveRoam for active eSIMs
                    if (
                        esim.status in ["provisioned", "assigned", "activated"]
                        and esim.traveroam_esim_id
                    ):
                        try:
                            status_result = (
                                ESIMWorkflowService.get_esim_status_from_traveroam(esim)
                            )
                            if status_result.get("success"):
                                esim_data["traveroam_status"] = status_result.get(
                                    "status_data", {}
                                )
                        except Exception as e:
                            logger.warning(
                                f"Failed to get TraveRoam status for eSIM {esim.id}: {str(e)}"
                            )

                    client_data["esim_history"].append(esim_data)

                client_history.append(client_data)

            return Response(
                {
                    "success": True,
                    "clients": client_history,
                    "total_clients": len(client_history),
                }
            )

        except Exception as e:
            logger.error(f"Error getting client eSIM history: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def refresh_traveroam_status(self, request, pk=None):
        """Refresh eSIM status from TraveRoam API"""
        try:
            esim = self.get_object()

            if not esim.traveroam_esim_id:
                return Response(
                    {"error": "No TraveRoam eSIM ID available"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            refresh_result = ESIMWorkflowService.refresh_esim_data_from_traveroam(esim)

            if refresh_result.get("success"):
                return Response(
                    {
                        "success": True,
                        "message": "eSIM status refreshed successfully",
                        "data": refresh_result.get("data", {}),
                    }
                )
            else:
                return Response(
                    {"error": refresh_result.get("error", "Failed to refresh status")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error refreshing eSIM status: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def revoke_bundle(self, request, pk=None):
        """Revoke eSIM bundle via TraveRoam API"""
        try:
            esim = self.get_object()
            reason = request.data.get("reason", "Revoked by reseller")

            revoke_result = ESIMWorkflowService.revoke_esim_bundle_via_traveroam(
                esim, reason
            )

            if revoke_result.get("success"):
                return Response(
                    {
                        "success": True,
                        "message": "eSIM bundle revoked successfully",
                        "data": revoke_result.get("data", {}),
                    }
                )
            else:
                return Response(
                    {"error": revoke_result.get("error", "Failed to revoke bundle")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error revoking eSIM bundle: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def send_sms_notification(self, request, pk=None):
        """Send SMS notification via TraveRoam API"""
        try:
            esim = self.get_object()
            message = request.data.get("message", "")

            if not message:
                return Response(
                    {"error": "Message is required"}, status=status.HTTP_400_BAD_REQUEST
                )

            sms_result = ESIMWorkflowService.send_sms_via_traveroam(esim, message)

            if sms_result.get("success"):
                return Response(
                    {
                        "success": True,
                        "message": "SMS notification sent successfully",
                        "data": sms_result.get("data", {}),
                    }
                )
            else:
                return Response(
                    {"error": sms_result.get("error", "Failed to send SMS")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logger.error(f"Error sending SMS notification: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ResellerPlanViewSet(viewsets.ReadOnlyModelViewSet):
    """Professional ViewSet for viewing available eSIM plans"""

    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    serializer_class = ESIMPlanSerializer

    def get_queryset(self):
        """Get available plans from TraveRoam"""
        return ESIMPlan.objects.filter(is_active=True)

    def list(self, request, *args, **kwargs):
        """Get available plans from TraveRoam API"""
        try:
            # Get filters from query parameters
            country = request.query_params.get("country")
            region = request.query_params.get("region")

            # Get plans from TraveRoam API
            plans = ESIMWorkflowService.get_available_plans(
                country=country, region=region
            )

            return Response(
                {
                    "success": True,
                    "plans": plans,
                    "count": len(plans),
                    "filters": {"country": country, "region": region},
                }
            )

        except Exception as e:
            logger.error(f"Error getting plans: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def networks(self, request):
        """Get available networks from TraveRoam"""
        try:
            country = request.query_params.get("country")
            networks = ESIMWorkflowService.get_networks(countries=country)

            return Response(
                {"success": True, "networks": networks, "count": len(networks)}
            )

        except Exception as e:
            logger.error(f"Error getting networks: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def countries(self, request):
        """Get available countries for plans"""
        try:
            # Get all plans to extract unique countries
            plans = ESIMWorkflowService.get_available_plans()

            countries = {}
            for plan in plans:
                country_code = plan.get("country_code")
                country_name = plan.get("country")
                if country_code and country_name:
                    countries[country_code] = country_name

            return Response(
                {"success": True, "countries": countries, "count": len(countries)}
            )

        except Exception as e:
            logger.error(f"Error getting countries: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ResellerDashboardViewSet(viewsets.ViewSet):
    """Professional ViewSet for reseller dashboard data with comprehensive TraveRoam integration"""

    permission_classes = [IsAuthenticated]

    def list(self, request):
        """Get comprehensive dashboard summary with TraveRoam integration"""
        try:
            reseller = Reseller.objects.get(user=request.user)

            # Get date filters
            date_from = request.query_params.get("date_from")
            date_to = request.query_params.get("date_to")

            # Get comprehensive analytics from service
            analytics = ESIMWorkflowService.get_comprehensive_reseller_analytics(
                reseller=reseller, date_from=date_from, date_to=date_to
            )

            if not analytics.get("success"):
                return Response(
                    {"error": analytics.get("error", "Failed to get analytics")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            # Get recent orders
            recent_orders = Order.objects.filter(
                reseller=reseller, order_type="esim"
            ).order_by("-created_at")[:10]

            # Get recent eSIMs
            recent_esims = ESIM.objects.filter(reseller=reseller).order_by(
                "-assigned_at"
            )[:10]

            # Get recent clients
            recent_clients = Client.objects.filter(reseller=reseller).order_by(
                "-created_at"
            )[:10]

            dashboard_data = {
                "analytics": analytics.get("analytics", {}),
                "recent_orders": [
                    {
                        "id": order.id,
                        "order_number": order.order_number,
                        "status": order.status,
                        "total_amount": float(order.total_amount),
                        "created_at": order.created_at.isoformat(),
                        "client_name": (
                            order.client.full_name if order.client else "N/A"
                        ),
                        "esim_id": order.esim_id,
                        "traveroam_order_ref": order.traveroam_order_reference,
                    }
                    for order in recent_orders
                ],
                "recent_esims": [
                    {
                        "id": esim.id,
                        "bundle_name": esim.bundle_name,
                        "status": esim.status,
                        "assigned_at": (
                            esim.assigned_at.isoformat() if esim.assigned_at else None
                        ),
                        "client_name": esim.client.full_name if esim.client else "N/A",
                        "iccid": esim.traveroam_esim_id,
                        "traveroam_order_ref": esim.traveroam_order_reference,
                    }
                    for esim in recent_esims
                ],
                "recent_clients": [
                    {
                        "id": client.id,
                        "full_name": client.full_name,
                        "email": client.email,
                        "phone_number": client.phone_number,
                        "created_at": client.created_at.isoformat(),
                        "total_esims": client.esims.count(),
                    }
                    for client in recent_clients
                ],
                "reseller_info": {
                    "company_name": reseller.user.get_full_name(),
                    "current_credit": float(reseller.current_credit),
                    "credit_limit": float(reseller.credit_limit),
                    "credit_utilization": (
                        (
                            float(reseller.current_credit)
                            / float(reseller.credit_limit)
                            * 100
                        )
                        if reseller.credit_limit > 0
                        else 0
                    ),
                    "max_clients": reseller.max_clients,
                    "max_sims": reseller.max_sims,
                    "status": "active" if not reseller.is_suspended else "suspended",
                },
            }

            return Response({"success": True, "dashboard": dashboard_data})

        except Exception as e:
            logger.error(f"Error getting dashboard data: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def orders(self, request):
        """Get all orders for reseller with TraveRoam integration"""
        try:
            reseller = Reseller.objects.get(user=request.user)

            # Get orders from database (linked to TraveRoam)
            orders = (
                Order.objects.filter(reseller=reseller, order_type="esim")
                .select_related("client")
                .order_by("-created_at")
            )

            # Apply filters
            status_filter = request.query_params.get("status")
            if status_filter:
                orders = orders.filter(status=status_filter)

            date_from = request.query_params.get("date_from")
            if date_from:
                orders = orders.filter(created_at__gte=date_from)

            date_to = request.query_params.get("date_to")
            if date_to:
                orders = orders.filter(created_at__lte=date_to)

            # Paginate results
            paginator = StandardPagination()
            page = paginator.paginate_queryset(orders, request)

            order_data = []
            for order in page:
                order_info = {
                    "id": order.id,
                    "order_number": order.order_number,
                    "status": order.status,
                    "total_amount": float(order.total_amount),
                    "created_at": order.created_at.isoformat(),
                    "confirmed_at": (
                        order.confirmed_at.isoformat() if order.confirmed_at else None
                    ),
                    "delivered_at": (
                        order.delivered_at.isoformat() if order.delivered_at else None
                    ),
                    "client_name": order.client.full_name if order.client else "N/A",
                    "client_email": order.client.email if order.client else "N/A",
                    "esim_id": order.esim_id,
                    "traveroam_order_ref": order.traveroam_order_reference,
                    "product_name": order.product_name,
                    "product_description": order.product_description,
                }

                # Get related payment info
                try:
                    payment = Payment.objects.get(order=order)
                    order_info["payment_status"] = payment.status
                    order_info["payment_method"] = payment.payment_method
                    order_info["payment_amount"] = float(payment.amount)
                except Payment.DoesNotExist:
                    order_info["payment_status"] = "pending"
                    order_info["payment_method"] = "N/A"
                    order_info["payment_amount"] = 0.0

                order_data.append(order_info)

            return Response(
                {
                    "success": True,
                    "orders": order_data,
                    "count": orders.count(),
                    "pagination": {
                        "next": paginator.get_next_link(),
                        "previous": paginator.get_previous_link(),
                        "count": orders.count(),
                    },
                }
            )

        except Exception as e:
            logger.error(f"Error getting orders: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def esims(self, request):
        """Get all eSIMs for reseller with TraveRoam integration"""
        try:
            reseller = Reseller.objects.get(user=request.user)

            # Get eSIMs from database (linked to TraveRoam)
            esims = (
                ESIM.objects.filter(reseller=reseller)
                .select_related("client")
                .order_by("-assigned_at")
            )

            # Apply filters
            status_filter = request.query_params.get("status")
            if status_filter:
                esims = esims.filter(status=status_filter)

            bundle_filter = request.query_params.get("bundle_name")
            if bundle_filter:
                esims = esims.filter(bundle_name__icontains=bundle_filter)

            date_from = request.query_params.get("date_from")
            if date_from:
                esims = esims.filter(assigned_at__gte=date_from)

            date_to = request.query_params.get("date_to")
            if date_to:
                esims = esims.filter(assigned_at__lte=date_to)

            # Paginate results
            paginator = StandardPagination()
            page = paginator.paginate_queryset(esims, request)

            esim_data = []
            for esim in page:
                esim_info = {
                    "id": esim.id,
                    "bundle_name": esim.bundle_name,
                    "status": esim.status,
                    "assigned_at": (
                        esim.assigned_at.isoformat() if esim.assigned_at else None
                    ),
                    "client_name": esim.client.full_name if esim.client else "N/A",
                    "client_email": esim.client.email if esim.client else "N/A",
                    "client_phone": esim.client.phone_number if esim.client else "N/A",
                    "iccid": esim.traveroam_esim_id,
                    "traveroam_order_ref": esim.traveroam_order_reference,
                    "bundle_details": esim.bundle_details or {},
                    "data_used": float(esim.data_used),
                    "data_remaining": float(esim.data_remaining),
                }

                # Get real-time status from TraveRoam for active eSIMs
                if (
                    esim.status in ["provisioned", "assigned", "activated"]
                    and esim.traveroam_esim_id
                ):
                    try:
                        status_result = (
                            ESIMWorkflowService.get_esim_status_from_traveroam(esim)
                        )
                        if status_result.get("success"):
                            esim_info["traveroam_status"] = status_result.get(
                                "status_data", {}
                            )
                    except Exception as e:
                        logger.warning(
                            f"Failed to get TraveRoam status for eSIM {esim.id}: {str(e)}"
                        )

                esim_data.append(esim_info)

            return Response(
                {
                    "success": True,
                    "esims": esim_data,
                    "count": esims.count(),
                    "pagination": {
                        "next": paginator.get_next_link(),
                        "previous": paginator.get_previous_link(),
                        "count": esims.count(),
                    },
                }
            )

        except Exception as e:
            logger.error(f"Error getting eSIMs: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def organization(self, request):
        """Get organization details from TraveRoam"""
        try:
            org_details = ESIMWorkflowService.get_organization_details()
            return Response({"success": True, "organization": org_details})
        except Exception as e:
            logger.error(f"Error getting organization details: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def revenue_analytics(self, request):
        """Get revenue analytics with TraveRoam integration"""
        try:
            reseller = Reseller.objects.get(user=request.user)

            # Get date filters
            date_from = request.query_params.get("date_from")
            date_to = request.query_params.get("date_to")

            # Get orders and payments
            orders = Order.objects.filter(reseller=reseller, order_type="esim")

            if date_from:
                orders = orders.filter(created_at__gte=date_from)
            if date_to:
                orders = orders.filter(created_at__lte=date_to)

            # Calculate revenue metrics
            total_revenue = (
                orders.aggregate(total=models.Sum("total_amount"))["total"] or 0
            )
            total_orders = orders.count()
            completed_orders = orders.filter(status="delivered").count()

            # Get payments
            payments = Payment.objects.filter(order__in=orders)
            successful_payments = payments.filter(status="completed").count()
            failed_payments = payments.filter(status="failed").count()

            # Get bundle revenue breakdown
            bundle_revenue = {}
            for order in orders:
                if order.esim_id:
                    try:
                        esim = ESIM.objects.get(id=order.esim_id)
                        bundle_name = esim.bundle_name or "Unknown"
                        if bundle_name not in bundle_revenue:
                            bundle_revenue[bundle_name] = 0
                        bundle_revenue[bundle_name] += float(order.total_amount)
                    except ESIM.DoesNotExist:
                        pass

            return Response(
                {
                    "success": True,
                    "revenue_analytics": {
                        "total_revenue": float(total_revenue),
                        "total_orders": total_orders,
                        "completed_orders": completed_orders,
                        "completion_rate": (
                            (completed_orders / total_orders * 100)
                            if total_orders > 0
                            else 0
                        ),
                        "successful_payments": successful_payments,
                        "failed_payments": failed_payments,
                        "payment_success_rate": (
                            (
                                successful_payments
                                / (successful_payments + failed_payments)
                                * 100
                            )
                            if (successful_payments + failed_payments) > 0
                            else 0
                        ),
                        "bundle_revenue": bundle_revenue,
                        "average_order_value": (
                            float(total_revenue / total_orders)
                            if total_orders > 0
                            else 0
                        ),
                    },
                }
            )

        except Exception as e:
            logger.error(f"Error getting revenue analytics: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
