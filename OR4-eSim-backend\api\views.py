import logging
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from django.conf import settings
from django.db.models import Avg, Count, Sum
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

# Import models
from accounts.models import User, UserProfile
from api.firebase_storage import firebase_storage
from api.utils import create_error_response, create_success_response
from clients.models import Client
from esim_management.models import ESIM
from orders.models import Order
from payments.models import Payment
from resellers.models import Reseller

logger = logging.getLogger(__name__)


@api_view(["GET"])
def dashboard_view(request):
    """Dashboard API endpoint"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    # Get user role and add debugging
    user_role = getattr(request.user, "role", "unknown")
    user_email = getattr(request.user, "email", "unknown")

    # Log the user information for debugging
    logger.info(
        f"Dashboard access - User: {user_email}, Role: {user_role}, Is Staff: {request.user.is_staff}, Is Superuser: {request.user.is_superuser}"
    )

    if user_role == "admin":
        logger.info(f"User {user_email} is admin, calling admin dashboard")
        data = _get_admin_dashboard()
    elif user_role == "reseller":
        logger.info(f"User {user_email} is reseller, calling reseller dashboard")
        data = _get_reseller_dashboard(request.user)
    else:
        logger.info(
            f"User {user_email} has role '{user_role}', calling client dashboard"
        )
        data = _get_client_dashboard(request.user)

    return Response(
        create_success_response(data=data, message="Dashboard data retrieved")
    )


# Test endpoint for admin dashboard (temporary, for debugging)
@api_view(["GET"])
@permission_classes([AllowAny])
def test_admin_dashboard(request):
    """Test admin dashboard without authentication (for debugging)"""
    logger.info("Test admin dashboard called")
    data = _get_admin_dashboard()
    return Response(
        create_success_response(
            data=data, message="Admin dashboard data retrieved (test)"
        )
    )


def _get_admin_dashboard():
    """
    Get comprehensive admin dashboard data with real database queries.

    Returns:
        dict: Complete dashboard data including metrics, graphs, and activities
    """
    logger = logging.getLogger(__name__)
    try:
        now = timezone.now()
        today = now.date()
        this_month = now.replace(day=1)

        # Get current month name and previous months
        month_names = [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
        ]
        current_month_name = month_names[now.month - 1]

        # Calculate previous 6 months for trends
        monthly_data = []
        for i in range(6):
            month_date = now.replace(day=1) - timedelta(days=30 * i)
            month_name = month_names[month_date.month - 1]
            month_start = month_date.replace(day=1)
            month_end = (month_date.replace(day=1) + timedelta(days=32)).replace(
                day=1
            ) - timedelta(days=1)

            # Get monthly orders and revenue
            monthly_orders = Order.objects.filter(
                created_at__gte=month_start, created_at__lte=month_end
            ).count()

            monthly_revenue = (
                Payment.objects.filter(
                    status="completed",
                    created_at__gte=month_start,
                    created_at__lte=month_end,
                ).aggregate(total=Sum("amount"))["total"]
                or 0
            )

            monthly_data.append(
                {
                    "month": month_name,
                    "orders": monthly_orders,
                    "revenue": float(monthly_revenue),
                    "target": max(monthly_orders, 100),  # Simple target calculation
                    "growth": 0,  # Will calculate below
                }
            )

        # Calculate growth percentages
        for i in range(1, len(monthly_data)):
            if monthly_data[i - 1]["orders"] > 0:
                growth = (
                    (monthly_data[i]["orders"] - monthly_data[i - 1]["orders"])
                    / monthly_data[i - 1]["orders"]
                ) * 100
                monthly_data[i]["growth"] = round(growth, 1)

        # Reverse to show oldest to newest
        monthly_data.reverse()

        # Daily data for current month
        daily_data = []
        for i in range(30):
            date = today - timedelta(days=i)
            daily_orders = Order.objects.filter(created_at__date=date).count()
            daily_data.append(
                {
                    "day": f"Day {30-i}",
                    "orders": daily_orders,
                    "target": 80,  # Daily target
                }
            )
        daily_data.reverse()

        # Calculate summary metrics
        total_orders = sum(item["orders"] for item in monthly_data)
        total_target = sum(item["target"] for item in monthly_data)
        achievement_rate = (
            (total_orders / total_target * 100) if total_target > 0 else 0
        )
        average_daily_orders = total_orders // 30
        best_performance = (
            max(item["orders"] for item in monthly_data) if monthly_data else 0
        )

        # Revenue analytics
        revenue_data = []
        for i in range(6):
            month_date = now.replace(day=1) - timedelta(days=30 * i)
            month_name = month_names[month_date.month - 1]
            month_start = month_date.replace(day=1)
            month_end = (month_date.replace(day=1) + timedelta(days=32)).replace(
                day=1
            ) - timedelta(days=1)

            monthly_revenue = (
                Payment.objects.filter(
                    status="completed",
                    created_at__gte=month_start,
                    created_at__lte=month_end,
                ).aggregate(total=Sum("amount"))["total"]
                or 0
            )

            monthly_orders_count = Order.objects.filter(
                created_at__gte=month_start, created_at__lte=month_end
            ).count()

            # Calculate growth (simple calculation)
            growth = 0
            if i > 0:
                prev_month_start = month_start - timedelta(days=30)
                prev_month_end = month_start - timedelta(days=1)
                prev_revenue = (
                    Payment.objects.filter(
                        status="completed",
                        created_at__gte=prev_month_start,
                        created_at__lte=prev_month_end,
                    ).aggregate(total=Sum("amount"))["total"]
                    or 0
                )
                if prev_revenue > 0:
                    growth = ((monthly_revenue - prev_revenue) / prev_revenue) * 100

            revenue_data.append(
                {
                    "period": month_name,
                    "revenue": float(monthly_revenue),
                    "target": float(
                        monthly_revenue * Decimal("1.1")
                    ),  # 10% above actual
                    "growth": round(growth, 1),
                    "orders": monthly_orders_count,
                }
            )

        revenue_data.reverse()

        # Calculate revenue summary
        total_revenue = sum(item["revenue"] for item in revenue_data)
        total_revenue_target = sum(item["target"] for item in revenue_data)
        revenue_achievement_rate = (
            (total_revenue / total_revenue_target * 100)
            if total_revenue_target > 0
            else 0
        )
        average_growth = (
            sum(item["growth"] for item in revenue_data) / len(revenue_data)
            if revenue_data
            else 0
        )
        best_month = (
            max(item["revenue"] for item in revenue_data) if revenue_data else 0
        )

        # Sales trends for last 30 days
        sales_trends = []
        for i in range(4):  # 4 weeks
            week_start = today - timedelta(days=7 * i)
            week_end = week_start + timedelta(days=6)

            week_orders = Order.objects.filter(
                created_at__gte=week_start, created_at__lte=week_end
            ).count()

            week_revenue = (
                Payment.objects.filter(
                    status="completed",
                    created_at__gte=week_start,
                    created_at__lte=week_end,
                ).aggregate(total=Sum("amount"))["total"]
                or 0
            )

            sales_trends.append(
                {
                    "name": f"Week {4-i}",
                    "sales": float(week_revenue),
                    "orders": week_orders,
                }
            )

        sales_trends.reverse()

        # Calculate overall growth percentage
        if len(sales_trends) >= 2:
            first_week = sales_trends[0]["sales"]
            last_week = sales_trends[-1]["sales"]
            growth_percentage = (
                ((last_week - first_week) / first_week * 100) if first_week > 0 else 0
            )
        else:
            growth_percentage = 0

        # Top resellers with colors
        top_resellers_data = []
        try:
            top_resellers_raw = (
                Reseller.objects.filter(is_suspended=False)
                .annotate(
                    total_orders=Count("orders"),
                    total_revenue=Sum("orders__total_amount"),
                )
                .values(
                    "id",
                    "user__first_name",
                    "user__last_name",
                    "user__email",
                    "total_orders",
                    "total_revenue",
                )
                .order_by("-total_revenue")[:6]
            )

            colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#6b7280"]

            for i, reseller_data in enumerate(top_resellers_raw):
                total_orders = reseller_data.get("total_orders", 0) or 0
                total_revenue = reseller_data.get("total_revenue", 0) or 0

                # Calculate percentage of total sales
                total_sales = sum(
                    float(r.get("total_revenue", 0) or 0) for r in top_resellers_raw
                )
                percentage = (
                    (float(total_revenue) / total_sales * 100) if total_sales > 0 else 0
                )

                top_resellers_data.append(
                    {
                        "name": f"{reseller_data['user__first_name']} {reseller_data['user__last_name']}",
                        "sales": float(total_revenue),
                        "orders": total_orders,
                        "percentage": round(percentage, 1),
                        "color": colors[i] if i < len(colors) else colors[-1],
                    }
                )

            # Add "Others" category if we have more than 5 resellers
            if len(top_resellers_raw) >= 5:
                total_sales = sum(
                    float(r.get("total_revenue", 0) or 0) for r in top_resellers_raw
                )
                others_sales = float(total_sales * 0.1)  # 10% of total
                others_orders = (
                    sum(r.get("total_orders", 0) or 0 for r in top_resellers_raw) // 10
                )
                others_percentage = 10.0

                top_resellers_data.append(
                    {
                        "name": "Others",
                        "sales": others_sales,
                        "orders": others_orders,
                        "percentage": others_percentage,
                        "color": "#6b7280",
                    }
                )

        except Exception as e:
            logger.error(f"Error in top resellers query: {str(e)}")
            top_resellers_data = []

        # Calculate total sales and orders for resellers
        total_sales = sum(item["sales"] for item in top_resellers_data)
        total_orders_count = sum(item["orders"] for item in top_resellers_data)

        # Latest activities
        latest_activities = []

        # Recent user registrations
        try:
            recent_users = User.objects.filter(
                created_at__gte=now - timedelta(days=7)
            ).order_by("-created_at")[:3]

            for user in recent_users:
                latest_activities.append(
                    {
                        "id": f"act_{user.id:03d}",
                        "action": "New user registration",
                        "user": user.email,
                        "time": user.created_at.isoformat(),
                        "type": "user",
                    }
                )
        except Exception:
            pass

        # Recent orders
        try:
            recent_orders = Order.objects.select_related(
                "public_user__user", "client__user", "reseller__user"
            ).order_by("-created_at")[:3]

            for order in recent_orders:
                customer_name = "Unknown"
                if order.public_user:
                    customer_name = order.public_user.user.email
                elif order.client:
                    customer_name = order.client.full_name

                reseller_name = "Direct"
                if order.reseller:
                    reseller_name = f"{order.reseller.user.first_name} {order.reseller.user.last_name}"

                latest_activities.append(
                    {
                        "id": f"act_{order.id:03d}",
                        "action": "SIM order placed",
                        "user": reseller_name,
                        "time": order.created_at.isoformat(),
                        "type": "order",
                    }
                )
        except Exception:
            pass

        # Recent payments
        try:
            recent_payments = Payment.objects.filter(
                status="completed", created_at__gte=now - timedelta(days=7)
            ).order_by("-created_at")[:2]

            for payment in recent_payments:
                latest_activities.append(
                    {
                        "id": f"act_{payment.id:03d}",
                        "action": "Payment received",
                        "user": f"Order {payment.order.order_number}",
                        "time": payment.created_at.isoformat(),
                        "type": "payment",
                    }
                )
        except Exception:
            pass

        # Recent reseller approvals
        try:
            recent_approvals = Reseller.objects.filter(
                created_at__gte=now - timedelta(days=7)
            ).order_by("-created_at")[:2]

            for reseller in recent_approvals:
                latest_activities.append(
                    {
                        "id": f"act_{reseller.id:03d}",
                        "action": "New reseller approved",
                        "user": f"{reseller.user.first_name} {reseller.user.last_name}",
                        "time": reseller.created_at.isoformat(),
                        "type": "reseller",
                    }
                )
        except Exception:
            pass

        # Sort activities by time (newest first) and limit to 10
        latest_activities.sort(key=lambda x: x["time"], reverse=True)
        latest_activities = latest_activities[:10]

        # Get current metrics
        total_users = User.objects.filter(role="public_user", is_active=True).count()
        total_resellers = Reseller.objects.filter(is_suspended=False).count()
        # Calculate total clients under resellers
        total_reseller_clients = Client.objects.filter(
            client_type=Client.ClientType.RESELLER_CLIENT, reseller__is_suspended=False
        ).count()
        daily_sim_orders = Order.objects.filter(created_at__date=today).count()
        revenue_generated = (
            Payment.objects.filter(status="completed").aggregate(total=Sum("amount"))[
                "total"
            ]
            or 0
        )

        # Calculate growth rates (simple month-over-month)
        user_growth = 0
        reseller_growth = 0
        order_growth = 0
        revenue_growth = 0

        try:
            last_month = this_month - timedelta(days=30)
            last_month_users = User.objects.filter(
                role="public_user", is_active=True, created_at__lt=this_month
            ).count()
            if last_month_users > 0:
                user_growth = (
                    (total_users - last_month_users) / last_month_users
                ) * 100

            last_month_resellers = Reseller.objects.filter(
                is_suspended=False, created_at__lt=this_month
            ).count()
            if last_month_resellers > 0:
                reseller_growth = (
                    (total_resellers - last_month_resellers) / last_month_resellers
                ) * 100

            last_month_orders = Order.objects.filter(
                created_at__gte=last_month, created_at__lt=this_month
            ).count()
            current_month_orders = Order.objects.filter(
                created_at__gte=this_month
            ).count()
            if last_month_orders > 0:
                order_growth = (
                    (current_month_orders - last_month_orders) / last_month_orders
                ) * 100

            last_month_revenue = (
                Payment.objects.filter(
                    status="completed",
                    created_at__gte=last_month,
                    created_at__lt=this_month,
                ).aggregate(total=Sum("amount"))["total"]
                or 0
            )
            current_month_revenue = (
                Payment.objects.filter(
                    status="completed", created_at__gte=this_month
                ).aggregate(total=Sum("amount"))["total"]
                or 0
            )
            if last_month_revenue > 0:
                revenue_growth = (
                    (current_month_revenue - last_month_revenue) / last_month_revenue
                ) * 100

        except Exception:
            pass

        # Compile final dashboard data
        dashboard_data = {
            "metrics": {
                "totalUsers": total_users,
                "totalResellers": total_resellers,
                "totalResellerClients": total_reseller_clients,
                "dailySimOrders": daily_sim_orders,
                "revenueGenerated": float(revenue_generated),
                "userGrowth": round(user_growth, 1),
                "resellerGrowth": round(reseller_growth, 1),
                "orderGrowth": round(order_growth, 1),
                "revenueGrowth": round(revenue_growth, 1),
            },
            "salesTrends": {
                "period": "30days",
                "trends": sales_trends,
                "growthPercentage": round(growth_percentage, 1),
            },
            "topResellers": {
                "resellers": top_resellers_data,
                "totalSales": total_sales,
                "totalOrders": total_orders_count,
            },
            "ordersOverview": {
                "monthly": monthly_data,
                "daily": daily_data,
                "summary": {
                    "totalOrders": total_orders,
                    "totalTarget": total_target,
                    "achievementRate": round(achievement_rate, 1),
                    "averageDailyOrders": average_daily_orders,
                    "bestPerformance": best_performance,
                },
            },
            "revenueAnalytics": {
                "monthly": revenue_data,
                "summary": {
                    "totalRevenue": total_revenue,
                    "totalTarget": total_revenue_target,
                    "achievementRate": round(revenue_achievement_rate, 1),
                    "averageGrowth": round(average_growth, 1),
                    "bestMonth": best_month,
                },
            },
            "latestActivities": latest_activities,
            "lastUpdated": now.isoformat(),
        }

        return dashboard_data

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Admin dashboard error: {str(e)}", exc_info=True)

        # Return a more detailed error in debug mode
        error_details = str(e) if settings.DEBUG else "Internal server error"

        return {
            "error": "Failed to load dashboard data",
            "details": error_details,
            "error_type": type(e).__name__,
        }


def _get_reseller_dashboard(user):
    """Get reseller dashboard data"""
    try:
        # TODO: Implement actual reseller dashboard logic
        # This would include reseller-specific statistics

        dashboard_data = {
            "total_clients": 0,
            "total_orders": 0,
            "total_revenue": 0,
            "commission_earned": 0,
            "recent_orders": [],
            "top_clients": [],
        }

        return dashboard_data
    except Exception as e:
        return {"error": str(e)}


def _get_client_dashboard(user):
    """Get client dashboard data"""
    try:
        # TODO: Implement actual client dashboard logic
        # This would include client-specific statistics

        dashboard_data = {
            "total_orders": 0,
            "active_esims": 0,
            "total_spent": 0,
            "recent_orders": [],
            "esim_status": [],
        }

        return dashboard_data
    except Exception as e:
        return {"error": str(e)}


@api_view(["POST"])
def upload_profile_image(request):
    """Upload profile image to Firebase Storage"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        # Check if image file is provided
        if "image" not in request.FILES:
            return Response(
                create_error_response("Image file is required"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        image_file = request.FILES["image"]

        # Validate file type
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if image_file.content_type not in allowed_types:
            return Response(
                create_error_response(
                    "Invalid file type. Only JPEG, PNG, and GIF are allowed"
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate file size (max 5MB)
        if image_file.size > 5 * 1024 * 1024:
            return Response(
                create_error_response("File size too large. Maximum size is 5MB"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Upload to Firebase Storage
        user_full_name = f"{request.user.first_name} {request.user.last_name}".strip()
        public_url, file_path = firebase_storage.upload_profile_image(
            image_file, request.user.id, user_full_name
        )

        # Update user profile with image URL
        user_profile, created = UserProfile.objects.get_or_create(user=request.user)
        user_profile.profile_image_url = public_url
        user_profile.save()

        return Response(
            create_success_response(
                data={
                    "image_url": public_url,
                    "file_path": file_path,
                    "user_id": request.user.id,
                },
                message="Profile image uploaded successfully",
            )
        )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Profile image upload error: {str(e)}", exc_info=True)
        return Response(
            create_error_response(f"Failed to upload image: {str(e)}"),
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["POST"])
def upload_document_image(request):
    """Upload document image to Firebase Storage"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        # Check if image file is provided
        if "image" not in request.FILES:
            return Response(
                create_error_response("Image file is required"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if document type is provided
        document_type = request.data.get("document_type")
        if not document_type:
            return Response(
                create_error_response("Document type is required"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        image_file = request.FILES["image"]

        # Validate file type
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if image_file.content_type not in allowed_types:
            return Response(
                create_error_response(
                    "Invalid file type. Only JPEG, PNG, and GIF are allowed"
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate file size (max 5MB)
        if image_file.size > 5 * 1024 * 1024:
            return Response(
                create_error_response("File size too large. Maximum size is 5MB"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Upload to Firebase Storage
        user_full_name = f"{request.user.first_name} {request.user.last_name}".strip()
        public_url, file_path = firebase_storage.upload_document_image(
            image_file, document_type, request.user.id, user_full_name
        )

        return Response(
            create_success_response(
                data={
                    "image_url": public_url,
                    "file_path": file_path,
                    "document_type": document_type,
                    "user_id": request.user.id,
                },
                message="Document image uploaded successfully",
            )
        )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Document image upload error: {str(e)}", exc_info=True)
        return Response(
            create_error_response(f"Failed to upload document: {str(e)}"),
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["DELETE"])
def delete_profile_image(request):
    """Delete profile image"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        # TODO: Implement actual image deletion logic
        # This would use Firebase Storage

        return Response(
            create_success_response(message="Profile image deleted successfully")
        )
    except Exception as e:
        return Response(
            create_error_response(f"Failed to delete image: {str(e)}"),
            status=status.HTTP_400_BAD_REQUEST,
        )


# Simple test view for Swagger
@api_view(["GET"])
@permission_classes([AllowAny])
def test_view(request):
    """Test view for Swagger"""
    return Response(
        create_success_response(
            data={"message": "Hello World"}, message="Test endpoint working"
        )
    )


# Test comment
# Test comment
