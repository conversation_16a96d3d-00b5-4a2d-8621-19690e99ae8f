from decimal import Decimal

from django.conf import settings
from django.db import models
from django.utils import timezone
from simple_history.models import HistoricalRecords


class Payment(models.Model):
    """
    Payment model for order payments with admin controls and Stripe integration
    """

    class PaymentStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        PROCESSING = "processing", "Processing"
        COMPLETED = "completed", "Completed"
        FAILED = "failed", "Failed"
        CANCELLED = "cancelled", "Cancelled"
        REFUNDED = "refunded", "Refunded"
        MANUAL_APPROVAL = "manual_approval", "Manual Approval Required"
        REJECTED = "rejected", "Rejected"

    class PaymentType(models.TextChoices):
        STRIPE = "stripe", "Stripe"
        MANUAL = "manual", "Manual Payment"
        BANK_TRANSFER = "bank_transfer", "Bank Transfer"
        CASH = "cash", "Cash"

    # Payment details
    order = models.ForeignKey(
        "orders.Order",
        on_delete=models.CASCADE,
        related_name="payments",
        null=True,
        blank=True,
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default="USD")
    payment_method = models.CharField(max_length=50, default="stripe")
    payment_type = models.CharField(
        max_length=20, choices=PaymentType.choices, default=PaymentType.STRIPE
    )
    status = models.CharField(
        max_length=20, choices=PaymentStatus.choices, default=PaymentStatus.PENDING
    )

    # Transaction details
    transaction_id = models.CharField(
        max_length=100, unique=True, blank=True, null=True
    )
    gateway_transaction_id = models.CharField(max_length=100, blank=True, null=True)
    gateway_response = models.JSONField(blank=True, null=True)

    # Stripe-specific fields for TraveRoam bundles
    stripe_payment_intent_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_checkout_session_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True)
    client_secret = models.CharField(max_length=200, blank=True, null=True)

    # Bundle purchase details
    bundle_name = models.CharField(max_length=100, blank=True, null=True)
    bundle_details = models.JSONField(blank=True, null=True)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    reseller_markup_percent = models.DecimalField(
        max_digits=5, decimal_places=2, default=0.00
    )
    reseller_markup_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )

    # Manual payment details
    manual_payment_proof = models.FileField(
        upload_to="payment_proofs/", blank=True, null=True
    )
    manual_payment_notes = models.TextField(blank=True, null=True)
    manual_approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="approved_manual_payments",
    )
    manual_approved_at = models.DateTimeField(null=True, blank=True)
    manual_rejection_reason = models.TextField(blank=True, null=True)

    # Refund information
    refund_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    refund_reason = models.TextField(blank=True, null=True)
    refund_approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="approved_refunds",
    )
    refund_approved_at = models.DateTimeField(null=True, blank=True)

    # Invoice information
    invoice_number = models.CharField(max_length=50, blank=True, null=True)
    invoice_generated = models.BooleanField(default=False)
    invoice_generated_at = models.DateTimeField(null=True, blank=True)
    invoice_file = models.FileField(upload_to="invoices/", blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "payments"
        ordering = ["-created_at"]

    def __str__(self):
        return (
            f"Payment {self.transaction_id or self.id} - {self.amount} {self.currency}"
        )

    @property
    def is_refunded(self):
        return self.status == "refunded"

    @property
    def net_amount(self):
        """Calculate net amount after refunds"""
        from decimal import Decimal

        refund_amount = (
            Decimal(str(self.refund_amount)) if self.refund_amount else Decimal("0.00")
        )
        return self.amount - refund_amount

    @property
    def customer_name(self):
        """Get customer name from order"""
        return self.order.client.full_name if self.order.client else "Unknown"

    @property
    def is_bundle_purchase(self):
        """Check if this is a bundle purchase"""
        return bool(self.bundle_name)

    @property
    def is_stripe_payment(self):
        """Check if this is a Stripe payment"""
        return self.payment_type == self.PaymentType.STRIPE

    @property
    def total_amount_with_markup(self):
        """Calculate total amount with markup"""
        return self.base_price + self.reseller_markup_amount

    @property
    def reseller_name(self):
        """Get reseller name from order"""
        return (
            self.order.reseller.user.get_full_name()
            if self.order and self.order.reseller
            else "Unknown"
        )

    @property
    def requires_manual_approval(self):
        """Check if payment requires manual approval"""
        return self.status == self.PaymentStatus.MANUAL_APPROVAL

    def approve_manual_payment(self, approved_by, notes=None):
        """Approve a manual payment"""
        self.status = self.PaymentStatus.COMPLETED
        self.manual_approved_by = approved_by
        self.manual_approved_at = timezone.now()
        if notes:
            self.manual_payment_notes = notes
        self.processed_at = timezone.now()
        self.completed_at = timezone.now()
        self.save()

    def reject_manual_payment(self, rejected_by, reason):
        """Reject a manual payment"""
        self.status = self.PaymentStatus.REJECTED
        self.manual_rejection_reason = reason
        self.save()

    def generate_invoice_number(self):
        """Generate a unique invoice number"""
        if not self.invoice_number:
            timestamp = timezone.now().strftime("%Y%m%d")
            count = (
                Payment.objects.filter(
                    invoice_number__startswith=f"INV-{timestamp}"
                ).count()
                + 1
            )
            self.invoice_number = f"INV-{timestamp}-{count:04d}"
            self.save()
        return self.invoice_number

    # Stripe-specific methods
    @property
    def is_stripe_payment(self):
        """Check if this is a Stripe payment"""
        return self.payment_type == self.PaymentType.STRIPE

    @property
    def is_bundle_purchase(self):
        """Check if this is a bundle purchase payment"""
        return bool(self.bundle_name)

    @property
    def total_amount_with_markup(self):
        """Calculate total amount including reseller markup"""
        if self.is_bundle_purchase:
            return self.base_price + self.reseller_markup_amount
        return self.amount

    def mark_stripe_succeeded(self):
        """Mark Stripe payment as succeeded"""
        self.status = self.PaymentStatus.COMPLETED
        self.completed_at = timezone.now()
        self.processed_at = timezone.now()
        self.save()

    def mark_stripe_failed(self, error_message: str = None):
        """Mark Stripe payment as failed"""
        self.status = self.PaymentStatus.FAILED
        if error_message:
            self.gateway_response = {"error": error_message}
        self.save()

    def set_stripe_metadata(
        self,
        payment_intent_id: str = None,
        checkout_session_id: str = None,
        customer_id: str = None,
        client_secret: str = None,
    ):
        """Set Stripe-specific metadata"""
        if payment_intent_id:
            self.stripe_payment_intent_id = payment_intent_id
        if checkout_session_id:
            self.stripe_checkout_session_id = checkout_session_id
        if customer_id:
            self.stripe_customer_id = customer_id
        if client_secret:
            self.client_secret = client_secret
        self.save()


class StripeWebhook(models.Model):
    """
    Track Stripe webhook events for payment processing
    """

    class WebhookEventType(models.TextChoices):
        PAYMENT_INTENT_SUCCEEDED = (
            "payment_intent.succeeded",
            "Payment Intent Succeeded",
        )
        PAYMENT_INTENT_FAILED = "payment_intent.payment_failed", "Payment Intent Failed"
        CHECKOUT_SESSION_COMPLETED = (
            "checkout.session.completed",
            "Checkout Session Completed",
        )
        CHECKOUT_SESSION_EXPIRED = (
            "checkout.session.expired",
            "Checkout Session Expired",
        )
        INVOICE_PAYMENT_SUCCEEDED = (
            "invoice.payment_succeeded",
            "Invoice Payment Succeeded",
        )
        INVOICE_PAYMENT_FAILED = "invoice.payment_failed", "Invoice Payment Failed"
        CUSTOMER_CREATED = "customer.created", "Customer Created"
        CHARGE_DISPUTE_CREATED = "charge.dispute.created", "Charge Dispute Created"

    # Webhook details
    stripe_event_id = models.CharField(max_length=100, unique=True)
    event_type = models.CharField(max_length=50, choices=WebhookEventType.choices)

    # Related objects
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name="stripe_webhooks",
        null=True,
        blank=True,
    )

    # Event data
    event_data = models.JSONField()
    processed = models.BooleanField(default=False)
    processing_attempts = models.PositiveIntegerField(default=0)
    last_processing_error = models.TextField(blank=True, null=True)

    # Timestamps
    stripe_created_at = models.DateTimeField()
    received_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "stripe_webhooks"
        ordering = ["-received_at"]
        indexes = [
            models.Index(fields=["stripe_event_id"]),
            models.Index(fields=["event_type"]),
            models.Index(fields=["processed"]),
        ]

    def __str__(self):
        return f"Stripe Webhook {self.stripe_event_id} - {self.event_type}"

    def mark_processed(self):
        """Mark webhook as successfully processed"""
        self.processed = True
        self.processed_at = timezone.now()
        self.save()

    def increment_processing_attempts(self, error_message: str = None):
        """Increment processing attempts and record error"""
        self.processing_attempts += 1
        if error_message:
            self.last_processing_error = error_message
        self.save()
