#!/bin/bash

echo "🔧 Setting up linting and formatting tools..."

# Install pre-commit hooks
echo "📦 Installing pre-commit hooks..."
pre-commit install

# Install additional flake8 plugins
echo "🔍 Installing additional linting tools..."
pip install flake8-docstrings

# Create .flake8 config file
echo "⚙️ Creating flake8 configuration..."
cat > .flake8 << EOF
[flake8]
max-line-length = 88
extend-ignore = E203,W503,E501
exclude =
    .git,
    __pycache__,
    build,
    dist,
    migrations,
    venv,
    .venv,
    *.egg-info,
    .pytest_cache
EOF

# Format existing code
echo "🎨 Formatting existing code with Black..."
black .

echo "📝 Sorting imports with isort..."
isort .

echo "✅ Linting setup complete!"
echo ""
echo "📋 Usage:"
echo "  - Format code: black ."
echo "  - Sort imports: isort ."
echo "  - Check linting: flake8"
echo "  - Run all checks: pre-commit run --all-files"
echo ""
echo "🚀 Pre-commit hooks are now active!"
echo "   Every commit will automatically format and lint your code."
